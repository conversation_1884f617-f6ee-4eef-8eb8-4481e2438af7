<?php

use PhpOffice\PhpSpreadsheet\IOFactory;

$xlsm_file_path = '/var/www/html/mtl/reglementation.xlsm';

try {
  $spreadsheet = IOFactory::load($xlsm_file_path);
  
  echo "=== ANALYSE DE TOUS LES ONGLETS ===\n";
  
  foreach ($spreadsheet->getSheetNames() as $sheetName) {
    echo "\n--- ONGLET: $sheetName ---\n";
    
    $worksheet = $spreadsheet->getSheetByName($sheetName);
    $highestRow = $worksheet->getHighestRow();
    
    $count_with_titles = 0;
    for ($row = 20; $row <= min($highestRow, 200); $row++) {
      $titre_fr = trim($worksheet->getCell('F' . $row)->getValue() ?? '');
      if (!empty($titre_fr)) {
        $count_with_titles++;
        if ($count_with_titles <= 3) {
          echo "  Exemple ligne $row: " . substr($titre_fr, 0, 80) . "...\n";
        }
      }
    }
    echo "Total lignes avec titre français: $count_with_titles\n";
    echo "Total lignes dans l'onglet: $highestRow\n";
  }
  
} catch (Exception $e) {
  echo "Erreur: " . $e->getMessage() . "\n";
}