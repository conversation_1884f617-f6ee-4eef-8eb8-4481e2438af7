<?php

use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation des réglementations Texte Général depuis un fichier CSV
 * Usage: drush php:script import_reglementation_texte_general.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/mtl/Canvas Réglementation - Texte Général.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "Début de l'importation des réglementations Texte Général...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 */
function getTaxonomyTermByName($name, $vocabulary) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    return reset($terms);
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $name,
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour parser la date du CSV
 */
function parseDate($date_string) {
  if (empty($date_string)) {
    return null;
  }
  
  // Format attendu: dd/mm/yyyy
  $date_parts = explode('/', $date_string);
  if (count($date_parts) == 3) {
    $day = str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
    $month = str_pad($date_parts[1], 2, '0', STR_PAD_LEFT);
    $year = $date_parts[2];
    return "$year-$month-$day";
  }
  
  return null;
}

/**
 * Fonction pour créer ou récupérer un fichier
 */
function createOrGetFile($filename, $pdf_directory = 'web/modules/custom/import_reglementation/pdf/') {
  if (empty($filename)) {
    return null;
  }
  
  $file_path = $pdf_directory . $filename;
  
  // Vérifier si le fichier existe physiquement
  if (!file_exists($file_path)) {
    // Ignorer silencieusement les fichiers manquants
    return null;
  }
  
  // Rechercher le fichier existant dans Drupal
  $files = \Drupal::entityTypeManager()
    ->getStorage('file')
    ->loadByProperties(['filename' => $filename]);
  
  if ($files) {
    return reset($files);
  }
  
  // Créer une nouvelle entité fichier
  $file_system = \Drupal::service('file_system');
  $destination = 'public://reglementation/pdf/' . $filename;
  
  // Créer le répertoire de destination s'il n'existe pas
  $directory = dirname($destination);
  $file_system->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
  
  // Copier le fichier
  $uri = $file_system->copy($file_path, $destination, FileSystemInterface::EXISTS_REPLACE);
  
  if ($uri) {
    $file = File::create([
      'filename' => $filename,
      'uri' => $uri,
      'status' => 1,
    ]);
    $file->save();
    echo "Fichier créé: $filename\n";
    return $file;
  }
  
  return null;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      return Node::load(reset($nids));
    }
  }
  
  return null;
}

// Récupérer les termes existants
$texte_general_sector = getTaxonomyTermByName('Texte géneral', 'modes_de_transport');
$texte_general_domaine = getTaxonomyTermByName('Texte général', 'domaines_d_activites');

if (!$texte_general_sector) {
  echo "Erreur: Le terme 'Texte géneral' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

if (!$texte_general_domaine) {
  echo "Erreur: Le terme 'Texte général' n'existe pas dans le vocabulaire 'domaines_d_activites'\n";
  return;
}

// Lire le fichier CSV complet pour traitement personnalisé
$csv_content = file_get_contents($csv_file_path);
if (!$csv_content) {
  echo "Erreur: Impossible de lire le fichier CSV\n";
  return;
}

// Diviser en lignes et nettoyer les retours à la ligne dans les cellules
$lines = explode("\n", $csv_content);

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-16)
$data_lines = array_slice($lines, 16);

// Fonction pour parser une ligne CSV malformée
function parseCSVLine($line) {
  // Utiliser str_getcsv avec gestion des retours à la ligne
  $data = str_getcsv($line, ',', '"');
  
  // Nettoyer les retours à la ligne dans chaque cellule
  foreach ($data as &$cell) {
    $cell = trim(str_replace(["\n", "\r"], ' ', $cell));
  }
  
  return $data;
}

// Reconstruire les lignes brisées par les retours à la ligne
$reconstructed_lines = [];
$current_line = '';
$in_quoted_cell = false;

foreach ($data_lines as $line) {
  $line = trim($line);
  
  if (empty($line)) {
    continue;
  }
  
  // Ajouter à la ligne courante
  $current_line .= ($current_line ? ' ' : '') . $line;
  
  // Compter les guillemets pour détecter les cellules incomplètes
  $quote_count = substr_count($current_line, '"');
  
  // Si le nombre de guillemets est pair, la ligne est complète
  if ($quote_count % 2 == 0) {
    $reconstructed_lines[] = $current_line;
    $current_line = '';
  }
}

// Ajouter la dernière ligne si elle existe
if (!empty($current_line)) {
  $reconstructed_lines[] = $current_line;
}

$imported = 0;
$updated = 0;
$errors = 0;
$line_number = 16; // Commencer après les en-têtes

// Traiter chaque ligne de données
foreach ($reconstructed_lines as $line) {
  $line_number++;
  
  if (empty(trim($line))) {
    continue;
  }
  
  $data = parseCSVLine($line);
  
  // Vérifier que la ligne contient toutes les colonnes attendues
  if (count($data) < 11) {
    echo "Ligne $line_number ignorée (colonnes incomplètes: " . count($data) . ")\n";
    continue;
  }
  
  // Vérifier que la ligne contient des données valides
  if (empty($data[5]) || empty($data[6])) { // Intitulé FR et AR
    echo "Ligne $line_number ignorée (colonnes 5/6 vides)\n";
    continue;
  }
  
  try {
    // Extraire les données
    $type_fr = trim($data[0]);
    $type_ar = trim($data[1]);
    $domaine_fr = trim($data[2]);
    $domaine_ar = trim($data[3]);
    $numero = trim($data[4]);
    $titre_fr = trim($data[5]);
    // Nettoyer les retours à la ligne dans le titre arabe
    $titre_ar = trim(str_replace(["\n", "\r"], ' ', $data[6]));
    $date_publication = trim($data[7]);
    $pdf_fr = trim($data[8]);
    $pdf_ar = trim($data[9]);
    $remarques = isset($data[10]) ? trim($data[10]) : '';
    
    // Récupérer ou créer le domaine d'activité depuis les données CSV
    $domaine_term = null;
    if (!empty($domaine_fr)) {
      $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites');
    }
    
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, $numero);
    
    // Mapper les types français vers les termes anglais existants
    $type_mapping = [
      'Dahir' => 'Dahir',
      'Loi organique' => 'Law', 
      'Loi' => 'Law',
      'DECRET ROYAL' => 'Decree',
      'Décret royal' => 'Decree',
      'Décret' => 'Decree',
      'DECRET' => 'Decree',
      'Arrêté royal' => 'Arrêté',
      'Arrêté' => 'Arrêté',
      'Circulaire' => 'Circulaire',
      'Cahier des charges' => 'Cahier des charges',
    ];
    
    $mapped_type = isset($type_mapping[$type_fr]) ? $type_mapping[$type_fr] : null;
    
    if (!$mapped_type) {
      echo "Attention: Type '$type_fr' non mappé, ignoré\n";
      continue;
    }
    
    // Récupérer ou créer le terme de type de loi
    $type_term = getTaxonomyTermByName($mapped_type, 'type');
    
    // Parser la date
    $date_formatted = parseDate($date_publication);
    
    // Gérer les fichiers PDF
    $pdf_fr_file = createOrGetFile($pdf_fr);
    $pdf_ar_file = createOrGetFile($pdf_ar);
    
    // Gérer le titre long (limite de 255 caractères pour le champ title)
    $title_for_node = $titre_fr;
    $long_title = null;
    
    if (mb_strlen($titre_fr, 'UTF-8') > 255) {
      $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
      $long_title = $titre_fr;
      echo "Titre tronqué pour: $title_for_node\n";
    }
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'reglementation',
      'title' => $title_for_node,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $texte_general_sector->id()],
      'field_domaine_d_activite' => ['target_id' => $domaine_term ? $domaine_term->id() : $texte_general_domaine->id()],
      'status' => 1,
      'uid' => 1,
    ];
    
    // Ajouter le titre long si nécessaire
    if ($long_title) {
      $node_data['field_titre_long'] = ['value' => $long_title];
    }
    
    // Ajouter la date si disponible
    if ($date_formatted) {
      $node_data['field_date'] = ['value' => $date_formatted];
    }
    
    // Ajouter le fichier PDF (prioriser le français, sinon l'arabe)
    if ($pdf_fr_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_fr_file->id()];
    } elseif ($pdf_ar_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_ar_file->id()];
    }
    
    if ($existing_node) {
      // Mettre à jour le nœud existant
      foreach ($node_data as $field => $value) {
        if ($field !== 'type') {
          $existing_node->set($field, $value);
        }
      }
      $existing_node->save();
      
      // Ajouter/mettre à jour la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($title_ar_for_node);
          if (mb_strlen($titre_ar, 'UTF-8') > 255 && $ar_translation->hasField('field_titre_long')) {
            $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
          }
          $ar_translation->save();
        } else {
          $ar_data = ['title' => $title_ar_for_node];
          if (mb_strlen($titre_ar, 'UTF-8') > 255) {
            $ar_data['field_titre_long'] = ['value' => $titre_ar];
          }
          $existing_node->addTranslation('ar', $ar_data);
          $existing_node->save();
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer un nouveau nœud
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long pour les nouveaux nœuds
        $title_ar_for_node = $titre_ar;
        $ar_data = ['title' => $title_ar_for_node];
        
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
          $ar_data['title'] = $title_ar_for_node;
          $ar_data['field_titre_long'] = ['value' => $titre_ar];
        }
        
        $node->addTranslation('ar', $ar_data);
        $node->save();
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";