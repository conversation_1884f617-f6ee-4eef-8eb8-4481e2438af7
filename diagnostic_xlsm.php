<?php

use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Script de diagnostic pour analyser pourquoi certaines lignes sont ignorées
 * Usage: drush php:script diagnostic_xlsm.php
 */

$xlsm_file_path = '/var/www/html/mtl/reglementation.xlsm';

if (!file_exists($xlsm_file_path)) {
  echo "Erreur: Le fichier XLSM n'existe pas: $xlsm_file_path\n";
  return;
}

try {
  $spreadsheet = IOFactory::load($xlsm_file_path);
  $worksheet = $spreadsheet->getSheetByName('Transport routier');
  
  if (!$worksheet) {
    echo "Erreur: Onglet 'Transport routier' non trouvé\n";
    return;
  }
  
  $highestRow = $worksheet->getHighestRow();
  
  echo "=== DIAGNOSTIC DES LIGNES IGNORÉES ===\n";
  echo "Analyse de l'onglet 'Transport routier' (lignes 20 à $highestRow)\n\n";
  
  $type_mapping = [
    'Dahir' => 'Dahir',
    'Loi organique' => 'Loi',
    'Loi' => 'Loi',
    'DECRET ROYAL' => 'Décret',
    'Décret royal' => 'Décret', 
    'Décret' => 'Décret',
    'DECRET' => 'Décret',
    'Arrêté royal' => 'Arrêté',
    'Arrêté' => 'Arrêté',
    'Circulaire' => 'Circulaire',
    'Cahier des charges' => 'Cahier des charges',
  ];
  
  $stats = [
    'total' => 0,
    'lignes_vides' => 0,
    'titre_vide' => 0,
    'type_non_mappe' => 0,
    'pdf_problematique' => 0,
    'valides' => 0
  ];
  
  $types_non_mappes = [];
  
  // D'abord, examiner les en-têtes pour identifier toutes les colonnes
  echo "=== EN-TÊTES ET VALEURS DES COLONNES ===\n";
  for ($col = 'A'; $col <= 'Z'; $col++) {
    $header = trim($worksheet->getCell($col . '19')->getValue() ?? ''); // Ligne 19 pour les en-têtes
    $value20 = trim($worksheet->getCell($col . '20')->getValue() ?? ''); // Première ligne de données
    $calc20 = trim($worksheet->getCell($col . '20')->getCalculatedValue() ?? ''); // Valeur calculée
    
    if (!empty($header) || !empty($value20)) {
      echo "Colonne $col:\n";
      echo "  En-tête: '$header'\n";
      echo "  Valeur brute ligne 20: '$value20'\n";
      echo "  Valeur calculée ligne 20: '$calc20'\n\n";
    }
  }
  echo "\n";
  
  for ($row = 20; $row <= $highestRow; $row++) {
    $stats['total']++;
    
    $type_fr = trim($worksheet->getCell('A' . $row)->getValue() ?? '');
    $type_ar = trim($worksheet->getCell('B' . $row)->getValue() ?? '');
    $domaine_fr = trim($worksheet->getCell('C' . $row)->getValue() ?? '');
    $domaine_ar = trim($worksheet->getCell('D' . $row)->getValue() ?? '');
    $numero = trim($worksheet->getCell('E' . $row)->getValue() ?? '');
    $titre_fr = trim($worksheet->getCell('F' . $row)->getValue() ?? '');
    $titre_ar = trim($worksheet->getCell('G' . $row)->getValue() ?? '');
    $date_publication = trim($worksheet->getCell('H' . $row)->getValue() ?? '');
    $pdf_fr = trim($worksheet->getCell('I' . $row)->getValue() ?? '');
    $pdf_ar = trim($worksheet->getCell('J' . $row)->getValue() ?? '');
    
    // Vérifier d'autres colonnes pour les PDF
    $pdf_regl_fr = trim($worksheet->getCell('K' . $row)->getValue() ?? '');
    $pdf_regl_ar = trim($worksheet->getCell('L' . $row)->getValue() ?? '');
    $col_m = trim($worksheet->getCell('M' . $row)->getValue() ?? '');
    $col_n = trim($worksheet->getCell('N' . $row)->getValue() ?? '');
    $col_o = trim($worksheet->getCell('O' . $row)->getValue() ?? '');
    $col_p = trim($worksheet->getCell('P' . $row)->getValue() ?? '');
    
    // Debug: afficher les premières lignes pour voir où sont les vrais PDF
    if ($row <= 22) {
      echo "LIGNE $row:\n";
      echo "  I (PDF_FR): '$pdf_fr'\n";
      echo "  J (PDF_AR): '$pdf_ar'\n";
      echo "  K: '$pdf_regl_fr'\n";
      echo "  L: '$pdf_regl_ar'\n";
      echo "  M: '$col_m'\n";
      echo "  N: '$col_n'\n";
      echo "  O: '$col_o'\n";
      echo "  P: '$col_p'\n\n";
    }
    
    // Vérifier si ligne complètement vide (toutes les colonnes importantes)
    if (empty($type_fr) && empty($type_ar) && empty($domaine_fr) && empty($domaine_ar) && 
        empty($numero) && empty($titre_fr) && empty($titre_ar) && empty($date_publication) && 
        empty($pdf_fr) && empty($pdf_ar)) {
      $stats['lignes_vides']++;
      continue;
    }
    
    // Vérifier titre français
    if (empty($titre_fr)) {
      $stats['titre_vide']++;
      echo "LIGNE $row: Titre français vide (Type: '$type_fr', PDF: '$pdf_fr')\n";
      continue;
    }
    
    // Vérifier type mappé
    $mapped_type = isset($type_mapping[$type_fr]) ? $type_mapping[$type_fr] : null;
    if (!$mapped_type) {
      $stats['type_non_mappe']++;
      $types_non_mappes[$type_fr] = ($types_non_mappes[$type_fr] ?? 0) + 1;
      echo "LIGNE $row: Type non mappé '$type_fr' (Titre: " . substr($titre_fr, 0, 50) . "...)\n";
      continue;
    }
    
    // Vérifier si les PDF ont des noms problématiques
    $pdf_problematique = false;
    if (!empty($pdf_fr) && (strpos($pdf_fr, 'OneDrive') !== false || strpos($pdf_fr, ' - ') !== false)) {
      $pdf_problematique = true;
    }
    if (!empty($pdf_ar) && (strpos($pdf_ar, 'OneDrive') !== false || strpos($pdf_ar, ' - ') !== false)) {
      $pdf_problematique = true;
    }
    
    if ($pdf_problematique) {
      $stats['pdf_problematique']++;
      if ($row <= 30) { // Afficher les premiers exemples
        echo "LIGNE $row: PDF problématique - FR: '$pdf_fr', AR: '$pdf_ar'\n";
      }
    }
    
    $stats['valides']++;
  }
  
  echo "\n=== STATISTIQUES ===\n";
  echo "Total lignes analysées: {$stats['total']}\n";
  echo "Lignes complètement vides: {$stats['lignes_vides']}\n";
  echo "Lignes avec titre vide: {$stats['titre_vide']}\n";
  echo "Lignes avec type non mappé: {$stats['type_non_mappe']}\n";
  echo "Lignes avec PDF problématiques: {$stats['pdf_problematique']}\n";
  echo "Lignes valides traitées: {$stats['valides']}\n";
  
  if (!empty($types_non_mappes)) {
    echo "\n=== TYPES NON MAPPÉS ===\n";
    foreach ($types_non_mappes as $type => $count) {
      echo "- '$type' ($count fois)\n";
    }
    
    echo "\n=== SUGGESTION ===\n";
    echo "Pour traiter ces types, ajoutez-les au mapping dans le script :\n";
    foreach (array_keys($types_non_mappes) as $type) {
      echo "'$type' => 'TYPE_CIBLE',\n";
    }
  }
  
} catch (Exception $e) {
  echo "Erreur: " . $e->getMessage() . "\n";
}