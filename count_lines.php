<?php

use PhpOffice\PhpSpreadsheet\IOFactory;

$xlsm_file_path = '/var/www/html/mtl/reglementation.xlsm';

try {
  $spreadsheet = IOFactory::load($xlsm_file_path);
  $worksheet = $spreadsheet->getSheetByName('Transport routier');
  
  $highestRow = $worksheet->getHighestRow();
  echo "Total lignes dans la feuille: $highestRow\n";
  
  $count = 0;
  $samples = [];
  
  for ($row = 20; $row <= $highestRow; $row++) {
    $titre_fr = trim($worksheet->getCell('F' . $row)->getValue() ?? '');
    if (!empty($titre_fr)) {
      $count++;
      if ($count <= 5) { // Garder les 5 premiers échantillons
        $samples[] = "Ligne $row: " . substr($titre_fr, 0, 100) . "...";
      }
    }
  }
  
  echo "Lignes avec titre français non vide: $count\n\n";
  echo "Échantillons:\n";
  foreach ($samples as $sample) {
    echo $sample . "\n";
  }
  
} catch (Exception $e) {
  echo "Erreur: " . $e->getMessage() . "\n";
}