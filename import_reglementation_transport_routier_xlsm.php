<?php

use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Script d'importation des réglementations Transport routier depuis un fichier XLSM
 * Usage: drush php:script import_reglementation_transport_routier_xlsm.php
 */

// Chemin vers le fichier XLSM
$xlsm_file_path = '/var/www/html/mtl/reglementation.xlsm';
$pdf_source_path = '/var/www/html/mtl/Portail MTL - Collecte de la réglementaire/transport routier';

// Vérifier si le fichier existe
if (!file_exists($xlsm_file_path)) {
  echo "Erreur: Le fichier XLSM n'existe pas: $xlsm_file_path\n";
  return;
}

echo "Début de l'importation des réglementations Transport routier depuis XLSM...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 * Met à jour les termes existants s'ils n'ont pas les bons filtres
 */
function getTaxonomyTermByName($name, $vocabulary, $sector_term = null) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    $term = reset($terms);
    
    // Vérifier et mettre à jour le secteur si nécessaire
    if ($sector_term && $vocabulary === 'domaines_d_activites') {
      if ($term->hasField('field_secteur')) {
        $current_secteur = $term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $sector_term->id()) {
          $term->set('field_secteur', ['target_id' => $sector_term->id()]);
          $term->save();
          echo "Secteur mis à jour pour le terme existant: $name\n";
        }
      }
    }
    
    return $term;
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term_data = [
    'vid' => $vocabulary,
    'name' => $name,
    'langcode' => 'fr',
  ];
  
  // Ajouter le secteur si fourni (pour les domaines d'activité)
  if ($sector_term && $vocabulary === 'domaines_d_activites') {
    $term_data['field_secteur'] = ['target_id' => $sector_term->id()];
  }
  
  $term = Term::create($term_data);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour récupérer ou créer un terme de type avec migration du contenu existant
 */
function getOrMigrateTypeTermByName($preferred_name, $vocabulary) {
  // D'abord, chercher le terme préféré (ex: 'Décret', 'Loi')
  $preferred_terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $preferred_name,
      'vid' => $vocabulary,
    ]);
  
  if ($preferred_terms) {
    $preferred_term = reset($preferred_terms);
    echo "Terme existant trouvé: $preferred_name\n";
    return $preferred_term;
  }
  
  // Si le terme préféré n'existe pas, chercher les anciens termes anglais
  $old_mapping = [
    'Décret' => ['Decree'],
    'Loi' => ['Law'],
  ];
  
  if (isset($old_mapping[$preferred_name])) {
    foreach ($old_mapping[$preferred_name] as $old_name) {
      $old_terms = \Drupal::entityTypeManager()
        ->getStorage('taxonomy_term')
        ->loadByProperties([
          'name' => $old_name,
          'vid' => $vocabulary,
        ]);
      
      if ($old_terms) {
        $old_term = reset($old_terms);
        echo "Migration: Renommage de '$old_name' vers '$preferred_name'\n";
        
        // Renommer le terme existant
        $old_term->set('name', $preferred_name);
        $old_term->save();
        
        return $old_term;
      }
    }
  }
  
  // Si aucun terme existant n'été trouvé, créer un nouveau terme
  echo "Création du terme '$preferred_name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $preferred_name,
    'langcode' => 'fr',
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour parser la date du XLSM
 */
function parseDate($date_string) {
  if (empty($date_string)) {
    return null;
  }
  
  // Convertir la date Excel en timestamp PHP si c'est un nombre
  if (is_numeric($date_string)) {
    // Excel date serial number (depuis le 1er janvier 1900)
    $unix_timestamp = ($date_string - 25569) * 86400;
    return date('Y-m-d', $unix_timestamp);
  }
  
  // Si c'est déjà une chaîne de date, essayer de la parser
  if (is_string($date_string)) {
    $timestamp = strtotime($date_string);
    if ($timestamp !== false) {
      return date('Y-m-d', $timestamp);
    }
    
    // Format attendu: dd/mm/yyyy
    $date_parts = explode('/', $date_string);
    if (count($date_parts) == 3) {
      $day = str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
      $month = str_pad($date_parts[1], 2, '0', STR_PAD_LEFT);
      $year = $date_parts[2];
      return "$year-$month-$day";
    }
  }
  
  return null;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      return Node::load(reset($nids));
    }
  }
  
  return null;
}

/**
 * Fonction pour extraire le numéro d'un titre
 */
function extractNumber($title) {
  // Chercher des patterns comme "2.12.494", "n° 2-12-494", etc.
  if (preg_match('/(?:n°?\s*)?(\d+[.-]\d+[.-]\d+)/', $title, $matches)) {
    return $matches[1];
  }
  return null;
}

/**
 * Fonction pour chercher un fichier PDF par numéro
 */
function findPdfByNumber($number, $type, $language, $pdf_source_path) {
  $lang_dir = ($language === 'ar') ? 'AR' : 'Fr';
  $type_dirs = [
    'Décret' => 'Décret/',
    'Dahir' => 'Dahir/',
    'Loi' => 'Loi/',
    'Arrêté' => 'Arrêté/',
    'Cahier des charges' => ($language === 'ar') ? 'Cahiers des charges/' : 'Cahiers de charges/',
  ];
  
  if (!isset($type_dirs[$type])) {
    return null;
  }
  
  $search_dir = $pdf_source_path . '/' . $lang_dir . '/' . $type_dirs[$type];
  
  if (!is_dir($search_dir)) {
    return null;
  }
  
  // Lister tous les fichiers PDF du dossier
  $files = glob($search_dir . '*.pdf');
  
  foreach ($files as $file) {
    $filename = basename($file);
    
    // Chercher le numéro dans le nom de fichier (avec différentes variations)
    $search_patterns = [
      str_replace(['.', '-'], ['.', '.'], $number),
      str_replace(['.', '-'], ['-', '-'], $number),
      str_replace(['.', '-'], ['.', '-'], $number),
      str_replace(['.', '-'], ['-', '.'], $number),
    ];
    
    foreach ($search_patterns as $pattern) {
      if (strpos($filename, $pattern) !== false) {
        return $filename;
      }
    }
  }
  
  return null;
}

/**
 * Fonction pour uploader un fichier PDF depuis le dossier local
 */
function uploadPdfFile($pdf_filename, $source_base_path) {
  if (empty($pdf_filename)) {
    return null;
  }
  
  // Rechercher le fichier dans les sous-dossiers Fr et Ar
  $possible_paths = [
    $source_base_path . '/Fr/Arrêté/' . $pdf_filename,
    $source_base_path . '/Fr/Décret/' . $pdf_filename,
    $source_base_path . '/Fr/Dahir/' . $pdf_filename,
    $source_base_path . '/Fr/Loi/' . $pdf_filename,
    $source_base_path . '/Fr/Cahiers de charges/' . $pdf_filename,
    $source_base_path . '/AR/Arrêté/' . $pdf_filename,
    $source_base_path . '/AR/Décret/' . $pdf_filename,
    $source_base_path . '/AR/Dahir/' . $pdf_filename,
    $source_base_path . '/AR/Loi/' . $pdf_filename,
    $source_base_path . '/AR/Cahiers des charges/' . $pdf_filename,
  ];
  
  $source_file_path = null;
  foreach ($possible_paths as $path) {
    if (file_exists($path)) {
      $source_file_path = $path;
      break;
    }
  }
  
  if (!$source_file_path) {
    echo "Fichier PDF non trouvé dans les dossiers: $pdf_filename\n";
    return null;
  }
  
  echo "Fichier trouvé: $source_file_path\n";
  
  // Créer l'URI Drupal de destination
  $destination_file = 'public://reglementation/pdf/' . $pdf_filename;
  
  // Vérifier si l'entité fichier existe déjà
  $existing_files = \Drupal::entityTypeManager()
    ->getStorage('file')
    ->loadByProperties(['uri' => $destination_file]);
  
  if ($existing_files) {
    $file = reset($existing_files);
    echo "Fichier PDF existant réutilisé: $pdf_filename -> {$file->id()}\n";
    return $file;
  }
  
  // Créer le répertoire de destination s'il n'existe pas
  $destination_directory = dirname(\Drupal::service('file_system')->realpath($destination_file));
  if (!is_dir($destination_directory)) {
    \Drupal::service('file_system')->prepareDirectory($destination_directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
  }
  
  // Copier le fichier vers la destination Drupal
  $destination_path = \Drupal::service('file_system')->copy($source_file_path, $destination_file, FileSystemInterface::EXISTS_REPLACE);
  
  if ($destination_path) {
    // Créer l'entité fichier
    $file = File::create([
      'filename' => $pdf_filename,
      'uri' => $destination_path,
      'status' => 1,
      'uid' => 1,
    ]);
    $file->save();
    
    echo "Fichier PDF uploadé: $pdf_filename -> {$file->id()}\n";
    return $file;
  } else {
    echo "Erreur lors de la copie du fichier: $pdf_filename\n";
    return null;
  }
}

// Récupérer les termes existants pour Transport routier
$transport_routier_sector = getTaxonomyTermByName('Transport routier', 'modes_de_transport');

if (!$transport_routier_sector) {
  echo "Erreur: Le terme 'Transport routier' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

try {
  // Charger le fichier XLSM
  $spreadsheet = IOFactory::load($xlsm_file_path);
  
  // Debug: lister tous les onglets
  $sheetNames = $spreadsheet->getSheetNames();
  echo "Onglets disponibles: " . implode(', ', $sheetNames) . "\n";
  
  // Sélectionner l'onglet "Transport routier"
  $worksheet = $spreadsheet->getSheetByName('Transport routier');
  if (!$worksheet) {
    echo "Erreur: Onglet 'Transport routier' non trouvé\n";
    return;
  }
  echo "Onglet sélectionné: " . $worksheet->getTitle() . "\n";
  
  // Obtenir la plus haute ligne et colonne
  $highestRow = $worksheet->getHighestRow();
  $highestColumn = $worksheet->getHighestColumn();
  
  echo "Fichier XLSM chargé: $highestRow lignes, $highestColumn colonnes\n";
  
  // Compter les lignes avec titre français
  $count_with_data = 0;
  for ($debug_row = 20; $debug_row <= $highestRow; $debug_row++) {
    $debug_titre_fr = trim($worksheet->getCell('F' . $debug_row)->getValue() ?? '');
    if (!empty($debug_titre_fr)) {
      $count_with_data++;
    }
  }
  echo "Nombre total de lignes avec titre français: $count_with_data\n";
  
  $imported = 0;
  $updated = 0;
  $errors = 0;
  
  // Commencer à partir de la ligne 20 (après les en-têtes comme dans le script original)
  for ($row = 20; $row <= $highestRow; $row++) {
    try {
      // Lire les données de la ligne
      $type_fr = trim($worksheet->getCell('A' . $row)->getValue() ?? '');
      $type_ar = trim($worksheet->getCell('B' . $row)->getValue() ?? '');
      $domaine_fr = trim($worksheet->getCell('C' . $row)->getValue() ?? '');
      $domaine_ar = trim($worksheet->getCell('D' . $row)->getValue() ?? '');
      $numero = trim($worksheet->getCell('E' . $row)->getValue() ?? '');
      $titre_fr = trim($worksheet->getCell('F' . $row)->getValue() ?? '');
      $titre_ar = trim($worksheet->getCell('G' . $row)->getValue() ?? '');
      $date_publication = $worksheet->getCell('H' . $row)->getValue() ?? '';
      // Extraire le numéro du titre et chercher les fichiers PDF correspondants
      $number = extractNumber($titre_fr);
      $pdf_fr = null;
      $pdf_ar = null;
      
      if ($number) {
        $pdf_fr = findPdfByNumber($number, $type_fr, 'fr', $pdf_source_path);
        $pdf_ar = findPdfByNumber($number, $type_fr, 'ar', $pdf_source_path);
      }
      $remarques = trim($worksheet->getCell('O' . $row)->getValue() ?? '');
      
      // Vérifier si la ligne contient des données valides (au moins un titre français)
      if (empty($titre_fr)) {
        echo "Ligne $row ignorée (titre français vide)\n";
        continue;
      }
      
      // Récupérer ou créer le domaine d'activité depuis les données XLSM
      $domaine_term = null;
      if (!empty($domaine_fr)) {
        $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites', $transport_routier_sector);
        
        // Ajouter le secteur au domaine d'activité s'il n'est pas déjà assigné
        if ($domaine_term && $domaine_term->hasField('field_secteur')) {
          $current_secteur = $domaine_term->get('field_secteur')->target_id;
          if (!$current_secteur || $current_secteur != $transport_routier_sector->id()) {
            $domaine_term->set('field_secteur', ['target_id' => $transport_routier_sector->id()]);
            $domaine_term->save();
            echo "Secteur Transport routier assigné au domaine: $domaine_fr\n";
          }
        }
        
        // Ajouter ou mettre à jour la traduction arabe avec les mêmes filtres
        if (!empty($domaine_ar) && $domaine_term) {
          if (!$domaine_term->hasTranslation('ar')) {
            // Créer la traduction avec les mêmes champs de référence
            $ar_translation_data = ['name' => $domaine_ar];
            
            // Hériter du secteur du terme principal
            if ($domaine_term->hasField('field_secteur') && !$domaine_term->get('field_secteur')->isEmpty()) {
              $ar_translation_data['field_secteur'] = $domaine_term->get('field_secteur')->getValue();
            }
            
            $domaine_term->addTranslation('ar', $ar_translation_data);
            $domaine_term->save();
            echo "Traduction arabe ajoutée pour le domaine: $domaine_fr avec secteur\n";
          } else {
            // Mettre à jour la traduction existante si elle n'a pas les bons filtres
            $ar_translation = $domaine_term->getTranslation('ar');
            $needs_update = false;
            
            // Vérifier et mettre à jour le secteur
            if ($domaine_term->hasField('field_secteur') && !$domaine_term->get('field_secteur')->isEmpty()) {
              $main_secteur = $domaine_term->get('field_secteur')->getValue();
              if ($ar_translation->hasField('field_secteur')) {
                $ar_secteur = $ar_translation->get('field_secteur')->getValue();
                if ($ar_secteur !== $main_secteur) {
                  $ar_translation->set('field_secteur', $main_secteur);
                  $needs_update = true;
                }
              }
            }
            
            if ($needs_update) {
              $ar_translation->save();
              echo "Traduction arabe mise à jour pour le domaine: $domaine_fr\n";
            }
          }
        }
      }
      
      echo "\nTraitement ligne $row: $titre_fr\n";
      
      // Vérifier si le contenu existe déjà
      $existing_node = getExistingNode($titre_fr, $numero);
      
      // Mapper les types français vers les termes existants (priorité aux termes français)
      $type_mapping = [
        'Dahir' => 'Dahir',
        'Loi organique' => 'Loi', // Utiliser 'Loi' au lieu de 'Law'
        'Loi' => 'Loi',           // Utiliser 'Loi' au lieu de 'Law'
        'DECRET ROYAL' => 'Décret', // Utiliser 'Décret' au lieu de 'Decree'
        'Décret royal' => 'Décret', // Utiliser 'Décret' au lieu de 'Decree'
        'Décret' => 'Décret',        // Utiliser 'Décret' au lieu de 'Decree'
        'DECRET' => 'Décret',         // Utiliser 'Décret' au lieu de 'Decree'
        'Arrêté royal' => 'Arrêté',
        'Arrêté' => 'Arrêté',
        'Circulaire' => 'Circulaire',
        'Cahier des charges' => 'Cahier des charges',
      ];
      
      $mapped_type = isset($type_mapping[$type_fr]) ? $type_mapping[$type_fr] : null;
      
      if (!$mapped_type) {
        echo "Attention: Type '$type_fr' non mappé, ignoré\n";
        continue;
      }
      
      // Récupérer ou créer le terme de type de loi avec migration si nécessaire
      $type_term = getOrMigrateTypeTermByName($mapped_type, 'type');
      
      // Ajouter ou mettre à jour la traduction arabe du type avec les mêmes filtres
      if (!empty($type_ar) && $type_term) {
        if (!$type_term->hasTranslation('ar')) {
          // Créer la traduction en héritant de tous les champs du terme principal
          $ar_translation_data = ['name' => $type_ar];
          
          // Hériter des champs de référence du terme principal (secteur, etc.)
          $term_fields = $type_term->getFieldDefinitions();
          foreach ($term_fields as $field_name => $field_definition) {
            // Copier les champs de référence (entity_reference) sauf les champs système
            if ($field_definition->getType() === 'entity_reference' && 
                !in_array($field_name, ['vid', 'parent', 'revision_user']) &&
                $type_term->hasField($field_name) && 
                !$type_term->get($field_name)->isEmpty()) {
              $ar_translation_data[$field_name] = $type_term->get($field_name)->getValue();
            }
          }
          
          $type_term->addTranslation('ar', $ar_translation_data);
          $type_term->save();
          echo "Traduction arabe ajoutée pour le type: $mapped_type avec filtres\n";
        } else {
          // Mettre à jour la traduction existante si elle n'a pas les bons filtres
          $ar_translation = $type_term->getTranslation('ar');
          $needs_update = false;
          
          // Vérifier et synchroniser tous les champs de référence
          $term_fields = $type_term->getFieldDefinitions();
          foreach ($term_fields as $field_name => $field_definition) {
            if ($field_definition->getType() === 'entity_reference' && 
                !in_array($field_name, ['vid', 'parent', 'revision_user']) &&
                $type_term->hasField($field_name) && 
                !$type_term->get($field_name)->isEmpty()) {
              
              $main_value = $type_term->get($field_name)->getValue();
              if ($ar_translation->hasField($field_name)) {
                $ar_value = $ar_translation->get($field_name)->getValue();
                if ($ar_value !== $main_value) {
                  $ar_translation->set($field_name, $main_value);
                  $needs_update = true;
                }
              }
            }
          }
          
          if ($needs_update) {
            $ar_translation->save();
            echo "Traduction arabe mise à jour pour le type: $mapped_type\n";
          }
        }
      }
      
      // Parser la date
      $date_formatted = parseDate($date_publication);
      
      // Gérer le titre long (limite de 255 caractères pour le champ title)
      $title_for_node = $titre_fr;
      $long_title = null;
      
      if (mb_strlen($titre_fr, 'UTF-8') > 255) {
        $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
        $long_title = $titre_fr;
        echo "Titre tronqué pour: $title_for_node\n";
      }
      
      // Uploader les fichiers PDF
      $pdf_files = [];
      if (!empty($pdf_fr)) {
        $pdf_file_fr = uploadPdfFile($pdf_fr, $pdf_source_path);
        if ($pdf_file_fr) {
          $pdf_files[] = ['target_id' => $pdf_file_fr->id()];
        }
      }
      if (!empty($pdf_ar)) {
        $pdf_file_ar = uploadPdfFile($pdf_ar, $pdf_source_path);
        if ($pdf_file_ar) {
          $pdf_files[] = ['target_id' => $pdf_file_ar->id()];
        }
      }
      
      // Préparer les données du nœud
      $node_data = [
        'type' => 'reglementation',
        'title' => $title_for_node,
        'field_type_loi' => ['target_id' => $type_term->id()],
        'field_secteur' => ['target_id' => $transport_routier_sector->id()],
        'status' => 1,
        'uid' => 1,
      ];
      
      // Ajouter le domaine d'activité si disponible
      if ($domaine_term) {
        $node_data['field_domaine_d_activite'] = ['target_id' => $domaine_term->id()];
      }
      
      // Ajouter le titre long si nécessaire
      if ($long_title) {
        $node_data['field_titre_long'] = ['value' => $long_title];
      }
      
      // Ajouter la date si disponible
      if ($date_formatted) {
        $node_data['field_date'] = ['value' => $date_formatted];
      }
      
      // Ajouter les fichiers PDF uploadés
      if (!empty($pdf_files)) {
        $node_data['field_lien_telechargement'] = $pdf_files;
      }
      
      if ($existing_node) {
        // Mettre à jour le nœud existant et vérifier les champs manquants
        $needs_update = false;
        
        // Vérifier et mettre à jour le secteur s'il manque
        if ($existing_node->hasField('field_secteur')) {
          $current_secteur = $existing_node->get('field_secteur')->target_id;
          if (!$current_secteur || $current_secteur != $transport_routier_sector->id()) {
            $existing_node->set('field_secteur', ['target_id' => $transport_routier_sector->id()]);
            $needs_update = true;
            echo "Secteur Transport routier ajouté au nœud existant\n";
          }
        }
        
        // Vérifier et mettre à jour le domaine d'activité s'il manque
        if ($domaine_term && $existing_node->hasField('field_domaine_d_activite')) {
          $current_domaine = $existing_node->get('field_domaine_d_activite')->target_id;
          if (!$current_domaine || $current_domaine != $domaine_term->id()) {
            $existing_node->set('field_domaine_d_activite', ['target_id' => $domaine_term->id()]);
            $needs_update = true;
            echo "Domaine d'activité ajouté au nœud existant\n";
          }
        }
        
        // Vérifier et mettre à jour le type s'il manque
        if ($type_term && $existing_node->hasField('field_type_loi')) {
          $current_type = $existing_node->get('field_type_loi')->target_id;
          if (!$current_type || $current_type != $type_term->id()) {
            $existing_node->set('field_type_loi', ['target_id' => $type_term->id()]);
            $needs_update = true;
            echo "Type de loi ajouté au nœud existant\n";
          }
        }
        
        // Mettre à jour les fichiers PDF dans field_lien_telechargement si disponibles
        if (!empty($pdf_files) && $existing_node->hasField('field_lien_telechargement')) {
          $existing_node->set('field_lien_telechargement', $pdf_files);
          $needs_update = true;
          echo "Fichiers PDF mis à jour dans field_lien_telechargement\n";
        }
        
        // Appliquer les autres mises à jour (SANS toucher au field_lien_telechargement déjà traité)
        foreach ($node_data as $field => $value) {
          if (!in_array($field, ['type', 'field_lien_telechargement', 'field_secteur', 'field_domaine_d_activite', 'field_type_loi'])) {
            $existing_node->set($field, $value);
            $needs_update = true;
          }
        }
        
        if ($needs_update) {
          $existing_node->save();
        }
        
        // Ajouter/mettre à jour la traduction arabe avec vérification des champs manquants
        if (!empty($titre_ar)) {
          // Gérer aussi le titre arabe long
          $title_ar_for_node = $titre_ar;
          if (mb_strlen($titre_ar, 'UTF-8') > 255) {
            $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
          }
          
          if ($existing_node->hasTranslation('ar')) {
            $ar_translation = $existing_node->getTranslation('ar');
            $ar_translation->setTitle($title_ar_for_node);
            
            // Ajouter le titre long arabe si le titre français en a un
            if ($existing_node->hasField('field_titre_long') && !$existing_node->get('field_titre_long')->isEmpty() && $ar_translation->hasField('field_titre_long')) {
              $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
            }
            
            // Vérifier que la traduction arabe a les mêmes filtres que la version française
            $ar_needs_update = false;
            
            // Synchroniser le secteur
            if ($ar_translation->hasField('field_secteur') && $existing_node->hasField('field_secteur')) {
              $fr_secteur = $existing_node->get('field_secteur')->getValue();
              $ar_secteur = $ar_translation->get('field_secteur')->getValue();
              if ($ar_secteur !== $fr_secteur) {
                $ar_translation->set('field_secteur', $fr_secteur);
                $ar_needs_update = true;
              }
            }
            
            // Synchroniser le domaine d'activité
            if ($ar_translation->hasField('field_domaine_d_activite') && $existing_node->hasField('field_domaine_d_activite')) {
              $fr_domaine = $existing_node->get('field_domaine_d_activite')->getValue();
              $ar_domaine = $ar_translation->get('field_domaine_d_activite')->getValue();
              if ($ar_domaine !== $fr_domaine) {
                $ar_translation->set('field_domaine_d_activite', $fr_domaine);
                $ar_needs_update = true;
              }
            }
            
            // Synchroniser le type de loi
            if ($ar_translation->hasField('field_type_loi') && $existing_node->hasField('field_type_loi')) {
              $fr_type = $existing_node->get('field_type_loi')->getValue();
              $ar_type = $ar_translation->get('field_type_loi')->getValue();
              if ($ar_type !== $fr_type) {
                $ar_translation->set('field_type_loi', $fr_type);
                $ar_needs_update = true;
              }
            }
            
            // Synchroniser les fichiers PDF
            if ($ar_translation->hasField('field_lien_telechargement') && $existing_node->hasField('field_lien_telechargement')) {
              $fr_files = $existing_node->get('field_lien_telechargement')->getValue();
              $ar_files = $ar_translation->get('field_lien_telechargement')->getValue();
              if ($ar_files !== $fr_files) {
                $ar_translation->set('field_lien_telechargement', $fr_files);
                $ar_needs_update = true;
              }
            }
            
            if ($ar_needs_update) {
              echo "Filtres et fichiers synchronisés pour la traduction arabe\n";
            }
            
            $ar_translation->save();
          } else {
            // Créer la traduction avec tous les champs de filtrage
            $ar_data = ['title' => $title_ar_for_node];
            
            // Ajouter le titre long arabe si le titre français en a un
            if ($existing_node->hasField('field_titre_long') && !$existing_node->get('field_titre_long')->isEmpty()) {
              $ar_data['field_titre_long'] = ['value' => $titre_ar];
            }
            
            // Copier tous les champs de filtrage depuis la version française
            if ($existing_node->hasField('field_secteur') && !$existing_node->get('field_secteur')->isEmpty()) {
              $ar_data['field_secteur'] = $existing_node->get('field_secteur')->getValue();
            }
            if ($existing_node->hasField('field_domaine_d_activite') && !$existing_node->get('field_domaine_d_activite')->isEmpty()) {
              $ar_data['field_domaine_d_activite'] = $existing_node->get('field_domaine_d_activite')->getValue();
            }
            if ($existing_node->hasField('field_type_loi') && !$existing_node->get('field_type_loi')->isEmpty()) {
              $ar_data['field_type_loi'] = $existing_node->get('field_type_loi')->getValue();
            }
            if ($existing_node->hasField('field_lien_telechargement') && !$existing_node->get('field_lien_telechargement')->isEmpty()) {
              $ar_data['field_lien_telechargement'] = $existing_node->get('field_lien_telechargement')->getValue();
            }
            
            $existing_node->addTranslation('ar', $ar_data);
            $existing_node->save();
            echo "Traduction arabe créée avec tous les filtres et fichiers\n";
          }
        }
        
        echo "Nœud mis à jour: {$existing_node->id()}\n";
        $updated++;
      } else {
        // Créer un nouveau nœud avec les PDFs uploadés
        $node = Node::create($node_data);
        $node->save();
        
        // Ajouter la traduction arabe avec tous les filtres
        if (!empty($titre_ar)) {
          // Gérer aussi le titre arabe long pour les nouveaux nœuds
          $title_ar_for_node = $titre_ar;
          if (mb_strlen($titre_ar, 'UTF-8') > 255) {
            $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
          }
          
          $ar_data = ['title' => $title_ar_for_node];
          
          // Ajouter le titre long arabe si le titre français en a un
          if ($node->hasField('field_titre_long') && !$node->get('field_titre_long')->isEmpty()) {
            $ar_data['field_titre_long'] = ['value' => $titre_ar];
          }
          
          // Copier tous les champs de filtrage depuis le nœud principal
          if ($node->hasField('field_secteur') && !$node->get('field_secteur')->isEmpty()) {
            $ar_data['field_secteur'] = $node->get('field_secteur')->getValue();
          }
          if ($domaine_term && $node->hasField('field_domaine_d_activite')) {
            $ar_data['field_domaine_d_activite'] = $node->get('field_domaine_d_activite')->getValue();
          }
          if ($type_term && $node->hasField('field_type_loi')) {
            $ar_data['field_type_loi'] = $node->get('field_type_loi')->getValue();
          }
          if ($node->hasField('field_lien_telechargement') && !$node->get('field_lien_telechargement')->isEmpty()) {
            $ar_data['field_lien_telechargement'] = $node->get('field_lien_telechargement')->getValue();
          }
          
          $node->addTranslation('ar', $ar_data);
          $node->save();
          echo "Traduction arabe créée avec tous les filtres et fichiers\n";
        }
        
        echo "Nouveau nœud créé: {$node->id()}\n";
        $imported++;
      }
      
    } catch (Exception $e) {
      echo "Erreur ligne $row: " . $e->getMessage() . "\n";
      $errors++;
    }
  }
  
} catch (Exception $e) {
  echo "Erreur lors du chargement du fichier XLSM: " . $e->getMessage() . "\n";
  return;
}

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";