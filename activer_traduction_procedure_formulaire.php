<?php

// Script pour activer la traduction de contenu pour procedure_formulaire
echo "=== ACTIVATION TRADUCTION PROCEDURE_FORMULAIRE ===\n";

// Obtenir la configuration
$config = \Drupal::configFactory()->getEditable('language.content_settings.node.procedure_formulaire');

echo "Configuration actuelle:\n";
echo "- Translation enabled: " . ($config->get('third_party_settings.content_translation.enabled') ? 'OUI' : 'NON') . "\n";
echo "- Language alterable: " . ($config->get('language_alterable') ? 'OUI' : 'NON') . "\n";

// Activer la traduction
$config->set('third_party_settings.content_translation.enabled', true);
$config->set('language_alterable', true);
$config->save();

echo "\n✓ Configuration mise à jour:\n";
echo "- Translation enabled: OUI\n";
echo "- Language alterable: OUI\n";

echo "\nActivation terminée avec succès!\n";