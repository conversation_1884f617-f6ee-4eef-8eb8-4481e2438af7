<?php
// Script pour extraire les liens d'images du fichier output-fr.json

$jsonFile = '/var/www/html/mtl/output-fr.json';
$outputFile = '/var/www/html/mtl/liens_images.txt';

// Lire le fichier JSON
$jsonContent = file_get_contents($jsonFile);
$data = json_decode($jsonContent, true);

// Ouvrir le fichier de sortie
$file = fopen($outputFile, 'w');

// Extraire les liens d'images
foreach ($data as $item) {
    if (isset($item['image']) && !empty($item['image'])) {
        fwrite($file, $item['image'] . "\n");
    }
}

// Fermer le fichier
fclose($file);

echo "Extraction terminée. Les liens d'images ont été sauvegardés dans liens_images.txt\n";
?>