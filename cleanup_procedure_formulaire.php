<?php

use <PERSON><PERSON>al\node\Entity\Node;

/**
 * Script pour supprimer tous les nœuds de type procedure_formulaire
 * Usage: drush php:script cleanup_procedure_formulaire.php
 */

echo "Début de la suppression des nœuds procedure_formulaire...\n";

// Récupérer tous les nœuds de type procedure_formulaire
$query = \Drupal::entityQuery('node')
  ->condition('type', 'procedure_formulaire')
  ->accessCheck(FALSE);

$nids = $query->execute();

if (empty($nids)) {
  echo "Aucun nœud procedure_formulaire trouvé.\n";
  return;
}

echo "Trouvé " . count($nids) . " nœuds à supprimer.\n";

$deleted = 0;
$errors = 0;

foreach ($nids as $nid) {
  try {
    $node = Node::load($nid);
    if ($node) {
      $title = $node->getTitle();
      echo "Suppression nœud $nid: " . ($title ?: '[SANS TITRE]') . "\n";
      $node->delete();
      $deleted++;
    }
  } catch (Exception $e) {
    echo "Erreur suppression nœud $nid: " . $e->getMessage() . "\n";
    $errors++;
  }
}

echo "\n=== RÉSULTATS DE LA SUPPRESSION ===\n";
echo "Nœuds supprimés: $deleted\n";
echo "Erreurs: $errors\n";
echo "Suppression terminée!\n";