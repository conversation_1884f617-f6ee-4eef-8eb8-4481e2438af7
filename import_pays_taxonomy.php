<?php

/**
 * <PERSON><PERSON>t to import CSV data into Pays taxonomy terms with Arabic translations
 * Usage: drush php:script import_pays_taxonomy.php
 */

use Drupal\taxonomy\Entity\Term;

// Path to French CSV file
$csv_file = '/var/www/mtl/Liste des accords signés.csv';

// Path to Arabic CSV file
$arabic_csv_file = '/var/www/mtl/لائحة-الاتفاقيات-الموقعة.csv';

// Complete mapping between French and Arabic country names
$country_mapping = [
  'Oman' => 'سلطنة عمان',
  'Mauritanie' => 'موريتانيا',
  'Palestine' => 'فلسطين',
  'Qatar' => 'قطر',
  'Les Émirats arabes unis' => 'دولة الإمارات العربية المتحدة',
  'Koweit' => 'الكويت',
  'Arabie Saoudite' => 'المملكة العربية السعودية',
  '<PERSON>hreïn' => 'البحرين',
  'Algérie' => 'الجزائر',
  'Egypte' => 'مصر',
  'Irak' => 'العراق',
  'Jordanie' => 'الأردن',
  'Liban' => 'لبنان',
  'Libye' => 'ليبيا',
  'Soudan' => 'السودان',
  'Syrie' => 'سوريا',
  'Tunisie' => 'تونس',
];

// Additional mappings for CSV variations
$arabic_to_french_mapping = [
  'سلطنة عمان' => 'Oman',
  'موريتانيا' => 'Mauritanie',
  'فلسطين' => 'Palestine',
  'قطر' => 'Qatar',
  'دولة الإمارات العربية المتحدة' => 'Les Émirats arabes unis',
  'الإمارات' => 'Les Émirats arabes unis',
  'الكويت' => 'Koweit',
  'المملكة العربية السعودية' => 'Arabie Saoudite',
  'السعودية' => 'Arabie Saoudite',
  'البحرين' => 'Bahreïn',
  'الجزائر' => 'Algérie',
  'مصر' => 'Egypte',
  'العراق' => 'Irak',
  'الأردن' => 'Jordanie',
  'لبنان' => 'Liban',
  'ليبيا' => 'Libye',
  'السودان' => 'Soudan',
  'سودان' => 'Soudan',
  'سوريا' => 'Syrie',
  'تونس' => 'Tunisie',
];

// Check if files exist
if (!file_exists($csv_file)) {
  echo "French CSV file not found: $csv_file\n";
  exit(1);
}

if (!file_exists($arabic_csv_file)) {
  echo "Arabic CSV file not found: $arabic_csv_file\n";
  exit(1);
}

// Step 1: Import French data
echo "=== STEP 1: Importing French Data ===\n";
$handle = fopen($csv_file, 'r');
if ($handle === FALSE) {
  echo "Could not open French CSV file\n";
  exit(1);
}

$header = fgetcsv($handle);
echo "French CSV headers: " . implode(', ', $header) . "\n";

$processed = 0;
$created = 0;
$updated = 0;
$french_descriptions = []; // Store French descriptions by country

while (($data = fgetcsv($handle)) !== FALSE) {
  $pays = trim($data[0]);
  $titre = trim($data[1]);
  
  if (empty($pays)) {
    continue;
  }
  
  echo "Processing: $pays - $titre\n";
  
  $existing_terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'vid' => 'pays',
      'name' => $pays,
    ]);
  
  if (!empty($existing_terms)) {
    $term = reset($existing_terms);
    
    // Get existing French descriptions for this country
    if (!isset($french_descriptions[$pays])) {
      $french_descriptions[$pays] = [];
    }
    $french_descriptions[$pays][] = $titre;
    echo "  Collected French description for: $pays\n";
  } else {
    // Create new term but don't set description yet
    $term = Term::create([
      'vid' => 'pays',
      'name' => $pays,
    ]);
    $term->save();
    echo "  Created new term: $pays\n";
    $created++;
    
    // Store description for later
    if (!isset($french_descriptions[$pays])) {
      $french_descriptions[$pays] = [];
    }
    $french_descriptions[$pays][] = $titre;
  }
  
  $processed++;
}

fclose($handle);

// Now update all French descriptions at once (overwrite mode)
echo "\n=== Updating French Descriptions ===\n";
foreach ($french_descriptions as $country => $descriptions) {
  $existing_terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'vid' => 'pays',
      'name' => $country,
    ]);
  
  if (!empty($existing_terms)) {
    $term = reset($existing_terms);
    $final_description = implode("\n\n", $descriptions);
    $term->set('description', $final_description);
    $term->save();
    echo "  ✅ Updated French description for $country (" . count($descriptions) . " titles)\n";
    $updated++;
  }
}

echo "\n=== French Import Summary ===\n";
echo "Processed rows: $processed\n";
echo "New terms created: $created\n";
echo "French descriptions updated: $updated\n";

// Step 2: Import Arabic translations
echo "\n=== STEP 2: Importing Arabic Translations ===\n";
$arabic_handle = fopen($arabic_csv_file, 'r');
if ($arabic_handle === FALSE) {
  echo "Could not open Arabic CSV file\n";
  exit(1);
}

$arabic_header = fgetcsv($arabic_handle);
echo "Arabic CSV headers: " . implode(', ', $arabic_header) . "\n";

$arabic_descriptions = [];
$arabic_processed = 0;

while (($data = fgetcsv($arabic_handle)) !== FALSE) {
  $titre_ar = trim($data[0]);
  $pays_ar = trim($data[1]);
  
  if (empty($pays_ar) || empty($titre_ar)) {
    continue;
  }
  
  if (isset($arabic_to_french_mapping[$pays_ar])) {
    $pays_fr = $arabic_to_french_mapping[$pays_ar];
    if (!isset($arabic_descriptions[$pays_fr])) {
      $arabic_descriptions[$pays_fr] = [];
    }
    $arabic_descriptions[$pays_fr][] = $titre_ar;
    echo "Mapping: $pays_ar -> $pays_fr\n";
  } else {
    echo "Warning: No French mapping found for: $pays_ar\n";
  }
  
  $arabic_processed++;
}

fclose($arabic_handle);

$translation_updated = 0;
foreach ($arabic_descriptions as $french_name => $arabic_titles) {
  $existing_terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'vid' => 'pays',
      'name' => $french_name,
    ]);
  
  if (!empty($existing_terms)) {
    $term = reset($existing_terms);
    $arabic_description = implode("\n\n", $arabic_titles);
    
    if (isset($country_mapping[$french_name]) && !empty($arabic_description)) {
      if (!$term->hasTranslation('ar')) {
        // Create Arabic translation with ONLY Arabic content
        $term->addTranslation('ar', [
          'name' => $country_mapping[$french_name],
          'description' => $arabic_description,
        ]);
        $term->save();
        echo "  ✅ Added Arabic translation for $french_name (" . count($arabic_titles) . " titles)\n";
        $translation_updated++;
      } else {
        // Overwrite existing Arabic translation
        $arabic_term = $term->getTranslation('ar');
        $arabic_term->set('description', $arabic_description);
        $arabic_term->save();
        echo "  ✅ Overwritten Arabic translation for $french_name (" . count($arabic_titles) . " titles)\n";
        $translation_updated++;
      }
    }
  }
}

echo "\n=== FINAL SUMMARY ===\n";
echo "French data processed: $processed rows\n";
echo "Arabic data processed: $arabic_processed rows\n";
echo "Terms with Arabic translations: $translation_updated\n";
echo "Complete import finished successfully!\n";