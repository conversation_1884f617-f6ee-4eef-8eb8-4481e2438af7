<?php

use Drupal\file\Entity\File;
use Drupal\media\Entity\Media;

/**
 * Debug précis du processus complet d'image
 */

$json_file_path = '/var/www/html/mtl/output-fr.json';
$json_content = file_get_contents($json_file_path);
$data = json_decode($json_content, true);

$test_item = $data[0]; // Premier élément
$image_url = $test_item['image'];
$title = $test_item['titre'];

echo "DEBUG COMPLET - PROCESSUS IMAGE\n";
echo "URL: $image_url\n";
echo "Titre: " . substr($title, 0, 40) . "...\n\n";

echo "=== ÉTAPE 1: Téléchargement ===\n";

$ch = curl_init();
curl_setopt_array($ch, [
  CURLOPT_URL => $image_url,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_TIMEOUT => 45, // Timeout augmenté
  CURLOPT_SSL_VERIFYPEER => false,
  CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  CURLOPT_FOLLOWLOCATION => true,
]);

$start_time = time();
$file_data = curl_exec($ch);
$end_time = time();

$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$curl_error = curl_error($ch);

curl_close($ch);

echo "Durée: " . ($end_time - $start_time) . "s\n";
echo "HTTP Code: $http_code\n";
echo "Content-Type: $content_type\n";
echo "Erreur cURL: " . ($curl_error ?: 'Aucune') . "\n";
echo "Taille data: " . strlen($file_data) . " bytes\n";

if (!$file_data || $http_code !== 200) {
  echo "❌ ÉCHEC TÉLÉCHARGEMENT\n";
  echo "Cause: ";
  if (!$file_data) echo "Pas de données reçues\n";
  if ($http_code !== 200) echo "Code HTTP: $http_code\n";
  if ($curl_error) echo "Erreur cURL: $curl_error\n";
  exit;
}

echo "✅ Téléchargement réussi\n\n";

echo "=== ÉTAPE 2: Préparation fichier ===\n";

$file_name = basename(parse_url($image_url, PHP_URL_PATH));
if (empty($file_name) || strpos($file_name, '.') === false) {
  $file_name = 'actualite_debug_' . time() . '.jpg';
} else {
  $file_name = 'actualite_debug_' . $file_name;
}

echo "Nom fichier: $file_name\n";
echo "Chemin cible: public://$file_name\n\n";

echo "=== ÉTAPE 3: Sauvegarde fichier ===\n";

try {
  $file_repository = \Drupal::service('file.repository');
  echo "Service file.repository: ✅\n";
  
  $file = $file_repository->writeData($file_data, 'public://' . $file_name);
  
  if ($file) {
    echo "✅ Fichier sauvegardé\n";
    echo "File ID: " . $file->id() . "\n";
    echo "URI: " . $file->getFileUri() . "\n";
    echo "Taille: " . $file->getSize() . " bytes\n";
  } else {
    echo "❌ Échec sauvegarde fichier (writeData returned NULL)\n";
    exit;
  }
} catch (\Exception $e) {
  echo "❌ Exception sauvegarde: " . $e->getMessage() . "\n";
  exit;
}

echo "\n=== ÉTAPE 4: Création média ===\n";

try {
  // Vérifier que le bundle image existe
  $media_types = \Drupal::entityTypeManager()->getStorage('media_type')->loadMultiple();
  echo "Media types disponibles: " . implode(', ', array_keys($media_types)) . "\n";
  
  if (!isset($media_types['image'])) {
    echo "❌ Bundle 'image' non trouvé\n";
    exit;
  }
  
  echo "Bundle 'image': ✅\n";
  
  $media = Media::create([
    'bundle' => 'image',
    'name' => $title,
    'field_media_image' => [
      'target_id' => $file->id(),
      'alt' => $title,
      'title' => $title
    ],
    'status' => 1,
    'langcode' => 'fr',
  ]);
  
  echo "Média créé en mémoire: ✅\n";
  
  $media->save();
  
  echo "✅ Média sauvegardé\n";
  echo "Media ID: " . $media->id() . "\n";
  echo "Media name: " . $media->getName() . "\n";
  
} catch (\Exception $e) {
  echo "❌ Exception média: " . $e->getMessage() . "\n";
  echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
  exit;
}

echo "\n🎉 PROCESSUS COMPLET RÉUSSI !\n";
echo "File ID: " . $file->id() . "\n";
echo "Media ID: " . $media->id() . "\n";