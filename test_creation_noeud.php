<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;
use Drupal\taxonomy\Entity\Term;

/**
 * Test de création automatique de nœud - Version simple
 */

echo "=== TEST CRÉATION AUTOMATIQUE DE NŒUD ===\n";

// Fonction pour trouver les termes de taxonomie par nom
function findTaxonomyTermByName($vocabulary, $name) {
  $query = \Drupal::entityQuery('taxonomy_term')
    ->condition('vid', $vocabulary)
    ->condition('name', $name)
    ->accessCheck(FALSE);
  
  $tids = $query->execute();
  
  if ($tids) {
    return reset($tids);
  }
  
  return null;
}

// Test 1: Vérifier les termes de taxonomie
echo "--- VÉRIFICATION DES TERMES DE TAXONOMIE ---\n";

$secteur_tid = findTaxonomyTermByName('modes_de_transport', 'Aviation Civile');
echo "Terme 'Aviation Civile' trouvé: TID = $secteur_tid\n";

$type_procedure_tid = findTaxonomyTermByName('type_de_procedure', 'Formulaires');
echo "Terme 'Formulaires' trouvé: TID = $type_procedure_tid\n";

// Test 2: Créer un nœud test
if ($secteur_tid && $type_procedure_tid) {
  echo "\n--- CRÉATION D'UN NŒUD TEST ---\n";
  
  $test_title = "TEST - Formulaire de test automatique - " . date('Y-m-d H:i:s');
  
  try {
    $node = Node::create([
      'type' => 'procedure_formulaire',
      'title' => $test_title,
      'field_secteur' => ['target_id' => $secteur_tid],
      'field_type_de_procedure' => ['target_id' => $type_procedure_tid],
      'status' => 1,
      'uid' => 1,
      'langcode' => 'fr',
    ]);
    
    $node->save();
    
    echo "✓ Nœud test créé avec succès!\n";
    echo "  → ID: " . $node->id() . "\n";
    echo "  → Titre: " . $node->getTitle() . "\n";
    echo "  → URL: " . $node->toUrl()->toString() . "\n";
    
    // Supprimer le nœud test
    echo "\n--- NETTOYAGE ---\n";
    $node->delete();
    echo "✓ Nœud test supprimé\n";
    
  } catch (Exception $e) {
    echo "✗ Erreur lors de la création: " . $e->getMessage() . "\n";
  }
} else {
  echo "✗ Impossible de trouver les termes de taxonomie requis\n";
}

echo "\n=== TEST TERMINÉ ===\n";