<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON><PERSON>\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'upload uniquement des documents pour les réglementations Aviation civile
 * Usage: drush php:script upload_documents_aviation_civile.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/mtl/reglementation-dac.csv';

// Dossier des fichiers à uploader
$files_directory = '/var/www/mtl/drive/Portail MTL - Collecte de la réglementaire';

// Vérifier si le fichier CSV existe
if (!file_exists($csv_file_path)) {
  echo "ERREUR: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

// Vérifier si le dossier des fichiers existe
if (!is_dir($files_directory)) {
  echo "ERREUR: Le dossier des fichiers n'existe pas: $files_directory\n";
  return;
}

echo "=== DÉBUT DE L'UPLOAD DES DOCUMENTS AVIATION CIVILE ===\n";
echo "Fichier CSV: $csv_file_path\n";
echo "Dossier des fichiers: $files_directory\n\n";

// Statistiques globales
$stats = [
  'total_lines' => 0,
  'nodes_found' => 0,
  'nodes_not_found' => 0,
  'files_fr_found' => 0,
  'files_fr_not_found' => 0,
  'files_fr_uploaded' => 0,
  'files_fr_existing' => 0,
  'files_fr_errors' => 0,
  'files_ar_found' => 0,
  'files_ar_not_found' => 0,
  'files_ar_uploaded' => 0,
  'files_ar_existing' => 0,
  'files_ar_errors' => 0,
  'nodes_updated' => 0,
];

/**
 * Fonction pour normaliser les noms de fichiers selon les déformations du serveur
 */
function normalizeFilename($filename) {
  $normalized = $filename;
  
  // Étape 1: Remplacer les accents et caractères spéciaux
  $replacements = [
    'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
    'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a',
    'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
    'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
    'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
    'ç' => 'c', 'ñ' => 'n',
    'É' => 'E', 'È' => 'E', 'Ê' => 'E', 'Ë' => 'E',
    'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A',
    'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U',
    'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
    'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O',
    'Ç' => 'C', 'Ñ' => 'N',
  ];
  
  foreach ($replacements as $search => $replace) {
    $normalized = str_replace($search, $replace, $normalized);
  }
  
  // Étape 2: Supprimer les caractères spéciaux et ponctuation (comme le serveur fait)
  // GARDER les underscores qui peuvent être conservés par le serveur
  $normalized = preg_replace('/[°\/\-\(\)\[\]{}|\\\\:;,.<>?!@#$%^&*+=`~"\']/', '', $normalized);
  
  // Étape 3: Supprimer TOUS les espaces (le serveur les supprime)
  $normalized = preg_replace('/\s+/', '', $normalized);
  
  // Étape 4: Supprimer les caractères non-ASCII restants
  $normalized = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $normalized);
  
  return $normalized;
}

/**
 * Fonction pour créer une version flexible du nom de fichier (gestion des cas complexes)
 */
function createFlexibleMatch($filename) {
  $base = pathinfo($filename, PATHINFO_FILENAME);
  $ext = pathinfo($filename, PATHINFO_EXTENSION);
  
  // Normaliser sans supprimer les underscores
  $normalized = $base;
  
  // Remplacer accents
  $replacements = [
    'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
    'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a',
    'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
    'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
    'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
    'ç' => 'c', 'ñ' => 'n',
    'É' => 'E', 'È' => 'E', 'Ê' => 'E', 'Ë' => 'E',
    'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A',
    'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U',
    'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
    'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O',
    'Ç' => 'C', 'Ñ' => 'N',
  ];
  
  foreach ($replacements as $search => $replace) {
    $normalized = str_replace($search, $replace, $normalized);
  }
  
  // Supprimer caractères spéciaux SAUF underscores
  $normalized = preg_replace('/[°\/\-\(\)\[\]{}|\\\\:;,.<>?!@#$%^&*+=`~"\' ]/', '', $normalized);
  
  // Créer version sans underscores aussi
  $without_underscores = str_replace('_', '', $normalized);
  
  return [
    'with_underscores' => $normalized . '.' . $ext,
    'without_underscores' => $without_underscores . '.' . $ext,
    'pattern' => substr($without_underscores, 0, 30), // Pour matching partiel
    'short_pattern' => substr($without_underscores, 0, 15) // Pattern court pour cas difficiles
  ];
}

/**
 * Fonction pour trouver un fichier à partir du chemin du CSV avec gestion avancée des caractères spéciaux
 */
function findFileFromCSVPath($base_directory, $csv_path) {
  if (empty($csv_path)) {
    echo "  → Chemin CSV vide\n";
    return null;
  }
  
  echo "  → Chemin CSV: $csv_path\n";
  
  // Essayer d'abord le chemin complet tel que dans le CSV
  // Vérifier si le chemin CSV commence déjà par le nom du dossier de base
  $base_dir_name = basename($base_directory);
  if (strpos($csv_path, $base_dir_name) === 0) {
    // Le chemin CSV commence par le nom du dossier de base, utiliser le répertoire parent
    $full_path = dirname($base_directory) . '/' . $csv_path;
  } else {
    $full_path = $base_directory . '/' . $csv_path;
  }
  echo "  → Tentative chemin complet: $full_path\n";
  
  if (file_exists($full_path)) {
    echo "  ✓ Fichier trouvé par chemin complet: $full_path\n";
    return $full_path;
  }
  
  // Si le chemin complet ne marche pas, extraire juste le nom du fichier
  $filename = basename($csv_path);
  $filename_normalized = normalizeFilename($filename);
  
  echo "  → Recherche récursive du fichier: $filename\n";
  if ($filename !== $filename_normalized) {
    echo "  → Nom normalisé: $filename_normalized\n";
  }
  
  $iterator = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($base_directory, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::LEAVES_ONLY
  );
  
  foreach ($iterator as $file) {
    if ($file->isFile()) {
      $server_filename = $file->getFilename();
      
      // Essayer correspondance exacte d'abord
      if ($server_filename === $filename) {
        echo "  ✓ Fichier trouvé par correspondance exacte: " . $file->getPathname() . "\n";
        return $file->getPathname();
      }
      
      // Ensuite correspondance normalisée standard
      $server_filename_normalized = normalizeFilename($server_filename);
      if ($server_filename_normalized === $filename_normalized) {
        echo "  ✓ Fichier trouvé par correspondance normalisée: " . $file->getPathname() . "\n";
        echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
        return $file->getPathname();
      }
      
      // Essayer correspondance flexible (cas complexes avec déformations serveur)
      $filename_flexible = createFlexibleMatch($filename);
      $server_flexible = createFlexibleMatch($server_filename);
      
      // Extensions doivent correspondre
      if (pathinfo($server_filename, PATHINFO_EXTENSION) === pathinfo($filename, PATHINFO_EXTENSION)) {
        
        // 1. Correspondance exacte flexible
        if ($server_flexible['with_underscores'] === $filename_flexible['with_underscores'] || 
            $server_flexible['without_underscores'] === $filename_flexible['without_underscores']) {
          echo "  ✓ Fichier trouvé par correspondance flexible exacte: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          return $file->getPathname();
        }
        
        // 2. Le serveur contient le pattern du CSV (cas de troncature) - PATTERN LONG SEULEMENT
        if (strlen($filename_flexible['pattern']) > 15 && 
            strpos($server_flexible['without_underscores'], $filename_flexible['pattern']) !== false) {
          echo "  ✓ Fichier trouvé par pattern CSV dans serveur: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          echo "    Pattern CSV: {$filename_flexible['pattern']}\n";
          return $file->getPathname();
        }
        
        // 3. Le CSV contient le pattern du serveur (cas inverse) - PATTERN LONG SEULEMENT  
        if (strlen($server_flexible['pattern']) > 15 && 
            strpos($filename_flexible['without_underscores'], $server_flexible['pattern']) !== false) {
          echo "  ✓ Fichier trouvé par pattern serveur dans CSV: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          echo "    Pattern serveur: {$server_flexible['pattern']}\n";
          return $file->getPathname();
        }
        
        // 4. Correspondance par pattern court (pour cas très difficiles) - MINIMUM 15 CHARS
        if (strlen($filename_flexible['short_pattern']) > 15 && 
            (strpos($server_flexible['without_underscores'], $filename_flexible['short_pattern']) !== false ||
             strpos($filename_flexible['without_underscores'], $server_flexible['short_pattern']) !== false)) {
          echo "  ✓ Fichier trouvé par pattern court: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          echo "    Pattern court utilisé: {$filename_flexible['short_pattern']}\n";
          return $file->getPathname();
        }
      }
    }
  }
  
  echo "  ✗ Fichier non trouvé: $csv_path\n";
  echo "    Recherché: $filename (normalisé: $filename_normalized)\n";
  return null;
}

/**
 * Fonction pour uploader un fichier PDF dans Drupal
 */
function uploadFileToDrupal($pdf_filename, $files_directory, $language = 'fr') {
  global $stats;
  
  if (empty($pdf_filename)) {
    return null;
  }
  
  echo "  === UPLOAD $language ===\n";
  echo "  Nom du fichier: $pdf_filename\n";
  
  // Nettoyer le chemin du fichier
  $pdf_filename = trim($pdf_filename);
  
  // Chercher le fichier à partir du chemin CSV
  $file_path = findFileFromCSVPath($files_directory, $pdf_filename);
  
  if (!$file_path || !file_exists($file_path)) {
    echo "  ✗ Fichier non accessible: $pdf_filename\n";
    $stats["files_{$language}_not_found"]++;
    return null;
  }
  
  $stats["files_{$language}_found"]++;
  
  try {
    // Vérifier si le fichier existe déjà dans Drupal par nom
    $existing_files = \Drupal::entityTypeManager()
      ->getStorage('file')
      ->loadByProperties(['filename' => basename($file_path)]);
    
    if ($existing_files) {
      $existing_file = reset($existing_files);
      echo "  ⚡ Fichier existant trouvé dans Drupal: " . $existing_file->getFilename() . " (ID: " . $existing_file->id() . ")\n";
      $stats["files_{$language}_existing"]++;
      return $existing_file;
    }
    
    // Préparer le répertoire de destination
    $destination_dir = 'public://reglementation/aviation-civile/';
    \Drupal::service('file_system')->prepareDirectory($destination_dir, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
    
    echo "  → Lecture du fichier: $file_path\n";
    $file_data = file_get_contents($file_path);
    
    if ($file_data === FALSE) {
      echo "  ✗ Erreur lors de la lecture du fichier: $file_path\n";
      $stats["files_{$language}_errors"]++;
      return null;
    }
    
    echo "  → Taille du fichier: " . strlen($file_data) . " bytes\n";
    
    // Copier le fichier vers Drupal
    $destination = $destination_dir . basename($file_path);
    echo "  → Destination: $destination\n";
    
    // Créer l'entité fichier
    $file = \Drupal::service('file.repository')->writeData($file_data, $destination, FileSystemInterface::EXISTS_REPLACE);
    
    if ($file) {
      $file->setPermanent();
      $file->save();
      echo "  ✓ Fichier uploadé avec succès: " . $file->getFilename() . " (ID: " . $file->id() . ")\n";
      echo "  → URI: " . $file->getFileUri() . "\n";
      $stats["files_{$language}_uploaded"]++;
      return $file;
    } else {
      echo "  ✗ Erreur lors de la création du fichier dans Drupal\n";
      $stats["files_{$language}_errors"]++;
      return null;
    }
    
  } catch (Exception $e) {
    echo "  ✗ Exception lors de l'upload: " . $e->getMessage() . "\n";
    $stats["files_{$language}_errors"]++;
    return null;
  }
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  echo "  → Recherche du nœud: $title\n";
  
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    $node = Node::load(reset($nids));
    echo "  ✓ Nœud trouvé par titre: ID " . $node->id() . "\n";
    return $node;
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    echo "  → Recherche alternative par numéro: $numero\n";
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      $node = Node::load(reset($nids));
      echo "  ✓ Nœud trouvé par numéro: ID " . $node->id() . "\n";
      return $node;
    }
  }
  
  echo "  ✗ Nœud non trouvé\n";
  return null;
}

// Lire le fichier CSV complet
$csv_content = file_get_contents($csv_file_path);
if (!$csv_content) {
  echo "ERREUR: Impossible de lire le fichier CSV\n";
  return;
}

// Diviser en lignes et nettoyer les retours à la ligne dans les cellules
$lines = explode("\n", $csv_content);

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-19)
$data_lines = array_slice($lines, 19);

// Fonction pour parser une ligne CSV malformée
function parseCSVLine($line) {
  $data = str_getcsv($line, ',', '"');
  
  foreach ($data as &$cell) {
    $cell = trim(str_replace(["\n", "\r"], ' ', $cell));
  }
  
  return $data;
}

// Reconstruire les lignes brisées par les retours à la ligne
$reconstructed_lines = [];
$current_line = '';

foreach ($data_lines as $line) {
  $line = trim($line);
  
  if (empty($line)) {
    continue;
  }
  
  $current_line .= ($current_line ? ' ' : '') . $line;
  $quote_count = substr_count($current_line, '"');
  
  if ($quote_count % 2 == 0) {
    $reconstructed_lines[] = $current_line;
    $current_line = '';
  }
}

if (!empty($current_line)) {
  $reconstructed_lines[] = $current_line;
}

$line_number = 19; // Commencer après les en-têtes

// Traiter chaque ligne de données
foreach ($reconstructed_lines as $line) {
  $line_number++;
  $stats['total_lines']++;
  
  if (empty(trim($line))) {
    continue;
  }
  
  $data = parseCSVLine($line);
  
  // Vérifier que la ligne contient toutes les colonnes attendues
  if (count($data) < 11) {
    echo "LIGNE $line_number: Ignorée (colonnes incomplètes: " . count($data) . ")\n\n";
    continue;
  }
  
  // Vérifier que la ligne contient des données valides
  if (empty($data[5])) { // Intitulé FR
    echo "LIGNE $line_number: Ignorée (titre français vide)\n\n";
    continue;
  }
  
  echo "=== LIGNE $line_number ===\n";
  
  try {
    // Extraire les données
    $numero = trim($data[4]);
    $titre_fr = trim($data[5]);
    $pdf_fr = trim($data[8]);
    $pdf_ar = trim($data[9]);
    
    echo "Titre: $titre_fr\n";
    echo "Numéro: $numero\n";
    echo "PDF FR: $pdf_fr\n";
    echo "PDF AR: $pdf_ar\n";
    
    // Chercher le nœud existant
    $existing_node = getExistingNode($titre_fr, $numero);
    
    if (!$existing_node) {
      echo "✗ Nœud non trouvé - IGNORÉ\n\n";
      $stats['nodes_not_found']++;
      continue;
    }
    
    $stats['nodes_found']++;
    echo "✓ Nœud trouvé: ID " . $existing_node->id() . "\n";
    
    // Vérifier les fichiers actuels du nœud
    if ($existing_node->hasField('field_lien_telechargement')) {
      $current_files = $existing_node->get('field_lien_telechargement')->getValue();
      echo "Fichiers actuels dans le nœud: " . count($current_files) . "\n";
      
      foreach ($current_files as $file_info) {
        if (isset($file_info['target_id'])) {
          $file = File::load($file_info['target_id']);
          if ($file) {
            echo "  - " . $file->getFilename() . " (ID: " . $file->id() . ")\n";
          }
        }
      }
    }
    
    // Uploader les fichiers
    $file_fr = null;
    $file_ar = null;
    $files_to_add = [];
    
    if (!empty($pdf_fr)) {
      $file_fr = uploadFileToDrupal($pdf_fr, $files_directory, 'fr');
      if ($file_fr) {
        $files_to_add[] = [
          'target_id' => $file_fr->id(),
          'description' => 'Version française',
        ];
      }
    }
    
    if (!empty($pdf_ar)) {
      $file_ar = uploadFileToDrupal($pdf_ar, $files_directory, 'ar');
      if ($file_ar) {
        $files_to_add[] = [
          'target_id' => $file_ar->id(),
          'description' => 'Version arabe',
        ];
      }
    }
    
    // Mettre à jour le nœud avec les nouveaux fichiers
    if (!empty($files_to_add) && $existing_node->hasField('field_lien_telechargement')) {
      $current_files = $existing_node->get('field_lien_telechargement')->getValue();
      $existing_file_ids = array_column($current_files, 'target_id');
      
      $new_files = [];
      foreach ($files_to_add as $file_info) {
        if (!in_array($file_info['target_id'], $existing_file_ids)) {
          $new_files[] = $file_info;
        }
      }
      
      if (!empty($new_files)) {
        $all_files = array_merge($current_files, $new_files);
        $existing_node->set('field_lien_telechargement', $all_files);
        $existing_node->save();
        
        echo "✓ Nœud mis à jour avec " . count($new_files) . " nouveau(x) fichier(s)\n";
        echo "Total fichiers dans le nœud: " . count($all_files) . "\n";
        $stats['nodes_updated']++;
      } else {
        echo "⚡ Tous les fichiers étaient déjà présents dans le nœud\n";
      }
    } else if (empty($files_to_add)) {
      echo "✗ Aucun fichier à ajouter\n";
    }
    
  } catch (Exception $e) {
    echo "ERREUR ligne $line_number: " . $e->getMessage() . "\n";
  }
  
  echo "\n";
}

// Afficher le résumé détaillé
echo "\n";
echo "==================================================\n";
echo "            RÉSUMÉ DÉTAILLÉ FINAL\n";
echo "==================================================\n";
echo "Total lignes traitées: " . $stats['total_lines'] . "\n";
echo "\n--- NŒUDS ---\n";
echo "Nœuds trouvés: " . $stats['nodes_found'] . "\n";
echo "Nœuds non trouvés: " . $stats['nodes_not_found'] . "\n";
echo "Nœuds mis à jour: " . $stats['nodes_updated'] . "\n";
echo "\n--- FICHIERS FRANÇAIS ---\n";
echo "Fichiers FR trouvés sur disque: " . $stats['files_fr_found'] . "\n";
echo "Fichiers FR non trouvés: " . $stats['files_fr_not_found'] . "\n";
echo "Fichiers FR uploadés (nouveaux): " . $stats['files_fr_uploaded'] . "\n";
echo "Fichiers FR existants (réutilisés): " . $stats['files_fr_existing'] . "\n";
echo "Erreurs upload FR: " . $stats['files_fr_errors'] . "\n";
echo "\n--- FICHIERS ARABES ---\n";
echo "Fichiers AR trouvés sur disque: " . $stats['files_ar_found'] . "\n";
echo "Fichiers AR non trouvés: " . $stats['files_ar_not_found'] . "\n";
echo "Fichiers AR uploadés (nouveaux): " . $stats['files_ar_uploaded'] . "\n";
echo "Fichiers AR existants (réutilisés): " . $stats['files_ar_existing'] . "\n";
echo "Erreurs upload AR: " . $stats['files_ar_errors'] . "\n";
echo "\n--- TOTAUX ---\n";
echo "Total fichiers trouvés: " . ($stats['files_fr_found'] + $stats['files_ar_found']) . "\n";
echo "Total fichiers uploadés: " . ($stats['files_fr_uploaded'] + $stats['files_ar_uploaded']) . "\n";
echo "Total fichiers existants: " . ($stats['files_fr_existing'] + $stats['files_ar_existing']) . "\n";
echo "Total erreurs: " . ($stats['files_fr_errors'] + $stats['files_ar_errors']) . "\n";
echo "==================================================\n";
echo "Upload terminé!\n";