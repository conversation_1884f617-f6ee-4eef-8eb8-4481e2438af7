<?php

use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Script simplifié pour trouver les bonnes colonnes PDF
 */

$xlsm_file_path = '/var/www/html/mtl/reglementation.xlsm';

try {
  $spreadsheet = IOFactory::load($xlsm_file_path);
  $worksheet = $spreadsheet->getSheetByName('Transport routier');
  
  echo "=== RECHERCHE DES COLONNES PDF ===\n";
  
  // Vérifier les colonnes K à T pour les PDF
  for ($col = 'K'; $col <= 'T'; $col++) {
    $header = trim($worksheet->getCell($col . '19')->getValue() ?? '');
    $value20 = trim($worksheet->getCell($col . '20')->getValue() ?? '');
    
    echo "Colonne $col: En-tête='$header', Valeur='$value20'\n";
    
    // Si la valeur ressemble à un nom de fichier PDF
    if (strpos($value20, '.pdf') !== false) {
      echo "  *** POTENTIEL CHEMIN PDF TROUVÉ ***\n";
    }
  }
  
} catch (Exception $e) {
  echo "Erreur: " . $e->getMessage() . "\n";
}