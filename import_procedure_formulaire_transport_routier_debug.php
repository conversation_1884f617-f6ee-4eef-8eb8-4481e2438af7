<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation des procédures et formulaires Transport routier avec debug
 * Usage: drush php:script import_procedure_formulaire_transport_routier_debug.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/html/mtl/Canva Procédures et Formulaires.xlsx - Transport routier.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "Début de l'importation des procédures et formulaires Transport routier (mode debug)...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 */
function getTaxonomyTermByName($name, $vocabulary) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    return reset($terms);
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $name,
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $type = 'procedure_formulaire') {
  $query = \Drupal::entityQuery('node')
    ->condition('type', $type)
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  return null;
}

// Récupérer le terme Transport Routier
$transport_routier_sector = getTaxonomyTermByName('Transport Routier', 'modes_de_transport');

if (!$transport_routier_sector) {
  $transport_routier_sector = getTaxonomyTermByName('Transport routier', 'modes_de_transport');
}

if (!$transport_routier_sector) {
  echo "Erreur: Le terme 'Transport Routier' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

echo "Secteur Transport Routier trouvé: ID {$transport_routier_sector->id()}\n";

// Lire le fichier CSV proprement
if (($handle = fopen($csv_file_path, 'r')) === FALSE) {
  echo "Erreur: Impossible d'ouvrir le fichier CSV\n";
  return;
}

$imported = 0;
$updated = 0;
$errors = 0;
$skipped = 0;
$line_number = 0;

// Ignorer la ligne d'en-tête
fgetcsv($handle);
$line_number++;

echo "En-tête ignoré, début du traitement des données...\n";

while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE) {
  $line_number++;
  
  // Debug: afficher la structure de la ligne
  echo "\n--- LIGNE $line_number ---\n";
  echo "Colonnes trouvées: " . count($data) . "\n";
  
  // Si la ligne a moins de 8 colonnes, l'ignorer
  if (count($data) < 8) {
    echo "IGNORÉ: Pas assez de colonnes (" . count($data) . ")\n";
    $skipped++;
    continue;
  }
  
  // Nettoyer et extraire les données
  $type_fr = trim($data[0] ?? '');
  $type_ar = trim($data[1] ?? '');
  $domaine_fr = trim($data[2] ?? '');
  $domaine_ar = trim($data[3] ?? '');
  $titre_fr = trim($data[4] ?? '');
  $titre_ar = trim($data[5] ?? '');
  $pdf_fr = trim($data[6] ?? '');
  $pdf_ar = trim($data[7] ?? '');
  
  echo "Type FR: '$type_fr'\n";
  echo "Domaine FR: '$domaine_fr'\n";
  echo "Titre FR: '$titre_fr'\n";
  
  // Vérifier si c'est une ligne d'exemple (les lignes 2-16 du CSV original)
  if (empty($type_fr) || $type_fr === 'Ex' || strpos($type_fr, 'Procédures') !== false && strpos($domaine_fr, 'EX') !== false) {
    echo "IGNORÉ: Ligne d'exemple détectée\n";
    $skipped++;
    continue;
  }
  
  // Vérifier les données minimales
  if (empty($titre_fr)) {
    echo "IGNORÉ: Titre français vide\n";
    $skipped++;
    continue;
  }
  
  if (empty($type_fr) || empty($domaine_fr)) {
    echo "IGNORÉ: Type ou domaine vide\n";
    $skipped++;
    continue;
  }
  
  try {
    echo "TRAITEMENT: '$titre_fr'\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, 'procedure_formulaire');
    
    // Récupérer ou créer le terme de type
    $type_term = getTaxonomyTermByName($type_fr, 'type');
    
    // Récupérer ou créer le domaine d'activité
    $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites');
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'procedure_formulaire',
      'title' => $titre_fr,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $transport_routier_sector->id()],
      'field_domaine_d_activite' => ['target_id' => $domaine_term->id()],
      'status' => 1,
    ];
    
    if ($existing_node) {
      // Mettre à jour
      foreach ($node_data as $field => $value) {
        if ($field !== 'type') {
          $existing_node->set($field, $value);
        }
      }
      $existing_node->save();
      
      // Ajouter traduction arabe
      if (!empty($titre_ar)) {
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($titre_ar);
          $ar_translation->save();
        } else {
          $existing_node->addTranslation('ar', ['title' => $titre_ar]);
          $existing_node->save();
        }
      }
      
      echo "SUCCÈS: Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer nouveau
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter traduction arabe
      if (!empty($titre_ar)) {
        $node->addTranslation('ar', ['title' => $titre_ar]);
        $node->save();
      }
      
      echo "SUCCÈS: Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "ERREUR: " . $e->getMessage() . "\n";
    $errors++;
  }
}

fclose($handle);

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Lignes ignorées: $skipped\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";