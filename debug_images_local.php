<?php

/**
 * Script de debug LOCAL pour analyser le dossier images et adapter l'import
 * Usage: drush php:script debug_images_local.php
 */

$json_file_path = '/var/www/html/mtl/output-fr.json';
$local_images_folder = '/var/www/html/mtl/images-actualites/';

echo "=== DEBUG LOCAL COMPLET - TOUTES LES ACTUALITÉS ===\n\n";

// Vérifier si le dossier existe
if (!is_dir($local_images_folder)) {
    echo "❌ ERREUR: Le dossier $local_images_folder n'existe pas!\n";
    return;
}

// Lister tous les fichiers du dossier local
$local_files = [];
$files = scandir($local_images_folder);
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..' && is_file($local_images_folder . $file)) {
        $local_files[] = $file;
    }
}

echo "📁 Dossier analysé: $local_images_folder\n";
echo "📊 Fichiers trouvés: " . count($local_files) . "\n\n";

if (empty($local_files)) {
    echo "⚠️ Aucun fichier trouvé dans le dossier!\n";
    return;
}

// Analyse des patterns de noms de fichiers
echo "🔍 === ANALYSE DES PATTERNS DE FICHIERS ===\n";
$extensions = [];
$has_spaces = 0;
$has_special_chars = 0;
$has_unicode = 0;
$has_underscores = 0;
$has_dates = 0;

foreach ($local_files as $file) {
    // Extension
    $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    $extensions[$ext] = ($extensions[$ext] ?? 0) + 1;
    
    // Patterns
    if (strpos($file, ' ') !== false) $has_spaces++;
    if (strpos($file, '_') !== false) $has_underscores++;
    if (preg_match('/[àáâãäåèéêëìíîïòóôõöùúûüçñ]/i', $file)) $has_unicode++;
    if (preg_match('/[%#]/', $file)) $has_special_chars++;
    if (preg_match('/\d{4}-\d{2}-\d{2}/', $file)) $has_dates++;
}

echo "Extensions trouvées:\n";
foreach ($extensions as $ext => $count) {
    echo "  • .$ext : $count fichiers\n";
}

echo "\nPatterns détectés:\n";
echo "  • Avec espaces: $has_spaces fichiers\n";
echo "  • Avec underscores: $has_underscores fichiers\n";
echo "  • Avec caractères Unicode: $has_unicode fichiers\n";
echo "  • Avec caractères spéciaux (%#): $has_special_chars fichiers\n";
echo "  • Avec dates: $has_dates fichiers\n\n";

// Lire le JSON et analyser les correspondances
$json_content = file_get_contents($json_file_path);
$data = json_decode($json_content, true);

// Fonctions de nettoyage améliorées
function cleanFilename($url) {
    $filename = basename(parse_url($url, PHP_URL_PATH));
    
    // Multiple URL decoding
    $filename = urldecode($filename);
    $filename = urldecode($filename);
    
    // Clean invalid characters
    $filename = preg_replace('/[<>:"\/\\|?*]/', '', $filename);
    $filename = preg_replace('/\s+/', ' ', $filename);
    $filename = trim($filename);
    
    return $filename;
}

function removeAccents($string) {
    $string = preg_replace_callback('/#U([0-9A-Fa-f]{4})/', function($matches) {
        return mb_convert_encoding(pack('H*', $matches[1]), 'UTF-8', 'UCS-2BE');
    }, $string);
    
    $accents = [
        'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a',
        'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e',
        'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
        'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
        'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
        'ç' => 'c', 'ñ' => 'n',
        'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A',
        'È' => 'E', 'É' => 'E', 'Ê' => 'E', 'Ë' => 'E',
        'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
        'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O',
        'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U',
        'Ç' => 'C', 'Ñ' => 'N'
    ];
    
    return strtr($string, $accents);
}

// Fonction de matching avancé
function findBestMatch($target_filename, $local_files) {
    $target_lower = strtolower($target_filename);
    
    // 1. Correspondance exacte
    foreach ($local_files as $file) {
        if ($file === $target_filename) {
            return ['file' => $file, 'method' => 'Exacte', 'confidence' => 100];
        }
    }
    
    // 2. Correspondance insensible à la casse
    foreach ($local_files as $file) {
        if (strtolower($file) === $target_lower) {
            return ['file' => $file, 'method' => 'Casse', 'confidence' => 95];
        }
    }
    
    // 3. Avec espaces remplacés par underscores
    $target_underscore = str_replace(' ', '_', $target_lower);
    foreach ($local_files as $file) {
        if (strtolower($file) === $target_underscore) {
            return ['file' => $file, 'method' => 'Underscore', 'confidence' => 90];
        }
    }
    
    // 4. Sans espaces du tout
    $target_nospace = str_replace(' ', '', $target_lower);
    foreach ($local_files as $file) {
        if (strtolower(str_replace(' ', '', $file)) === $target_nospace) {
            return ['file' => $file, 'method' => 'Sans espaces', 'confidence' => 85];
        }
    }
    
    // 5. Extensions différentes
    $target_base = pathinfo($target_filename, PATHINFO_FILENAME);
    foreach ($local_files as $file) {
        $file_base = pathinfo($file, PATHINFO_FILENAME);
        if (strtolower($target_base) === strtolower($file_base)) {
            return ['file' => $file, 'method' => 'Extension diff', 'confidence' => 80];
        }
    }
    
    // 6. Recherche fuzzy (similarité)
    $best_match = null;
    $best_similarity = 0;
    
    foreach ($local_files as $file) {
        similar_text($target_lower, strtolower($file), $similarity);
        if ($similarity > $best_similarity && $similarity > 70) {
            $best_similarity = $similarity;
            $best_match = $file;
        }
    }
    
    if ($best_match) {
        return ['file' => $best_match, 'method' => 'Fuzzy', 'confidence' => $best_similarity];
    }
    
    return null;
}

// Test avec TOUTES les images du JSON
echo "🎯 === TEST COMPLET AVEC TOUTES LES ACTUALITÉS ===\n";

$all_data = json_decode($json_content, true);
echo "📊 Total actualités dans le JSON: " . count($all_data) . "\n\n";
$matches_found = 0;
$total_images = 0;
$detailed_results = [];

foreach ($data as $index => $item) {
    if (!empty($item['image'])) {
        $total_images++;
        $url = $item['image'];
        $clean_filename = cleanFilename($url);
        
        if (empty($clean_filename) || strlen($clean_filename) < 3) {
            continue; // Skip invalid filenames
        }
        
        // Test sans accents aussi
        $no_accents = removeAccents($clean_filename);
        
        // Chercher correspondance
        $match = findBestMatch($clean_filename, $local_files);
        if (!$match) {
            $match = findBestMatch($no_accents, $local_files);
        }
        
        if ($match) {
            $matches_found++;
            if ($match['confidence'] < 100) {
                $detailed_results[] = [
                    'expected' => $clean_filename,
                    'found' => $match['file'],
                    'method' => $match['method'],
                    'confidence' => $match['confidence'],
                    'url' => $url
                ];
            }
        } else {
            $detailed_results[] = [
                'expected' => $clean_filename,
                'found' => null,
                'method' => 'NON TROUVÉ',
                'confidence' => 0,
                'url' => $url
            ];
        }
        
        // Pas de limite - traiter TOUTES les actualités
    }
}

echo "\n=== RÉSULTATS ===\n";
echo "Total images testées: $total_images\n";
echo "Correspondances trouvées: $matches_found\n";
echo "Taux de succès: " . round(($matches_found/$total_images)*100, 1) . "%\n\n";

if (!empty($detailed_results)) {
    echo "🔧 === DÉTAILS DES CORRESPONDANCES ===\n";
    
    $not_found = [];
    $non_exact = [];
    
    foreach ($detailed_results as $result) {
        if ($result['found'] === null) {
            $not_found[] = $result;
        } else {
            $non_exact[] = $result;
        }
    }
    
    if (!empty($non_exact)) {
        echo "✅ Correspondances non-exactes (" . count($non_exact) . "):\n";
        foreach ($non_exact as $result) {
            echo "• {$result['expected']} → {$result['found']}\n";
            echo "  Méthode: {$result['method']} (confiance: {$result['confidence']}%)\n\n";
        }
    }
    
    if (!empty($not_found)) {
        echo "❌ Images NON TROUVÉES (" . count($not_found) . "):\n";
        foreach ($not_found as $result) {
            echo "• {$result['expected']}\n";
            echo "  URL: {$result['url']}\n";
            
            // Montrer les 3 fichiers les plus similaires
            $similarities = [];
            foreach ($local_files as $file) {
                similar_text(strtolower($result['expected']), strtolower($file), $similarity);
                if ($similarity > 30) {
                    $similarities[] = ['file' => $file, 'similarity' => $similarity];
                }
            }
            usort($similarities, function($a, $b) { return $b['similarity'] <=> $a['similarity']; });
            for ($i = 0; $i < 3 && $i < count($similarities); $i++) {
                echo "   Similaire: {$similarities[$i]['file']} ({$similarities[$i]['similarity']}%)\n";
            }
            echo "\n";
        }
        
        echo "📋 === LISTE SIMPLE DES FICHIERS MANQUANTS ===\n";
        foreach ($not_found as $result) {
            echo $result['expected'] . "\n";
        }
        echo "\n";
    }
}

// Recommandations pour améliorer l'import
echo "💡 === RECOMMANDATIONS ===\n";
if ($has_underscores > $has_spaces) {
    echo "• Ajouter conversion espaces → underscores\n";
}
if ($has_special_chars > 0) {
    echo "• Améliorer gestion caractères spéciaux\n";
}
if (isset($extensions['jpg']) && isset($extensions['jpeg'])) {
    echo "• Ajouter test extensions multiples (jpg/jpeg)\n";
}

echo "\n🔧 DEBUG LOCAL TERMINÉ !\n";