<?php

use Dr<PERSON>al\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation des réglementations Marine marchande depuis un fichier CSV
 * Usage: drush php:script import_reglementation_marine_marchande.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/mtl/Canvas Réglementation - Marine marchande.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "Début de l'importation des réglementations Marine marchande...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 */
function getTaxonomyTermByName($name, $vocabulary) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    return reset($terms);
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $name,
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour parser la date du CSV
 */
function parseDate($date_string) {
  if (empty($date_string)) {
    return null;
  }
  
  $date_string = trim($date_string);
  
  // Dictionnaire des mois français (avec et sans accents)
  $months_fr = [
    'jan' => '01', 'janv' => '01', 'janvier' => '01',
    'fév' => '02', 'fev' => '02', 'févr' => '02', 'fevr' => '02', 'février' => '02',
    'mar' => '03', 'mars' => '03',
    'avr' => '04', 'avril' => '04',
    'mai' => '05',
    'jui' => '06', 'juin' => '06',
    'jul' => '07', 'juil' => '07', 'juillet' => '07',
    'aoû' => '08', 'aou' => '08', 'août' => '08', 'aout' => '08',
    'sep' => '09', 'sept' => '09', 'septembre' => '09',
    'oct' => '10', 'octobre' => '10',
    'nov' => '11', 'novembre' => '11',
    'déc' => '12', 'dec' => '12', 'décembre' => '12'
  ];
  
  // Format "d/m/yyyy" ou "dd/mm/yyyy" (ex: "4/5/1934", "03/08/1962")
  if (preg_match('#(\d{1,2})/(\d{1,2})/(\d{4})#', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
    $year = $matches[3];
    return "$year-$month-$day";
  }
  
  // Format avec tirets (ex: "3-mai-95", "3-août-62", "26-mai-19", "19-août-10")
  if (preg_match('/(\d{1,2})[-](\w+)[-](\d{2,4})/', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Normaliser les accents
    $month_name = str_replace(['é', 'è', 'û'], ['e', 'e', 'u'], $month_name);
    
    // Convertir année à 2 chiffres en 4 chiffres
    if (strlen($year) == 2) {
      $year = (intval($year) > 50) ? "19$year" : "20$year";
    }
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  // Format avec points et espaces (ex: "6 jui. 2002", "4 avr. 2013", "21 fév. 2013", "20 fév. 2020")
  if (preg_match('/(\d{1,2})\s+(\w+)\.?\s+(\d{4})/', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Normaliser les accents
    $month_name = str_replace(['é', 'è'], ['e', 'e'], $month_name);
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  // Format avec espaces sans points (ex: "30 jan 1974", "27 mar 1959", "26 fév. 1937")
  if (preg_match('/(\d{1,2})\s+(\w+)\.?\s+(\d{4})/', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Normaliser les accents
    $month_name = str_replace(['é', 'è'], ['e', 'e'], $month_name);
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  // Dernière tentative: normaliser tous les accents et espaces
  $clean_date = preg_replace('/\s+/', ' ', trim($date_string));
  $clean_date = str_replace(['é', 'è', 'ê', 'û', 'ù', 'à', 'ç'], ['e', 'e', 'e', 'u', 'u', 'a', 'c'], $clean_date);
  
  // Format flexible avec normalisation
  if (preg_match('/(\d{1,2})[-\s]+(\w+)[-\s\.]*(\d{2,4})/', $clean_date, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Convertir année à 2 chiffres en 4 chiffres
    if (strlen($year) == 2) {
      $year = (intval($year) > 50) ? "19$year" : "20$year";
    }
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  echo "Date non reconnue: '$date_string' (normalisée: '$clean_date')\n";
  return null;
}

/**
 * Fonction pour créer ou récupérer un fichier
 */
function createOrGetFile($filename, $pdf_directory = 'web/modules/custom/import_reglementation/pdf/') {
  if (empty($filename)) {
    return null;
  }
  
  $file_path = $pdf_directory . $filename;
  
  // Vérifier si le fichier existe physiquement
  if (!file_exists($file_path)) {
    // Ignorer silencieusement les fichiers manquants
    return null;
  }
  
  // Rechercher le fichier existant dans Drupal
  $files = \Drupal::entityTypeManager()
    ->getStorage('file')
    ->loadByProperties(['filename' => $filename]);
  
  if ($files) {
    return reset($files);
  }
  
  // Créer une nouvelle entité fichier
  $file_system = \Drupal::service('file_system');
  $destination = 'public://reglementation/pdf/' . $filename;
  
  // Créer le répertoire de destination s'il n'existe pas
  $directory = dirname($destination);
  $file_system->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
  
  // Copier le fichier
  $uri = $file_system->copy($file_path, $destination, FileSystemInterface::EXISTS_REPLACE);
  
  if ($uri) {
    $file = File::create([
      'filename' => $filename,
      'uri' => $uri,
      'status' => 1,
    ]);
    $file->save();
    echo "Fichier créé: $filename\n";
    return $file;
  }
  
  return null;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      return Node::load(reset($nids));
    }
  }
  
  return null;
}

// Récupérer le secteur Marine marchande
$marine_sector = getTaxonomyTermByName('Marine marchande', 'modes_de_transport');

if (!$marine_sector) {
  echo "Erreur: Le terme 'Marine marchande' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

// Lire et parser le fichier CSV
$handle = fopen($csv_file_path, 'r');
if (!$handle) {
  echo "Erreur: Impossible d'ouvrir le fichier CSV\n";
  return;
}

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-16)
$line_number = 0;
while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE && $line_number < 16) {
  $line_number++;
}

$imported = 0;
$updated = 0;
$errors = 0;

// Traiter chaque ligne de données
while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE) {
  $line_number++;
  
  // Vérifier que la ligne contient des données valides
  if (empty($data[5]) || empty($data[0]) || count($data) < 7) { // Intitulé FR, Type FR et colonnes minimum
    echo "Ligne $line_number ignorée: données insuffisantes\n";
    continue;
  }
  
  // Vérifier que ce n'est pas une ligne vide ou avec seulement des virgules
  $non_empty_fields = array_filter($data, function($field) {
    return !empty(trim($field));
  });
  
  if (count($non_empty_fields) < 3) {
    echo "Ligne $line_number ignorée: trop peu de données\n";
    continue;
  }
  
  try {
    // Extraire les données
    $type_fr = trim($data[0]);
    $type_ar = trim($data[1]);
    $domaine_fr = trim($data[2]);
    $domaine_ar = trim($data[3]);
    $numero = trim($data[4]);
    $titre_fr = trim($data[5]);
    $titre_ar = trim($data[6]);
    $date_publication = trim($data[7]);
    $pdf_fr = trim($data[8]);
    $pdf_ar = trim($data[9]);
    $remarques = trim($data[10]);
    
    // Récupérer ou créer le domaine d'activité depuis les données CSV
    $domaine_term = null;
    if (!empty($domaine_fr)) {
      $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites');
      
      // Ajouter la traduction arabe si disponible
      if (!empty($domaine_ar) && $domaine_term && !$domaine_term->hasTranslation('ar')) {
        $domaine_term->addTranslation('ar', ['name' => $domaine_ar]);
        $domaine_term->save();
        echo "Traduction arabe ajoutée pour le domaine: $domaine_fr\n";
      }
    }
    
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, $numero);
    
    // Mapper les types français vers les termes anglais existants
    $type_mapping = [
      'Dahir' => 'Dahir',
      'Loi organique' => 'Law', 
      'Loi' => 'Law',
      'DECRET ROYAL' => 'Decree',
      'Décret royal' => 'Decree',
      'Décret' => 'Decree',
      'DECRET' => 'Decree',
      'Arrêté royal' => 'Arrêté',
      'Arrêté' => 'Arrêté',
      'Circulaire' => 'Circulaire',
      'Cahier des charges' => 'Cahier des charges',
    ];
    
    $mapped_type = isset($type_mapping[$type_fr]) ? $type_mapping[$type_fr] : null;
    
    if (!$mapped_type) {
      echo "Attention: Type '$type_fr' non mappé, ignoré\n";
      continue;
    }
    
    // Récupérer ou créer le terme de type de loi
    $type_term = getTaxonomyTermByName($mapped_type, 'type');
    
    // Parser la date
    $date_formatted = parseDate($date_publication);
    
    // Gérer les fichiers PDF
    $pdf_fr_file = createOrGetFile($pdf_fr);
    $pdf_ar_file = createOrGetFile($pdf_ar);
    
    // Gérer le titre long (limite de 255 caractères pour le champ title)
    // TOUJOURS utiliser field_titre_long pour les titres complets
    $title_for_node = $titre_fr;
    $use_long_title_field = true;
    
    if (mb_strlen($titre_fr, 'UTF-8') > 255) {
      $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
      echo "Titre tronqué pour: $title_for_node\n";
    }
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'reglementation',
      'title' => $title_for_node,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $marine_sector->id()],
      'field_domaine_d_activite' => ['target_id' => $domaine_term ? $domaine_term->id() : null],
      'status' => 1,
      'uid' => 1,
    ];
    
    // Ajouter le titre long (toujours pour les titres complets)
    if ($use_long_title_field) {
      $node_data['field_titre_long'] = ['value' => $titre_fr];
    }
    
    // Ajouter la date si disponible
    if ($date_formatted) {
      $node_data['field_date'] = ['value' => $date_formatted];
    }
    
    // Ajouter le fichier PDF (prioriser le français, sinon l'arabe)
    if ($pdf_fr_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_fr_file->id()];
    } elseif ($pdf_ar_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_ar_file->id()];
    }
    
    if ($existing_node) {
      // Mettre à jour le nœud existant
      foreach ($node_data as $field => $value) {
        if ($field !== 'type') {
          $existing_node->set($field, $value);
        }
      }
      $existing_node->save();
      
      // Ajouter/mettre à jour la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($title_ar_for_node);
          if ($ar_translation->hasField('field_titre_long')) {
            $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
          }
          $ar_translation->save();
        } else {
          $ar_data = ['title' => $title_ar_for_node];
          // Toujours ajouter le titre long pour l'arabe aussi
          $ar_data['field_titre_long'] = ['value' => $titre_ar];
          $existing_node->addTranslation('ar', $ar_data);
          $existing_node->save();
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer un nouveau nœud
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long pour les nouveaux nœuds
        $title_ar_for_node = $titre_ar;
        
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        $ar_data = [
          'title' => $title_ar_for_node,
          'field_titre_long' => ['value' => $titre_ar] // Toujours ajouter le titre complet
        ];
        
        $node->addTranslation('ar', $ar_data);
        $node->save();
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

fclose($handle);

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";