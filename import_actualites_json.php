<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\media\Entity\Media;

/**
 * Script d'importation des actualités depuis le fichier JSON output-fr.json
 * Usage: drush php:script import_actualites_json.php
 */

// Chemin vers le fichier JSON
$json_file_path = '/var/www/html/mtl/output-fr.json';

// Vérifier si le fichier existe
if (!file_exists($json_file_path)) {
  echo "Erreur: Le fichier JSON n'existe pas: $json_file_path\n";
  return;
}

echo "Début de l'importation des actualités depuis le fichier JSON...\n";

/**
 * Cache pour les termes de taxonomie
 */
$term_cache = [];

/**
 * Fonction pour récupérer un terme de taxonomie par son nom
 */
function getTaxonomyTermByName($name, $vocabulary) {
  global $term_cache;
  
  $cache_key = $vocabulary . ':' . $name;
  if (isset($term_cache[$cache_key])) {
    return $term_cache[$cache_key];
  }
  
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  $term = $terms ? reset($terms) : null;
  $term_cache[$cache_key] = $term;
  
  return $term;
}

/**
 * Fonction pour mapper les secteurs et types d'actualités
 */
function processSecteurField($secteur_string) {
  if (empty($secteur_string)) {
    return ['secteurs' => [], 'types' => []];
  }
  
  // Séparer les secteurs multiples par point-virgule
  $secteurs = array_map('trim', explode(';', $secteur_string));
  $secteur_terms = [];
  $type_terms = [];
  
  // Mapping des secteurs vers modes_de_transport
  $secteur_mapping = [
    'Accueil' => 'Transport Routier',
    'Transport Aérien' => 'Aviation Civile',
    'Transport Routier' => 'Transport Routier',
    'Transport routier' => 'Transport Routier',
    'Marine Marchande' => 'Marine Marchande',
    'Ferroviaire' => 'Transport Routier',
    'Logistique' => 'Transport Routier',
  ];
  
  // Mapping des types d'actualités vers type_d_actualites
  $type_mapping = [
    'Coopération' => 'Coopération internationale',
    'Gouvernance' => 'Gouvernance',
    'Parlement' => 'Parlement',
  ];
  
  foreach ($secteurs as $secteur_name) {
    // D'abord essayer de mapper comme secteur
    if (isset($secteur_mapping[$secteur_name])) {
      $mapped_name = $secteur_mapping[$secteur_name];
      $term = getTaxonomyTermByName($mapped_name, 'modes_de_transport');
      if ($term) {
        $secteur_terms[] = ['target_id' => $term->id()];
        echo "  ✅ Secteur mappé: '$secteur_name' -> '$mapped_name' (ID: " . $term->id() . ")\n";
        continue;
      }
    }
    
    // Ensuite essayer de mapper comme type d'actualité
    if (isset($type_mapping[$secteur_name])) {
      $mapped_name = $type_mapping[$secteur_name];
      $term = getTaxonomyTermByName($mapped_name, 'type_d_actualites');
      if ($term) {
        $type_terms[] = ['target_id' => $term->id()];
        echo "  ✅ Type mappé: '$secteur_name' -> '$mapped_name' (ID: " . $term->id() . ")\n";
        continue;
      }
    }
    
    // Essayer de trouver directement dans les secteurs
    $term = getTaxonomyTermByName($secteur_name, 'modes_de_transport');
    if ($term) {
      $secteur_terms[] = ['target_id' => $term->id()];
      echo "  ✅ Secteur direct: '$secteur_name' (ID: " . $term->id() . ")\n";
      continue;
    }
    
    // Essayer de trouver directement dans les types
    $term = getTaxonomyTermByName($secteur_name, 'type_d_actualites');
    if ($term) {
      $type_terms[] = ['target_id' => $term->id()];
      echo "  ✅ Type direct: '$secteur_name' (ID: " . $term->id() . ")\n";
      continue;
    }
    
    echo "  ⚠️ Terme ignoré: '$secteur_name'\n";
  }
  
  return ['secteurs' => $secteur_terms, 'types' => $type_terms];
}

/**
 * Fonction pour télécharger et créer une entité media depuis une URL
 */
function createMediaFromUrl($image_url, $title, $language = 'fr') {
  if (empty($image_url)) {
    return null;
  }
  
  try {
    // Télécharger l'image
    $ch = curl_init();
    curl_setopt_array($ch, [
      CURLOPT_URL => $image_url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_SSL_VERIFYPEER => false,
      CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; DrupalImporter)',
      CURLOPT_FOLLOWLOCATION => true,
    ]);
    
    $file_data = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if (!$file_data || $http_code !== 200) {
      echo "Erreur lors du téléchargement de l'image: $image_url (HTTP $http_code)\n";
      return null;
    }
    
    // Créer le nom du fichier avec préfixe actualites
    $file_name = basename(parse_url($image_url, PHP_URL_PATH));
    if (empty($file_name) || strpos($file_name, '.') === false) {
      $file_name = 'actualite_image_' . time() . '.jpg';
    } else {
      $file_name = 'actualite_' . $file_name;
    }
    
    // Utiliser directement public:// au lieu du sous-répertoire
    $file_repository = \Drupal::service('file.repository');
    $file = $file_repository->writeData($file_data, 'public://' . $file_name);
    
    if (!$file) {
      echo "Erreur lors de la sauvegarde du fichier\n";
      return null;
    }
    
    // Créer l'entité media
    $media = Media::create([
      'bundle' => 'image',
      'name' => $title,
      'field_media_image' => [
        'target_id' => $file->id(),
        'alt' => $title,
        'title' => $title
      ],
      'status' => 1,
      'langcode' => $language,
    ]);
    
    $media->save();
    return $media;
    
  } catch (\Exception $e) {
    echo "Erreur lors du traitement de l'image: " . $e->getMessage() . "\n";
    return null;
  }
}

/**
 * Fonction pour vérifier si une actualité existe déjà
 * Utilise une combinaison titre + début du contenu pour une détection plus précise
 */
function findExistingNode($title, $date = null, $content_preview = '') {
  // Stratégie 1: Recherche exacte par titre
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'actualite')
    ->condition('title', $title)
    ->accessCheck(FALSE);
    
  $nids = $query->execute();
  
  if (!empty($nids)) {
    $nodes = Node::loadMultiple($nids);
    
    // Si un seul nœud trouvé, c'est probablement le bon
    if (count($nodes) === 1) {
      echo "  📝 Nœud existant trouvé par titre exact\n";
      return reset($nodes);
    }
    
    // Si plusieurs nœuds, affiner par date ou contenu
    if ($date) {
      foreach ($nodes as $node) {
        $node_date = $node->get('field_date')->value;
        if ($node_date === $date) {
          echo "  📅 Nœud existant trouvé par titre + date\n";
          return $node;
        }
      }
    }
    
    // Dernier recours: comparer le début du contenu
    if (!empty($content_preview)) {
      $content_start = substr(strip_tags($content_preview), 0, 100);
      foreach ($nodes as $node) {
        $node_content = $node->get('body')->value;
        $node_start = substr(strip_tags($node_content), 0, 100);
        if (similar_text($content_start, $node_start) > 80) {
          echo "  📄 Nœud existant trouvé par titre + contenu similaire\n";
          return $node;
        }
      }
    }
    
    // Si ambiguïté, prendre le plus récent
    echo "  ⚠️ Plusieurs nœuds trouvés, prise du plus récent\n";
    usort($nodes, function($a, $b) { return $b->id() <=> $a->id(); });
    return reset($nodes);
  }
  
  return null;
}

/**
 * Fonction pour fusionner les termes de taxonomie sans doublons
 */
function mergeTerms($existing_terms, $new_terms) {
  $existing_ids = [];
  foreach ($existing_terms as $term) {
    $existing_ids[] = $term['target_id'];
  }
  
  foreach ($new_terms as $new_term) {
    if (!in_array($new_term['target_id'], $existing_ids)) {
      $existing_terms[] = $new_term;
    }
  }
  
  return $existing_terms;
}

// Lire le fichier JSON
$json_content = file_get_contents($json_file_path);
if ($json_content === false) {
  echo "Erreur: Impossible de lire le fichier JSON\n";
  return;
}

$data = json_decode($json_content, true);
if ($data === null) {
  echo "Erreur: Le fichier JSON n'est pas valide\n";
  return;
}

echo "Nombre d'actualités à traiter: " . count($data) . "\n";

$created_count = 0;
$updated_count = 0;
$skipped_count = 0;

foreach ($data as $index => $item) {
  echo "\n--- Traitement de l'actualité " . ($index + 1) . "/" . count($data) . " ---\n";
  echo "Titre: " . substr($item['titre'], 0, 60) . "...\n";
  
  // Vérifier les champs obligatoires
  if (empty($item['titre']) || empty($item['articleHtml'])) {
    echo "Actualité ignorée: titre ou contenu manquant\n";
    $skipped_count++;
    continue;
  }
  
  // Traiter la date
  $date_field = null;
  if (!empty($item['datePublication'])) {
    try {
      $date = \DateTime::createFromFormat('d/m/Y H:i', $item['datePublication']);
      if (!$date) {
        $date = \DateTime::createFromFormat('d/m/Y', $item['datePublication']);
      }
      if ($date) {
        $date_field = $date->format('Y-m-d');
      }
    } catch (\Exception $e) {
      echo "Erreur de format de date: " . $item['datePublication'] . "\n";
    }
  }
  
  // Vérifier si l'actualité existe déjà (utilise titre + date + début contenu)
  $content_preview = substr(strip_tags($item['articleHtml']), 0, 200);
  $existing_node = findExistingNode($item['titre'], $date_field, $content_preview);
  
  if ($existing_node) {
    echo "Actualité existante trouvée (ID: " . $existing_node->id() . "), mise à jour...\n";
    $node = $existing_node;
    $updated_count++;
  } else {
    echo "Création d'une nouvelle actualité...\n";
    $node = Node::create(['type' => 'actualite']);
    $created_count++;
  }
  
  // Définir les champs de base
  $node->set('title', $item['titre']);
  $node->set('body', [
    'value' => $item['articleHtml'],
    'format' => 'full_html',
  ]);
  $node->set('status', ($item['visible'] === 'oui') ? 1 : 0);
  $node->set('langcode', 'fr');
  
  // Ajouter la date si disponible
  if ($date_field) {
    $node->set('field_date', $date_field);
  }
  
  // Traiter les secteurs et types d'actualités
  if (!empty($item['secteur'])) {
    $taxonomy_data = processSecteurField($item['secteur']);
    
    if (!empty($taxonomy_data['secteurs'])) {
      if ($existing_node) {
        // Pour les mises à jour, fusionner avec les termes existants
        $existing_secteurs = $node->get('field_secteur')->getValue();
        $merged_secteurs = mergeTerms($existing_secteurs, $taxonomy_data['secteurs']);
        $node->set('field_secteur', $merged_secteurs);
      } else {
        $node->set('field_secteur', $taxonomy_data['secteurs']);
      }
      echo "Secteurs ajoutés: " . count($taxonomy_data['secteurs']) . "\n";
    }
    
    if (!empty($taxonomy_data['types'])) {
      if ($existing_node) {
        // Pour les mises à jour, fusionner avec les types existants
        $existing_types = $node->get('field_type_d_actualites')->getValue();
        $merged_types = mergeTerms($existing_types, $taxonomy_data['types']);
        $node->set('field_type_d_actualites', $merged_types);
      } else {
        $node->set('field_type_d_actualites', $taxonomy_data['types']);
      }
      echo "Types d'actualités ajoutés: " . count($taxonomy_data['types']) . "\n";
    }
  }
  
  // Traiter l'image si disponible (ignorer les erreurs pour continuer l'importation)
  if (!empty($item['image'])) {
    echo "Traitement de l'image: " . $item['image'] . "\n";
    $media = createMediaFromUrl($item['image'], $item['titre']);
    if ($media) {
      $node->set('field_image_media', ['target_id' => $media->id()]);
      echo "Image ajoutée avec succès (Media ID: " . $media->id() . ")\n";
    } else {
      echo "Image ignorée (erreur de téléchargement)\n";
    }
  }
  
  // Sauvegarder le nœud
  try {
    $node->save();
    echo "Actualité sauvegardée avec succès (Node ID: " . $node->id() . ")\n";
  } catch (\Exception $e) {
    echo "Erreur lors de la sauvegarde: " . $e->getMessage() . "\n";
    $skipped_count++;
    if (!$existing_node) $created_count--; // Ajuster le compteur si c'était une création
    if ($existing_node) $updated_count--; // Ajuster le compteur si c'était une mise à jour
  }
}

echo "\n=== RÉSUMÉ DE L'IMPORTATION ===\n";
echo "Actualités créées: $created_count\n";
echo "Actualités mises à jour: $updated_count\n";
echo "Actualités ignorées: $skipped_count\n";
echo "Total traité: " . ($created_count + $updated_count + $skipped_count) . "\n";
echo "\nImportation terminée !\n";