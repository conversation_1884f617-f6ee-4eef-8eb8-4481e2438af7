<?php

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$autoloader = require_once 'vendor/autoload.php';
$request = Request::createFromGlobals();
$kernel = DrupalKernel::createFromRequest($request, $autoloader, 'prod');
$kernel->boot();

// Test de la fonction du module
$secteur_id = 3; // Transport routier

echo "=== DEBUG HIERARCHICAL FILTERS ===\n";
echo "Secteur ID testé: $secteur_id\n\n";

// Test de la fonction
$domains = hierarchical_filters_get_domains_for_secteur($secteur_id);
echo "Domaines trouvés pour secteur $secteur_id:\n";
foreach ($domains as $id => $name) {
    echo "- ID: $id, Nom: $name\n";
}

echo "\n";

// Test de l'endpoint AJAX
$controller = new \Drupal\hierarchical_filters\Controller\HierarchicalFiltersController();
$response = $controller->getDomainesBySecteur($secteur_id);
$content = $response->getContent();
echo "Réponse JSON de l'endpoint:\n";
echo $content . "\n";

echo "\n=== TEST TERMINÉ ===\n";