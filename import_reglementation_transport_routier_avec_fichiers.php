<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON><PERSON>\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation des réglementations Transport routier avec upload de fichiers
 * Usage: drush php:script import_reglementation_transport_routier_avec_fichiers.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/html/mtl/reglementation_transport routier.csv';

// Dossier des fichiers à uploader
$files_directory = '/var/www/html/mtl/Portail MTL - Collecte de la réglementaire';

// Vérifier si le fichier CSV existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

// Vérifier si le dossier des fichiers existe
if (!is_dir($files_directory)) {
  echo "Erreur: Le dossier des fichiers n'existe pas: $files_directory\n";
  return;
}

echo "Début de l'importation des réglementations Transport routier avec fichiers...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 * Met à jour les termes existants s'ils n'ont pas les bons filtres
 */
function getTaxonomyTermByName($name, $vocabulary, $sector_term = null) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    $term = reset($terms);
    
    // Vérifier et mettre à jour le secteur si nécessaire
    if ($sector_term && $vocabulary === 'domaines_d_activites') {
      if ($term->hasField('field_secteur')) {
        $current_secteur = $term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $sector_term->id()) {
          $term->set('field_secteur', ['target_id' => $sector_term->id()]);
          $term->save();
          echo "Secteur mis à jour pour le terme existant: $name\n";
        }
      }
    }
    
    return $term;
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term_data = [
    'vid' => $vocabulary,
    'name' => $name,
    'langcode' => 'fr',
  ];
  
  // Ajouter le secteur si fourni (pour les domaines d'activité)
  if ($sector_term && $vocabulary === 'domaines_d_activites') {
    $term_data['field_secteur'] = ['target_id' => $sector_term->id()];
  }
  
  $term = Term::create($term_data);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour récupérer ou créer un terme de type avec migration du contenu existant
 */
function getOrMigrateTypeTermByName($preferred_name, $vocabulary) {
  // D'abord, chercher le terme préféré (ex: 'Décret', 'Loi')
  $preferred_terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $preferred_name,
      'vid' => $vocabulary,
    ]);
  
  if ($preferred_terms) {
    $preferred_term = reset($preferred_terms);
    echo "Terme existant trouvé: $preferred_name\n";
    return $preferred_term;
  }
  
  // Si le terme préféré n'existe pas, chercher les anciens termes anglais
  $old_mapping = [
    'Décret' => ['Decree'],
    'Loi' => ['Law'],
  ];
  
  if (isset($old_mapping[$preferred_name])) {
    foreach ($old_mapping[$preferred_name] as $old_name) {
      $old_terms = \Drupal::entityTypeManager()
        ->getStorage('taxonomy_term')
        ->loadByProperties([
          'name' => $old_name,
          'vid' => $vocabulary,
        ]);
      
      if ($old_terms) {
        $old_term = reset($old_terms);
        echo "Migration: Renommage de '$old_name' vers '$preferred_name'\n";
        
        // Renommer le terme existant
        $old_term->set('name', $preferred_name);
        $old_term->save();
        
        return $old_term;
      }
    }
  }
  
  // Si aucun terme existant n'été trouvé, créer un nouveau terme
  echo "Création du terme '$preferred_name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $preferred_name,
    'langcode' => 'fr',
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour parser la date du CSV
 */
function parseDate($date_string) {
  if (empty($date_string)) {
    return null;
  }
  
  // Format attendu: dd/mm/yyyy
  $date_parts = explode('/', $date_string);
  if (count($date_parts) == 3) {
    $day = str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
    $month = str_pad($date_parts[1], 2, '0', STR_PAD_LEFT);
    $year = $date_parts[2];
    return "$year-$month-$day";
  }
  
  return null;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      return Node::load(reset($nids));
    }
  }
  
  return null;
}

/**
 * Fonction pour uploader un fichier PDF dans Drupal
 */
function uploadFile($pdf_filename, $files_directory, $language = 'fr') {
  if (empty($pdf_filename)) {
    return null;
  }
  
  // Nettoyer le nom du fichier
  $pdf_filename = trim($pdf_filename);
  
  // Chercher le fichier dans tous les sous-dossiers
  $file_path = findFileInDirectory($files_directory, $pdf_filename);
  
  if (!$file_path || !file_exists($file_path)) {
    echo "Fichier PDF non trouvé: $pdf_filename\n";
    return null;
  }
  
  try {
    // Vérifier si le fichier existe déjà dans Drupal
    $existing_files = \Drupal::entityTypeManager()
      ->getStorage('file')
      ->loadByProperties(['filename' => basename($file_path)]);
    
    if ($existing_files) {
      $existing_file = reset($existing_files);
      echo "Fichier existant trouvé: " . $existing_file->getFilename() . "\n";
      return $existing_file;
    }
    
    // Préparer le répertoire de destination
    $destination_dir = 'public://reglementation/transport-routier/';
    \Drupal::service('file_system')->prepareDirectory($destination_dir, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
    
    // Copier le fichier vers Drupal
    $destination = $destination_dir . basename($file_path);
    $file_data = file_get_contents($file_path);
    
    if ($file_data === FALSE) {
      echo "Erreur lors de la lecture du fichier: $file_path\n";
      return null;
    }
    
    // Créer l'entité fichier
    $file = \Drupal::service('file.repository')->writeData($file_data, $destination, FileSystemInterface::EXISTS_REPLACE);
    
    if ($file) {
      $file->setPermanent();
      $file->save();
      echo "Fichier uploadé: " . $file->getFilename() . " (ID: " . $file->id() . ")\n";
      return $file;
    } else {
      echo "Erreur lors de la création du fichier dans Drupal\n";
      return null;
    }
    
  } catch (Exception $e) {
    echo "Erreur lors de l'upload du fichier $pdf_filename: " . $e->getMessage() . "\n";
    return null;
  }
}

/**
 * Fonction pour trouver un fichier dans un dossier et ses sous-dossiers
 */
function findFileInDirectory($directory, $filename) {
  if (!is_dir($directory)) {
    return null;
  }
  
  $iterator = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::LEAVES_ONLY
  );
  
  foreach ($iterator as $file) {
    if ($file->isFile() && $file->getFilename() === $filename) {
      return $file->getPathname();
    }
  }
  
  return null;
}

// Récupérer les termes existants pour Transport routier
$transport_routier_sector = getTaxonomyTermByName('Transport routier', 'modes_de_transport');

if (!$transport_routier_sector) {
  echo "Erreur: Le terme 'Transport routier' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

// Lire le fichier CSV complet pour traitement personnalisé
$csv_content = file_get_contents($csv_file_path);
if (!$csv_content) {
  echo "Erreur: Impossible de lire le fichier CSV\n";
  return;
}

// Diviser en lignes et nettoyer les retours à la ligne dans les cellules
$lines = explode("\n", $csv_content);

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-19)
$data_lines = array_slice($lines, 19);

// Fonction pour parser une ligne CSV malformée
function parseCSVLine($line) {
  // Utiliser str_getcsv avec gestion des retours à la ligne
  $data = str_getcsv($line, ',', '"');
  
  // Nettoyer les retours à la ligne dans chaque cellule
  foreach ($data as &$cell) {
    $cell = trim(str_replace(["\n", "\r"], ' ', $cell));
  }
  
  return $data;
}

// Reconstruire les lignes brisées par les retours à la ligne
$reconstructed_lines = [];
$current_line = '';

foreach ($data_lines as $line) {
  $line = trim($line);
  
  if (empty($line)) {
    continue;
  }
  
  // Ajouter à la ligne courante
  $current_line .= ($current_line ? ' ' : '') . $line;
  
  // Compter les guillemets pour détecter les cellules incomplètes
  $quote_count = substr_count($current_line, '"');
  
  // Si le nombre de guillemets est pair, la ligne est complète
  if ($quote_count % 2 == 0) {
    $reconstructed_lines[] = $current_line;
    $current_line = '';
  }
}

// Ajouter la dernière ligne si elle existe
if (!empty($current_line)) {
  $reconstructed_lines[] = $current_line;
}

$imported = 0;
$updated = 0;
$errors = 0;
$line_number = 19; // Commencer après les en-têtes

// Traiter chaque ligne de données
foreach ($reconstructed_lines as $line) {
  $line_number++;
  
  if (empty(trim($line))) {
    continue;
  }
  
  $data = parseCSVLine($line);
  
  // Vérifier que la ligne contient toutes les colonnes attendues
  if (count($data) < 11) {
    echo "Ligne $line_number ignorée (colonnes incomplètes: " . count($data) . ")\n";
    continue;
  }
  
  // Vérifier que la ligne contient des données valides
  if (empty($data[5]) || empty($data[6])) { // Intitulé FR et AR
    echo "Ligne $line_number ignorée (colonnes 5/6 vides)\n";
    continue;
  }
  
  try {
    // Extraire les données
    $type_fr = trim($data[0]);
    $type_ar = trim($data[1]);
    $domaine_fr = trim($data[2]);
    $domaine_ar = trim($data[3]);
    $numero = trim($data[4]);
    $titre_fr = trim($data[5]);
    // Nettoyer les retours à la ligne dans le titre arabe
    $titre_ar = trim(str_replace(["\n", "\r"], ' ', $data[6]));
    $date_publication = trim($data[7]);
    $pdf_fr = trim($data[8]);
    $pdf_ar = trim($data[9]);
    $remarques = isset($data[10]) ? trim($data[10]) : '';
    
    // Récupérer ou créer le domaine d'activité depuis les données CSV
    $domaine_term = null;
    if (!empty($domaine_fr)) {
      $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites', $transport_routier_sector);
      
      // Ajouter le secteur au domaine d'activité s'il n'est pas déjà assigné
      if ($domaine_term && $domaine_term->hasField('field_secteur')) {
        $current_secteur = $domaine_term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $transport_routier_sector->id()) {
          $domaine_term->set('field_secteur', ['target_id' => $transport_routier_sector->id()]);
          $domaine_term->save();
          echo "Secteur Transport routier assigné au domaine: $domaine_fr\n";
        }
      }
      
      // Ajouter ou mettre à jour la traduction arabe avec les mêmes filtres
      if (!empty($domaine_ar) && $domaine_term) {
        if (!$domaine_term->hasTranslation('ar')) {
          // Créer la traduction avec les mêmes champs de référence
          $ar_translation_data = ['name' => $domaine_ar];
          
          // Hériter du secteur du terme principal
          if ($domaine_term->hasField('field_secteur') && !$domaine_term->get('field_secteur')->isEmpty()) {
            $ar_translation_data['field_secteur'] = $domaine_term->get('field_secteur')->getValue();
          }
          
          $domaine_term->addTranslation('ar', $ar_translation_data);
          $domaine_term->save();
          echo "Traduction arabe ajoutée pour le domaine: $domaine_fr avec secteur\n";
        } else {
          // Mettre à jour la traduction existante si elle n'a pas les bons filtres
          $ar_translation = $domaine_term->getTranslation('ar');
          $needs_update = false;
          
          // Vérifier et mettre à jour le secteur
          if ($domaine_term->hasField('field_secteur') && !$domaine_term->get('field_secteur')->isEmpty()) {
            $main_secteur = $domaine_term->get('field_secteur')->getValue();
            if ($ar_translation->hasField('field_secteur')) {
              $ar_secteur = $ar_translation->get('field_secteur')->getValue();
              if ($ar_secteur !== $main_secteur) {
                $ar_translation->set('field_secteur', $main_secteur);
                $needs_update = true;
              }
            }
          }
          
          if ($needs_update) {
            $ar_translation->save();
            echo "Traduction arabe mise à jour pour le domaine: $domaine_fr\n";
          }
        }
      }
    }
    
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, $numero);
    
    // Mapper les types français vers les termes existants (priorité aux termes français)
    $type_mapping = [
      'Dahir' => 'Dahir',
      'Loi organique' => 'Loi',
      'Loi' => 'Loi',
      'DECRET ROYAL' => 'Décret',
      'Décret royal' => 'Décret',
      'Décret' => 'Décret',
      'DECRET' => 'Décret',
      'Arrêté royal' => 'Arrêté',
      'Arrêté' => 'Arrêté',
      'Circulaire' => 'Circulaire',
      'Cahier des charges' => 'Cahier des charges',
    ];
    
    $mapped_type = isset($type_mapping[$type_fr]) ? $type_mapping[$type_fr] : null;
    
    if (!$mapped_type) {
      echo "Attention: Type '$type_fr' non mappé, ignoré\n";
      continue;
    }
    
    // Récupérer ou créer le terme de type de loi avec migration si nécessaire
    $type_term = getOrMigrateTypeTermByName($mapped_type, 'type');
    
    // Ajouter ou mettre à jour la traduction arabe du type avec les mêmes filtres
    if (!empty($type_ar) && $type_term) {
      if (!$type_term->hasTranslation('ar')) {
        // Créer la traduction en héritant de tous les champs du terme principal
        $ar_translation_data = ['name' => $type_ar];
        
        // Hériter des champs de référence du terme principal (secteur, etc.)
        $term_fields = $type_term->getFieldDefinitions();
        foreach ($term_fields as $field_name => $field_definition) {
          // Copier les champs de référence (entity_reference) sauf les champs système
          if ($field_definition->getType() === 'entity_reference' && 
              !in_array($field_name, ['vid', 'parent', 'revision_user']) &&
              $type_term->hasField($field_name) && 
              !$type_term->get($field_name)->isEmpty()) {
            $ar_translation_data[$field_name] = $type_term->get($field_name)->getValue();
          }
        }
        
        $type_term->addTranslation('ar', $ar_translation_data);
        $type_term->save();
        echo "Traduction arabe ajoutée pour le type: $mapped_type avec filtres\n";
      } else {
        // Mettre à jour la traduction existante si elle n'a pas les bons filtres
        $ar_translation = $type_term->getTranslation('ar');
        $needs_update = false;
        
        // Vérifier et synchroniser tous les champs de référence
        $term_fields = $type_term->getFieldDefinitions();
        foreach ($term_fields as $field_name => $field_definition) {
          if ($field_definition->getType() === 'entity_reference' && 
              !in_array($field_name, ['vid', 'parent', 'revision_user']) &&
              $type_term->hasField($field_name) && 
              !$type_term->get($field_name)->isEmpty()) {
            
            $main_value = $type_term->get($field_name)->getValue();
            if ($ar_translation->hasField($field_name)) {
              $ar_value = $ar_translation->get($field_name)->getValue();
              if ($ar_value !== $main_value) {
                $ar_translation->set($field_name, $main_value);
                $needs_update = true;
              }
            }
          }
        }
        
        if ($needs_update) {
          $ar_translation->save();
          echo "Traduction arabe mise à jour pour le type: $mapped_type\n";
        }
      }
    }
    
    // Parser la date
    $date_formatted = parseDate($date_publication);
    
    // Uploader les fichiers PDF
    $file_fr = null;
    $file_ar = null;
    
    if (!empty($pdf_fr)) {
      $file_fr = uploadFile($pdf_fr, $files_directory, 'fr');
    }
    
    if (!empty($pdf_ar)) {
      $file_ar = uploadFile($pdf_ar, $files_directory, 'ar');
    }
    
    // Gérer le titre long (limite de 255 caractères pour le champ title)
    $title_for_node = $titre_fr;
    $long_title = null;
    
    if (mb_strlen($titre_fr, 'UTF-8') > 255) {
      $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
      $long_title = $titre_fr;
      echo "Titre tronqué pour: $title_for_node\n";
    }
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'reglementation',
      'title' => $title_for_node,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $transport_routier_sector->id()],
      'status' => 1,
      'uid' => 1,
    ];
    
    // Ajouter le domaine d'activité si disponible
    if ($domaine_term) {
      $node_data['field_domaine_d_activite'] = ['target_id' => $domaine_term->id()];
    }
    
    // Ajouter le titre long si nécessaire
    if ($long_title) {
      $node_data['field_titre_long'] = ['value' => $long_title];
    }
    
    // Ajouter la date si disponible
    if ($date_formatted) {
      $node_data['field_date'] = ['value' => $date_formatted];
    }
    
    // Ajouter les fichiers si disponibles
    $files_to_attach = [];
    if ($file_fr) {
      $files_to_attach[] = [
        'target_id' => $file_fr->id(),
        'description' => 'Version française',
      ];
    }
    if ($file_ar) {
      $files_to_attach[] = [
        'target_id' => $file_ar->id(),
        'description' => 'Version arabe',
      ];
    }
    
    if (!empty($files_to_attach)) {
      $node_data['field_lien_telechargement'] = $files_to_attach;
    }
    
    if ($existing_node) {
      // Mettre à jour le nœud existant et vérifier les champs manquants
      $needs_update = false;
      
      // Vérifier et mettre à jour le secteur s'il manque
      if ($existing_node->hasField('field_secteur')) {
        $current_secteur = $existing_node->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $transport_routier_sector->id()) {
          $existing_node->set('field_secteur', ['target_id' => $transport_routier_sector->id()]);
          $needs_update = true;
          echo "Secteur Transport routier ajouté au nœud existant\n";
        }
      }
      
      // Vérifier et mettre à jour le domaine d'activité s'il manque
      if ($domaine_term && $existing_node->hasField('field_domaine_d_activite')) {
        $current_domaine = $existing_node->get('field_domaine_d_activite')->target_id;
        if (!$current_domaine || $current_domaine != $domaine_term->id()) {
          $existing_node->set('field_domaine_d_activite', ['target_id' => $domaine_term->id()]);
          $needs_update = true;
          echo "Domaine d'activité ajouté au nœud existant\n";
        }
      }
      
      // Vérifier et mettre à jour le type s'il manque
      if ($type_term && $existing_node->hasField('field_type_loi')) {
        $current_type = $existing_node->get('field_type_loi')->target_id;
        if (!$current_type || $current_type != $type_term->id()) {
          $existing_node->set('field_type_loi', ['target_id' => $type_term->id()]);
          $needs_update = true;
          echo "Type de loi ajouté au nœud existant\n";
        }
      }
      
      // Ajouter les fichiers s'ils n'existent pas
      if ($existing_node->hasField('field_lien_telechargement')) {
        $current_files = $existing_node->get('field_lien_telechargement')->getValue();
        $existing_file_ids = array_column($current_files, 'target_id');
        
        $files_to_add = [];
        if ($file_fr && !in_array($file_fr->id(), $existing_file_ids)) {
          $files_to_add[] = [
            'target_id' => $file_fr->id(),
            'description' => 'Version française',
          ];
        }
        if ($file_ar && !in_array($file_ar->id(), $existing_file_ids)) {
          $files_to_add[] = [
            'target_id' => $file_ar->id(),
            'description' => 'Version arabe',
          ];
        }
        
        if (!empty($files_to_add)) {
          // Combiner les fichiers existants avec les nouveaux
          $all_files = array_merge($current_files, $files_to_add);
          $existing_node->set('field_lien_telechargement', $all_files);
          $needs_update = true;
          echo "Fichiers ajoutés au nœud existant\n";
        }
      }
      
      // Appliquer les autres mises à jour
      foreach ($node_data as $field => $value) {
        if (!in_array($field, ['type', 'field_secteur', 'field_domaine_d_activite', 'field_type_loi', 'field_lien_telechargement'])) {
          $existing_node->set($field, $value);
          $needs_update = true;
        }
      }
      
      if ($needs_update) {
        $existing_node->save();
      }
      
      // Ajouter/mettre à jour la traduction arabe avec vérification des champs manquants
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($title_ar_for_node);
          
          // Ajouter le titre long arabe si le titre français en a un
          if ($existing_node->hasField('field_titre_long') && !$existing_node->get('field_titre_long')->isEmpty() && $ar_translation->hasField('field_titre_long')) {
            $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
          }
          
          // Les fichiers sont gérés au niveau du nœud principal, pas de la traduction
          // Drupal synchronise automatiquement les champs de fichiers entre les traductions
          
          // Vérifier que la traduction arabe a les mêmes filtres que la version française
          $ar_needs_update = false;
          
          // Synchroniser le secteur
          if ($ar_translation->hasField('field_secteur') && $existing_node->hasField('field_secteur')) {
            $fr_secteur = $existing_node->get('field_secteur')->getValue();
            $ar_secteur = $ar_translation->get('field_secteur')->getValue();
            if ($ar_secteur !== $fr_secteur) {
              $ar_translation->set('field_secteur', $fr_secteur);
              $ar_needs_update = true;
            }
          }
          
          // Synchroniser le domaine d'activité
          if ($ar_translation->hasField('field_domaine_d_activite') && $existing_node->hasField('field_domaine_d_activite')) {
            $fr_domaine = $existing_node->get('field_domaine_d_activite')->getValue();
            $ar_domaine = $ar_translation->get('field_domaine_d_activite')->getValue();
            if ($ar_domaine !== $fr_domaine) {
              $ar_translation->set('field_domaine_d_activite', $fr_domaine);
              $ar_needs_update = true;
            }
          }
          
          // Synchroniser le type de loi
          if ($ar_translation->hasField('field_type_loi') && $existing_node->hasField('field_type_loi')) {
            $fr_type = $existing_node->get('field_type_loi')->getValue();
            $ar_type = $ar_translation->get('field_type_loi')->getValue();
            if ($ar_type !== $fr_type) {
              $ar_translation->set('field_type_loi', $fr_type);
              $ar_needs_update = true;
            }
          }
          
          if ($ar_needs_update) {
            echo "Filtres synchronisés pour la traduction arabe\n";
          }
          
          $ar_translation->save();
        } else {
          // Créer la traduction avec tous les champs de filtrage
          $ar_data = ['title' => $title_ar_for_node];
          
          // Ajouter le titre long arabe si le titre français en a un
          if ($existing_node->hasField('field_titre_long') && !$existing_node->get('field_titre_long')->isEmpty()) {
            $ar_data['field_titre_long'] = ['value' => $titre_ar];
          }
          
          // Les fichiers sont gérés au niveau du nœud principal, pas de la traduction
          // Drupal synchronise automatiquement les champs de fichiers entre les traductions
          
          // Copier tous les champs de filtrage depuis la version française
          if ($existing_node->hasField('field_secteur') && !$existing_node->get('field_secteur')->isEmpty()) {
            $ar_data['field_secteur'] = $existing_node->get('field_secteur')->getValue();
          }
          if ($existing_node->hasField('field_domaine_d_activite') && !$existing_node->get('field_domaine_d_activite')->isEmpty()) {
            $ar_data['field_domaine_d_activite'] = $existing_node->get('field_domaine_d_activite')->getValue();
          }
          if ($existing_node->hasField('field_type_loi') && !$existing_node->get('field_type_loi')->isEmpty()) {
            $ar_data['field_type_loi'] = $existing_node->get('field_type_loi')->getValue();
          }
          
          $existing_node->addTranslation('ar', $ar_data);
          $existing_node->save();
          echo "Traduction arabe créée avec tous les filtres et fichier\n";
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer un nouveau nœud avec les fichiers
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter la traduction arabe avec tous les filtres
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long pour les nouveaux nœuds
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        $ar_data = ['title' => $title_ar_for_node];
        
        // Ajouter le titre long arabe si le titre français en a un
        if ($node->hasField('field_titre_long') && !$node->get('field_titre_long')->isEmpty()) {
          $ar_data['field_titre_long'] = ['value' => $titre_ar];
        }
        
        // Les fichiers sont gérés au niveau du nœud principal, pas de la traduction
        // Drupal synchronise automatiquement les champs de fichiers entre les traductions
        
        // Copier tous les champs de filtrage depuis le nœud principal
        if ($node->hasField('field_secteur') && !$node->get('field_secteur')->isEmpty()) {
          $ar_data['field_secteur'] = $node->get('field_secteur')->getValue();
        }
        if ($domaine_term && $node->hasField('field_domaine_d_activite')) {
          $ar_data['field_domaine_d_activite'] = $node->get('field_domaine_d_activite')->getValue();
        }
        if ($type_term && $node->hasField('field_type_loi')) {
          $ar_data['field_type_loi'] = $node->get('field_type_loi')->getValue();
        }
        
        $node->addTranslation('ar', $ar_data);
        $node->save();
        echo "Traduction arabe créée avec tous les filtres et fichier\n";
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";