<?php

use <PERSON>upal\node\Entity\Node;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

// Configuration
$csv_file = '/var/www/html/mtl/Canvas Réglementation - Transport routier.csv';
$pdf_paths_csv = '/var/www/html/mtl/mtl-reglementation.csv';
$pdf_base_directory = '/var/www/html/mtl/Portail MTL - Collecte de la réglementaire/';

// Function to create or get file entity
function createOrGetFile($file_path, $destination_folder = 'reglementation/pdf/transport-routier/') {
    $file_system = \Drupal::service('file_system');
    
    if (!file_exists($file_path)) {
        echo "File not found: $file_path\n";
        return NULL;
    }
    
    $filename = basename($file_path);
    $destination = 'public://' . $destination_folder . $filename;
    
    // Create directory if needed
    $directory = dirname($destination);
    $file_system->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
    
    // Check if file already exists
    $existing_files = \Drupal::entityTypeManager()
        ->getStorage('file')
        ->loadByProperties(['filename' => $filename]);
    
    if (!empty($existing_files)) {
        $file = reset($existing_files);
        echo "Using existing file: $filename (ID: {$file->id()})\n";
        return $file;
    }
    
    // Copy file to destination
    $uri = $file_system->copy($file_path, $destination, FileSystemInterface::EXISTS_REPLACE);
    
    if ($uri) {
        $file = File::create([
            'filename' => $filename,
            'uri' => $uri,
            'status' => 1,
        ]);
        $file->save();
        echo "Created new file: $filename (ID: {$file->id()})\n";
        return $file;
    } else {
        echo "Failed to copy file: $file_path\n";
        return NULL;
    }
}

// Function to read CSV file and get PDF paths with intelligent matching
function readCsvPdfPaths($csv_file) {
    $pdf_data = [];
    
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        return $pdf_data;
    }
    
    while (($data = fgetcsv($handle)) !== FALSE) {
        if (!empty($data[0])) {
            // Take the first non-empty PDF path from the row
            // Priority: French PDF first, then Arabic, then any other
            $pdf_path = null;
            foreach ($data as $path) {
                $path = trim($path);
                if (!empty($path) && strpos($path, '.pdf') !== false) {
                    $pdf_path = $path;
                    break; // Take the first valid PDF path found
                }
            }
            if ($pdf_path) {
                // Extract number from PDF filename for matching
                $filename = basename($pdf_path);
                $numero_extracted = extractNumeroFromFilename($filename);
                
                $pdf_data[] = [
                    'path' => $pdf_path,
                    'filename' => $filename,
                    'numero' => $numero_extracted
                ];
            }
        }
    }
    
    fclose($handle);
    return $pdf_data;
}

// Function to extract numero from PDF filename
function extractNumeroFromFilename($filename) {
    // Remove extension
    $name = pathinfo($filename, PATHINFO_FILENAME);
    
    // Common patterns for numbers in filenames
    $patterns = [
        '/(\d+\.\d+\.\d+)/',     // Ex: 2.10.311, 1.63.260
        '/(\d+\.\d+)/',          // Ex: 52.05, 116.14
        '/(\d+\.?\d*)/',         // Ex: 245.65, 664.03
        '/([A-Z]+\s*\d+\.\d+)/', // Ex: Loi 52.05
        '/(\d{3,})/',            // Ex: 664, 2219 (3+ digits)
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $name, $matches)) {
            return trim(str_replace(['Loi ', 'Décret ', 'Dahir '], '', $matches[1]));
        }
    }
    
    return null;
}

// Function to find best PDF match for a regulation
function findBestPdfMatch($csv_row, $pdf_data) {
    $numero_texte = $csv_row['numero_texte'];
    
    // First try exact match
    foreach ($pdf_data as $pdf) {
        if ($pdf['numero'] === $numero_texte) {
            return $pdf['path'];
        }
    }
    
    // Try partial match (remove dots, spaces)
    $numero_clean = str_replace(['.', ' ', '-'], '', $numero_texte);
    foreach ($pdf_data as $pdf) {
        $pdf_numero_clean = str_replace(['.', ' ', '-'], '', $pdf['numero']);
        if ($pdf_numero_clean === $numero_clean) {
            return $pdf['path'];
        }
    }
    
    // Try substring match
    foreach ($pdf_data as $pdf) {
        if ($pdf['numero'] && (strpos($pdf['numero'], $numero_texte) !== false || strpos($numero_texte, $pdf['numero']) !== false)) {
            return $pdf['path'];
        }
    }
    
    // Try match in filename with regulation number
    foreach ($pdf_data as $pdf) {
        if (strpos($pdf['filename'], $numero_texte) !== false) {
            return $pdf['path'];
        }
    }
    
    return null;
}

// Function to find regulation node by title
function findRegulationNode($intitule_fr, $numero_texte) {
    $query = \Drupal::entityQuery('node')
        ->condition('type', 'reglementation')
        ->condition('status', 1)
        ->accessCheck(FALSE);
    
    // First try exact title match
    $nids = $query->condition('title', $intitule_fr)->execute();
    if (!empty($nids)) {
        return Node::load(reset($nids));
    }
    
    // Try partial title match
    $nids = $query->condition('title', '%' . $intitule_fr . '%', 'LIKE')->execute();
    if (!empty($nids)) {
        return Node::load(reset($nids));
    }
    
    // Try to find by numero in title
    if ($numero_texte) {
        $nids = $query->condition('title', '%' . $numero_texte . '%', 'LIKE')->execute();
        if (!empty($nids)) {
            return Node::load(reset($nids));
        }
    }
    
    return NULL;
}

// Main import logic
try {
    // Read CSV for node titles (order)
    if (!file_exists($csv_file)) {
        throw new Exception("CSV file not found: $csv_file");
    }
    
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        throw new Exception("Could not open CSV file: $csv_file");
    }
    
    // Skip header rows (first row is headers, rows 2-20 are examples/empty)
    for ($i = 0; $i < 20; $i++) {
        fgetcsv($handle);
    }
    
    // Read all CSV data (titles and order)
    $csv_data = [];
    while (($data = fgetcsv($handle)) !== FALSE) {
        if (empty($data[5]) || empty($data[4])) { // Skip rows without title or numero
            continue;
        }
        
        $csv_data[] = [
            'type_fr' => trim($data[0]),
            'numero_texte' => trim($data[4]),
            'intitule_fr' => trim($data[5])
        ];
    }
    fclose($handle);
    
    // Read CSV for PDF paths (intelligent matching)
    if (!file_exists($pdf_paths_csv)) {
        throw new Exception("PDF paths CSV file not found: $pdf_paths_csv");
    }
    
    $pdf_data = readCsvPdfPaths($pdf_paths_csv);
    
    echo "Starting import process...\n";
    echo "================================\n";
    echo "Found " . count($csv_data) . " nodes in CSV\n";
    echo "Found " . count($pdf_data) . " PDF paths in CSV\n";
    echo "================================\n";
    
    $processed = 0;
    $updated = 0;
    $errors = 0;
    $matched = 0;
    $no_match = 0;
    
    // Process each CSV node and find best PDF match
    foreach ($csv_data as $csv_row) {
        $processed++;
        
        echo "\n[$processed] Processing: {$csv_row['intitule_fr']} ({$csv_row['numero_texte']})\n";
        
        // Find best PDF match
        $pdf_relative_path = findBestPdfMatch($csv_row, $pdf_data);
        
        if (!$pdf_relative_path) {
            echo "  ❌ No PDF match found for numero: {$csv_row['numero_texte']}\n";
            $no_match++;
            $errors++;
            continue;
        }
        
        $matched++;
        echo "  📁 Matched PDF: " . basename($pdf_relative_path) . "\n";
        
        // Find the regulation node
        $node = findRegulationNode($csv_row['intitule_fr'], $csv_row['numero_texte']);
        if (!$node) {
            echo "  ❌ Node not found for: {$csv_row['intitule_fr']}\n";
            $errors++;
            continue;
        }
        
        echo "  ✅ Found node: {$node->getTitle()} (ID: {$node->id()})\n";
        
        // Check if field already has a file
        if (!$node->get('field_lien_telechargement')->isEmpty()) {
            echo "  ⚠️  Node already has a file attached, skipping...\n";
            continue;
        }
        
        // Build full PDF file path
        // Remove base directory from path if it's already included
        if (strpos($pdf_relative_path, 'Portail MTL - Collecte de la réglementaire') === 0) {
            $pdf_file_path = '/var/www/html/mtl/' . $pdf_relative_path;
        } else {
            $pdf_file_path = $pdf_base_directory . $pdf_relative_path;
        }
        
        if (!file_exists($pdf_file_path)) {
            echo "  ❌ PDF file not found at: $pdf_file_path\n";
            $errors++;
            continue;
        }
        
        echo "  📄 Found PDF: " . basename($pdf_file_path) . "\n";
        
        // Create file entity
        $file = createOrGetFile($pdf_file_path);
        if (!$file) {
            echo "  ❌ Failed to create file entity\n";
            $errors++;
            continue;
        }
        
        // Update node with file reference
        try {
            $node->set('field_lien_telechargement', ['target_id' => $file->id()]);
            $node->save();
            $updated++;
            echo "  ✅ Updated node with file (File ID: {$file->id()})\n";
        } catch (Exception $e) {
            echo "  ❌ Failed to update node: " . $e->getMessage() . "\n";
            $errors++;
        }
    }
    
    echo "\n================================\n";
    echo "Import completed!\n";
    echo "Processed: $processed nodes\n";
    echo "Matched PDFs: $matched\n";
    echo "No match found: $no_match\n";
    echo "Updated: $updated nodes\n";
    echo "Errors: $errors\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}