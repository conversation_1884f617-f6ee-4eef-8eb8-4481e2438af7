<?php

use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\media\Entity\Media;

/**
 * Script d'importation complète de TOUTES les actualités ARABES
 * Usage: drush php:script import_actualites_ar.php
 */

$json_file_path = '/var/www/mtl/output-ar.json';

// Cache des fichiers locaux pour optimiser la recherche
$local_files_cache = null;

if (!file_exists($json_file_path)) {
  echo "Erreur: Le fichier JSON arabe n'existe pas: $json_file_path\n";
  return;
}

echo "=== IMPORT COMPLET DE TOUTES LES ACTUALITÉS ARABES ===\n\n";

$term_cache = [];

function getTaxonomyTermByName($name, $vocabulary) {
  global $term_cache;
  
  $cache_key = $vocabulary . ':' . $name;
  if (isset($term_cache[$cache_key])) {
    return $term_cache[$cache_key];
  }
  
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  $term = $terms ? reset($terms) : null;
  $term_cache[$cache_key] = $term;
  
  return $term;
}

function removeAccents($string) {
  // First handle Unicode escape sequences like #U00e9
  $string = preg_replace_callback('/#U([0-9A-Fa-f]{4})/', function($matches) {
    return mb_convert_encoding(pack('H*', $matches[1]), 'UTF-8', 'UCS-2BE');
  }, $string);
  
  $accents = [
    'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a',
    'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e',
    'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
    'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
    'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
    'ç' => 'c', 'ñ' => 'n',
    'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A',
    'È' => 'E', 'É' => 'E', 'Ê' => 'E', 'Ë' => 'E',
    'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
    'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O',
    'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U',
    'Ç' => 'C', 'Ñ' => 'N'
  ];
  
  return strtr($string, $accents);
}

function getLocalFilesCache($local_images_path) {
  global $local_files_cache;
  
  if ($local_files_cache === null) {
    $local_files_cache = [];
    if (is_dir($local_images_path)) {
      $files = scandir($local_images_path);
      foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && is_file($local_images_path . $file)) {
          $local_files_cache[] = $file;
        }
      }
    }
  }
  
  return $local_files_cache;
}

function findBestLocalMatch($target_filename, $local_images_path) {
  $local_files = getLocalFilesCache($local_images_path);
  $target_lower = strtolower($target_filename);
  
  // 1. Correspondance exacte
  foreach ($local_files as $file) {
    if ($file === $target_filename) {
      return ['file' => $file, 'method' => 'Exacte'];
    }
  }
  
  // 2. Correspondance insensible à la casse
  foreach ($local_files as $file) {
    if (strtolower($file) === $target_lower) {
      return ['file' => $file, 'method' => 'Casse'];
    }
  }
  
  // 3. Avec espaces remplacés par underscores
  $target_underscore = str_replace(' ', '_', $target_lower);
  foreach ($local_files as $file) {
    if (strtolower($file) === $target_underscore) {
      return ['file' => $file, 'method' => 'Underscore'];
    }
  }
  
  // 4. Sans espaces du tout
  $target_nospace = str_replace(' ', '', $target_lower);
  foreach ($local_files as $file) {
    if (strtolower(str_replace(' ', '', $file)) === $target_nospace) {
      return ['file' => $file, 'method' => 'Sans espaces'];
    }
  }
  
  // 5. Extensions différentes (jpg <-> jpeg)
  $target_base = pathinfo($target_filename, PATHINFO_FILENAME);
  $target_ext = strtolower(pathinfo($target_filename, PATHINFO_EXTENSION));
  $alt_extensions = [];
  
  if ($target_ext === 'jpg') {
    $alt_extensions[] = 'jpeg';
  } elseif ($target_ext === 'jpeg') {
    $alt_extensions[] = 'jpg';
  }
  
  foreach ($local_files as $file) {
    $file_base = pathinfo($file, PATHINFO_FILENAME);
    $file_ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    
    if (strtolower($target_base) === strtolower($file_base) && in_array($file_ext, $alt_extensions)) {
      return ['file' => $file, 'method' => 'Extension diff'];
    }
  }
  
  // 6. Recherche fuzzy (similarité > 85%)
  $best_match = null;
  $best_similarity = 0;
  
  foreach ($local_files as $file) {
    similar_text($target_lower, strtolower($file), $similarity);
    if ($similarity > $best_similarity && $similarity > 85) {
      $best_similarity = $similarity;
      $best_match = $file;
    }
  }
  
  if ($best_match) {
    return ['file' => $best_match, 'method' => 'Fuzzy (' . round($best_similarity, 1) . '%)'];
  }
  
  return null;
}

function processSecteurField($secteur_string) {
  if (empty($secteur_string)) {
    return ['secteurs' => [], 'types' => []];
  }
  
  $secteurs = array_map('trim', explode(';', $secteur_string));
  $secteur_terms = [];
  $type_terms = [];
  
  // Mapping adaptés pour l'arabe (même logique mais peut inclure des termes arabes)
  $secteur_mapping = [
    'الاستقبال' => 'Transport Routier', // Accueil en arabe
    'النقل الجوي' => 'Aviation Civile', // Transport Aérien en arabe
    'الطيران المدني' => 'Aviation Civile', // Aviation Civile en arabe
    'النقل البري' => 'Transport Routier', // Transport Routier en arabe
    'النقل البحري' => 'Marine Marchande', // Transport Maritime en arabe
    'البحرية التجارية' => 'Marine Marchande', // Marine Marchande en arabe
    'السكك الحديدية' => 'Transport Routier', // Ferroviaire en arabe
    'اللوجستيك' => 'Transport Routier', // Logistique en arabe
    // Garder les mappings français aussi
    'Accueil' => 'Transport Routier',
    'Transport Aérien' => 'Aviation Civile',
    'Transport Routier' => 'Transport Routier',
    'Transport routier' => 'Transport Routier',
    'Marine Marchande' => 'Marine Marchande',
    'Ferroviaire' => 'Transport Routier',
    'Logistique' => 'Transport Routier',
  ];
  
  $type_mapping = [
    'التعاون' => 'Coopération internationale', // Coopération en arabe
    'التعاون الدولي' => 'Coopération internationale', // Coopération internationale en arabe
    'الحوكمة' => 'Gouvernance', // Gouvernance en arabe
    'البرلمان' => 'Parlement', // Parlement en arabe
    // Garder les mappings français aussi
    'Coopération' => 'Coopération internationale',
    'Gouvernance' => 'Gouvernance',
    'Parlement' => 'Parlement',
  ];
  
  foreach ($secteurs as $secteur_name) {
    if (isset($secteur_mapping[$secteur_name])) {
      $mapped_name = $secteur_mapping[$secteur_name];
      $term = getTaxonomyTermByName($mapped_name, 'modes_de_transport');
      if ($term) {
        $secteur_terms[] = ['target_id' => $term->id()];
        continue;
      }
    }
    
    if (isset($type_mapping[$secteur_name])) {
      $mapped_name = $type_mapping[$secteur_name];
      $term = getTaxonomyTermByName($mapped_name, 'type_d_actualites');
      if ($term) {
        $type_terms[] = ['target_id' => $term->id()];
        continue;
      }
    }
    
    $term = getTaxonomyTermByName($secteur_name, 'modes_de_transport');
    if ($term) {
      $secteur_terms[] = ['target_id' => $term->id()];
      continue;
    }
    
    $term = getTaxonomyTermByName($secteur_name, 'type_d_actualites');
    if ($term) {
      $type_terms[] = ['target_id' => $term->id()];
      continue;
    }
  }
  
  return ['secteurs' => $secteur_terms, 'types' => $type_terms];
}

function createMediaFromUrl($image_url, $title, $language = 'ar') { // Language par défaut 'ar' pour arabe
  if (empty($image_url)) {
    return ['success' => false, 'error' => 'URL vide'];
  }
  
  try {
    // Extract filename from URL
    $filename = basename(parse_url($image_url, PHP_URL_PATH));
    
    // Multiple URL decoding passes to handle double encoding
    $filename = urldecode($filename);
    $filename = urldecode($filename); // Some files may be double-encoded
    
    // Clean invalid characters that may appear in corrupted URLs
    $filename = preg_replace('/[<>:"\/\\|?*]/', '', $filename);
    $filename = preg_replace('/\s+/', ' ', $filename); // Normalize multiple spaces
    $filename = trim($filename);
    
    // Path to local images folder
    $local_images_path = '/var/www/mtl/images-actualites/';
    
    // Skip files with invalid names (corrupted URLs)
    if (empty($filename) || strlen($filename) < 3 || strpos($filename, '.') === false) {
      return ['success' => false, 'error' => "Nom de fichier invalide: $filename"];
    }
    
    // Try advanced matching to find the best local file match
    $match_result = null;
    $final_filename = null;
    $match_method = '';
    
    // 1. Try original filename
    $match_result = findBestLocalMatch($filename, $local_images_path);
    if ($match_result) {
      $final_filename = $match_result['file'];
      $match_method = $match_result['method'];
    } else {
      // 2. Try without accents
      $filename_no_accents = removeAccents($filename);
      $match_result = findBestLocalMatch($filename_no_accents, $local_images_path);
      if ($match_result) {
        $final_filename = $match_result['file'];
        $match_method = $match_result['method'] . ' + Sans accents';
      } else {
        // 3. Try server format with Unicode sequences
        $server_filename = str_replace(['é', 'è', 'à', 'ç', 'ù', 'ô', 'î', 'ê', 'â'], ['#U00e9', '#U00e8', '#U00e0', '#U00e7', '#U00f9', '#U00f4', '#U00ee', '#U00ea', '#U00e2'], $filename);
        $match_result = findBestLocalMatch($server_filename, $local_images_path);
        if ($match_result) {
          $final_filename = $match_result['file'];
          $match_method = $match_result['method'] . ' + Format serveur';
        }
      }
    }
    
    if (!$final_filename) {
      return ['success' => false, 'error' => "Fichier introuvable: $filename"];
    }
    
    $local_file_path = $local_images_path . $final_filename;
    $filename = $final_filename; // Update filename for Drupal
    
    // Read local file
    $file_data = file_get_contents($local_file_path);
    if ($file_data === false) {
      return ['success' => false, 'error' => "Impossible de lire: $filename"];
    }
    
    // Generate unique filename for Drupal
    $file_extension = pathinfo($filename, PATHINFO_EXTENSION);
    $base_name = pathinfo($filename, PATHINFO_FILENAME);
    $drupal_filename = 'actualite_ar_' . time() . '_' . rand(100,999) . '_' . $base_name . '.' . $file_extension;
    
    $file_repository = \Drupal::service('file.repository');
    $file = $file_repository->writeData($file_data, 'public://' . $drupal_filename);
    
    if (!$file) {
      return ['success' => false, 'error' => "Échec création fichier Drupal"];
    }
    
    $media = Media::create([
      'bundle' => 'image',
      'name' => $title,
      'field_media_image' => [
        'target_id' => $file->id(),
        'alt' => $title,
        'title' => $title
      ],
      'status' => 1,
      'langcode' => $language,
    ]);
    
    $media->save();
    return [
      'success' => true, 
      'media' => $media,
      'match_method' => $match_method,
      'final_filename' => $final_filename
    ];
    
  } catch (\Exception $e) {
    return ['success' => false, 'error' => "Exception: " . $e->getMessage()];
  }
}

function findExistingNodeArabic($title, $date = null, $content_preview = '') {
  // Chercher UNIQUEMENT les nodes arabes avec un titre et contenu similaires
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'actualite')
    ->condition('langcode', 'ar') // SEULEMENT arabe
    ->accessCheck(FALSE);
    
  $nids = $query->execute();
  
  if (!empty($nids)) {
    $nodes = Node::loadMultiple($nids);
    
    // Filtrer par titre exact d'abord
    $exact_matches = [];
    foreach ($nodes as $node) {
      if ($node->getTitle() === $title) {
        $exact_matches[] = $node;
      }
    }
    
    if (!empty($exact_matches)) {
      if (count($exact_matches) === 1) {
        return reset($exact_matches);
      }
      
      // Si plusieurs avec même titre, filtrer par date
      if ($date) {
        foreach ($exact_matches as $node) {
          $node_date = $node->get('field_date')->value;
          if ($node_date === $date) {
            return $node;
          }
        }
      }
      
      // Retourner le plus récent
      usort($exact_matches, function($a, $b) { return $b->id() <=> $a->id(); });
      return reset($exact_matches);
    }
  }
  
  return null;
}


function mergeTerms($existing_terms, $new_terms) {
  $existing_ids = [];
  foreach ($existing_terms as $term) {
    $existing_ids[] = $term['target_id'];
  }
  
  foreach ($new_terms as $new_term) {
    if (!in_array($new_term['target_id'], $existing_ids)) {
      $existing_terms[] = $new_term;
    }
  }
  
  return $existing_terms;
}

// Lire le fichier JSON arabe
$json_content = file_get_contents($json_file_path);
if ($json_content === false) {
  echo "Erreur: Impossible de lire le fichier JSON arabe\n";
  return;
}

$data = json_decode($json_content, true);
if ($data === null) {
  echo "Erreur: Le fichier JSON arabe n'est pas valide\n";
  return;
}

$total_items = count($data);
echo "Total actualités arabes à traiter: $total_items\n\n";

$created_count = 0;
$updated_count = 0;
$skipped_count = 0;
$images_success = 0;
$images_failed = 0;
$failed_images = []; // Stockage des images qui ont échoué
$successful_matches = []; // Stockage des correspondances réussies avec méthode

$start_time = microtime(true);

foreach ($data as $index => $item) {
  $current_index = $index + 1;
  echo "[$current_index/$total_items] ";
  echo substr($item['titre'], 0, 60) . "...\n";
  
  if (empty($item['titre']) || empty($item['articleHtml'])) {
    echo "  ⚠️ Ignoré: données manquantes\n\n";
    $skipped_count++;
    continue;
  }
  
  // Traiter la date
  $date_field = null;
  if (!empty($item['datePublication'])) {
    try {
      $date = \DateTime::createFromFormat('d/m/Y H:i', $item['datePublication']);
      if (!$date) {
        $date = \DateTime::createFromFormat('d/m/Y', $item['datePublication']);
      }
      if ($date) {
        $date_field = $date->format('Y-m-d');
      }
    } catch (\Exception $e) {
      // Ignorer erreurs de date
    }
  }
  
  // Vérifier existence des nodes arabes seulement
  $content_preview = substr(strip_tags($item['articleHtml']), 0, 200);
  $existing_node = findExistingNodeArabic($item['titre'], $date_field, $content_preview);
  
  if ($existing_node) {
    echo "  📝 Mise à jour (ID: " . $existing_node->id() . ")\n";
    $node = $existing_node;
    $updated_count++;
  } else {
    echo "  ✨ Création\n";
    $node = Node::create([
      'type' => 'actualite',
      'langcode' => 'ar', // Définir la langue dès la création
    ]);
    $created_count++;
  }
  
  // Définir les champs - LANGUE ARABE
  $node->set('title', $item['titre']);
  $node->set('body', [
    'value' => $item['articleHtml'],
    'format' => 'full_html',
  ]);
  $node->set('status', ($item['visible'] === 'oui') ? 1 : 0);
  // langcode déjà défini à la création
  
  if ($date_field) {
    $node->set('field_date', $date_field);
  }
  
  // Traiter taxonomies
  if (!empty($item['secteur'])) {
    $taxonomy_data = processSecteurField($item['secteur']);
    
    if (!empty($taxonomy_data['secteurs'])) {
      if ($existing_node) {
        $existing_secteurs = $node->get('field_secteur')->getValue();
        $merged_secteurs = mergeTerms($existing_secteurs, $taxonomy_data['secteurs']);
        $node->set('field_secteur', $merged_secteurs);
      } else {
        $node->set('field_secteur', $taxonomy_data['secteurs']);
      }
    }
    
    if (!empty($taxonomy_data['types'])) {
      if ($existing_node) {
        $existing_types = $node->get('field_type_d_actualites')->getValue();
        $merged_types = mergeTerms($existing_types, $taxonomy_data['types']);
        $node->set('field_type_d_actualites', $merged_types);
      } else {
        $node->set('field_type_d_actualites', $taxonomy_data['types']);
      }
    }
  }
  
  // Traiter image
  if (!empty($item['image'])) {
    echo "  🖼️ Image...";
    $media_result = createMediaFromUrl($item['image'], $item['titre'], 'ar'); // Langue arabe
    if ($media_result['success']) {
      $node->set('field_image_media', ['target_id' => $media_result['media']->id()]);
      echo " ✅\n";
      $images_success++;
      
      // Enregistrer les correspondances réussies
      if (isset($media_result['match_method']) && $media_result['match_method'] !== 'Exacte') {
        $successful_matches[] = [
          'url' => $item['image'],
          'expected' => basename(parse_url($item['image'], PHP_URL_PATH)),
          'found' => $media_result['final_filename'],
          'method' => $media_result['match_method']
        ];
      }
    } else {
      echo " ❌\n";
      $images_failed++;
      $failed_images[] = [
        'url' => $item['image'],
        'titre' => $item['titre'],
        'error' => $media_result['error'],
        'filename' => basename(parse_url($item['image'], PHP_URL_PATH))
      ];
    }
  }
  
  // Sauvegarder avec vérification de sécurité
  try {
    // Vérifier que le node est bien en arabe avant sauvegarde
    if ($node->language()->getId() !== 'ar') {
      echo "  ⚠️ Erreur: Node pas en arabe, correction...\n";
      $node->set('langcode', 'ar');
    }
    
    $node->save();
    echo "  💾 Sauvegardé (ID: " . $node->id() . ", Lang: " . $node->language()->getId() . ")\n";
  } catch (\Exception $e) {
    echo "  ❌ Erreur: " . $e->getMessage() . "\n";
    echo "  🔍 Debug - Node ID: " . ($node->id() ?? 'nouveau') . ", Lang: " . ($node->language() ? $node->language()->getId() : 'undefined') . "\n";
    $skipped_count++;
    if (!$existing_node) $created_count--;
    if ($existing_node) $updated_count--;
  }
  
  echo "\n";
  
  // Libérer mémoire et afficher progression tous les 50
  if ($current_index % 50 === 0) {
    $elapsed = microtime(true) - $start_time;
    $avg_time = $elapsed / $current_index;
    $remaining = ($total_items - $current_index) * $avg_time;
    
    echo "=== PROGRESSION ===\n";
    echo "Traités: $current_index/$total_items (" . round(($current_index/$total_items)*100, 1) . "%)\n";
    echo "Temps écoulé: " . round($elapsed/60, 1) . " min\n";
    echo "Temps restant estimé: " . round($remaining/60, 1) . " min\n";
    echo "Images réussies: $images_success\n";
    echo "Images échouées: $images_failed\n";
    echo "==================\n\n";
    
    gc_collect_cycles();
  }
}

$end_time = microtime(true);
$duration = round(($end_time - $start_time) / 60, 2);

echo "=== RÉSUMÉ FINAL ===\n";
echo "Actualités arabes créées: $created_count\n";
echo "Actualités arabes mises à jour: $updated_count\n";
echo "Actualités arabes ignorées: $skipped_count\n";
echo "Images réussies: $images_success\n";
echo "Images échouées: $images_failed\n";
echo "Temps total: {$duration} minutes\n";
echo "Vitesse: " . round($total_items / ($duration * 60), 2) . " actualités/seconde\n\n";

// === RAPPORT DES IMAGES ÉCHOUÉES ===
if (!empty($failed_images)) {
  echo "❌ === LISTE DES IMAGES ÉCHOUÉES ===\n";
  echo "Total d'images échouées: " . count($failed_images) . "\n\n";
  
  // Grouper par type d'erreur
  $errors_by_type = [];
  foreach ($failed_images as $failed) {
    $error_type = $failed['error'];
    if (!isset($errors_by_type[$error_type])) {
      $errors_by_type[$error_type] = [];
    }
    $errors_by_type[$error_type][] = $failed;
  }
  
  foreach ($errors_by_type as $error_type => $failures) {
    echo "🔍 ERREUR: $error_type (" . count($failures) . " cas)\n";
    $count = 0;
    foreach ($failures as $failed) {
      if ($count < 10) { // Limiter à 10 exemples par type d'erreur
        echo "  • " . $failed['filename'] . "\n";
        echo "    Titre: " . substr($failed['titre'], 0, 50) . "...\n";
        $count++;
      }
    }
    if (count($failures) > 10) {
      echo "  ... et " . (count($failures) - 10) . " autres\n";
    }
    echo "\n";
  }
  
  echo "💡 === ANALYSE DES ÉCHECS ===\n";
  foreach ($errors_by_type as $error_type => $failures) {
    echo "• $error_type: " . count($failures) . " cas (" . round((count($failures)/count($failed_images))*100, 1) . "%)\n";
  }
  echo "\n";
  
  echo "📋 === FICHIERS MANQUANTS (pour copie manuelle) ===\n";
  $missing_count = 0;
  foreach ($failed_images as $failed) {
    if (strpos($failed['error'], 'Fichier introuvable') !== false) {
      if ($missing_count < 50) { // Limiter à 50 fichiers listés
        echo $failed['filename'] . "\n";
      }
      $missing_count++;
    }
  }
  if ($missing_count > 50) {
    echo "... et " . ($missing_count - 50) . " autres fichiers manquants\n";
  }
  echo "Total fichiers manquants: $missing_count\n\n";
} else {
  echo "✅ === TOUTES LES IMAGES ONT RÉUSSI ===\n\n";
}

// === RAPPORT DES CORRESPONDANCES RÉUSSIES ===
if (!empty($successful_matches)) {
  echo "🎯 === CORRESPONDANCES NON-EXACTES RÉUSSIES ===\n";
  echo "Total correspondances intelligentes: " . count($successful_matches) . "\n\n";
  
  // Grouper par méthode
  $methods = [];
  foreach ($successful_matches as $match) {
    $method = $match['method'];
    if (!isset($methods[$method])) {
      $methods[$method] = [];
    }
    $methods[$method][] = $match;
  }
  
  foreach ($methods as $method => $matches) {
    echo "🔧 MÉTHODE: $method (" . count($matches) . " cas)\n";
    $count = 0;
    foreach ($matches as $match) {
      if ($count < 5) { // Limiter à 5 exemples par méthode
        echo "  • {$match['expected']} → {$match['found']}\n";
        $count++;
      }
    }
    if (count($matches) > 5) {
      echo "  ... et " . (count($matches) - 5) . " autres\n";
    }
    echo "\n";
  }
}

echo "🎉 IMPORT ARABE TERMINÉ !\n";