<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation des réglementations Aviation civile (DAC) depuis un fichier CSV
 * Usage: drush php:script import_reglementation_dac.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/mtl/Canvas Réglementation - DAC.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "Début de l'importation des réglementations Aviation civile...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 * Met à jour les termes existants s'ils n'ont pas les bons filtres
 */
function getTaxonomyTermByName($name, $vocabulary, $sector_term = null) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    $term = reset($terms);
    
    // Vérifier et mettre à jour le secteur si nécessaire
    if ($sector_term && $vocabulary === 'domaines_d_activites') {
      if ($term->hasField('field_secteur')) {
        $current_secteur = $term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $sector_term->id()) {
          $term->set('field_secteur', ['target_id' => $sector_term->id()]);
          $term->save();
          echo "Secteur mis à jour pour le terme existant: $name\n";
        }
      }
    }
    
    return $term;
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term_data = [
    'vid' => $vocabulary,
    'name' => $name,
    'langcode' => 'fr',
  ];
  
  // Ajouter le secteur si fourni (pour les domaines d'activité)
  if ($sector_term && $vocabulary === 'domaines_d_activites') {
    $term_data['field_secteur'] = ['target_id' => $sector_term->id()];
  }
  
  $term = Term::create($term_data);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour parser la date du CSV
 */
function parseDate($date_string) {
  if (empty($date_string)) {
    return null;
  }
  
  $date_string = trim($date_string);
  
  // Dictionnaire des mois français (avec et sans accents)
  $months_fr = [
    'jan' => '01', 'janv' => '01', 'janvier' => '01',
    'fév' => '02', 'fev' => '02', 'févr' => '02', 'fevr' => '02', 'février' => '02',
    'mar' => '03', 'mars' => '03',
    'avr' => '04', 'avril' => '04',
    'mai' => '05',
    'jui' => '06', 'juin' => '06',
    'jul' => '07', 'juil' => '07', 'juillet' => '07',
    'aoû' => '08', 'aou' => '08', 'août' => '08', 'aout' => '08',
    'sep' => '09', 'sept' => '09', 'septembre' => '09',
    'oct' => '10', 'octobre' => '10',
    'nov' => '11', 'novembre' => '11',
    'déc' => '12', 'dec' => '12', 'décembre' => '12'
  ];
  
  // Format "d/m/yyyy" ou "dd/mm/yyyy" (ex: "4/5/1934", "03/08/1962", "16/1/2016")
  if (preg_match('#(\d{1,2})/(\d{1,2})/(\d{4})#', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
    $year = $matches[3];
    return "$year-$month-$day";
  }
  
  // Format avec tirets (ex: "3-mai-95", "3-août-62", "26-mai-19", "19-août-10")
  if (preg_match('/(\d{1,2})[-](\w+)[-](\d{2,4})/', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Normaliser les accents
    $month_name = str_replace(['é', 'è', 'û'], ['e', 'e', 'u'], $month_name);
    
    // Convertir année à 2 chiffres en 4 chiffres
    if (strlen($year) == 2) {
      $year = (intval($year) > 50) ? "19$year" : "20$year";
    }
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  // Format avec points et espaces (ex: "6 jui. 2002", "4 avr. 2013", "21 fév. 2013", "20 fév. 2020")
  if (preg_match('/(\d{1,2})\s+(\w+)\.?\s+(\d{4})/', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Normaliser les accents
    $month_name = str_replace(['é', 'è'], ['e', 'e'], $month_name);
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  // Format avec espaces sans points (ex: "30 jan 1974", "27 mar 1959", "26 fév. 1937")
  if (preg_match('/(\d{1,2})\s+(\w+)\.?\s+(\d{4})/', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Normaliser les accents
    $month_name = str_replace(['é', 'è'], ['e', 'e'], $month_name);
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  // Dernière tentative: normaliser tous les accents et espaces
  $clean_date = preg_replace('/\s+/', ' ', trim($date_string));
  $clean_date = str_replace(['é', 'è', 'ê', 'û', 'ù', 'à', 'ç'], ['e', 'e', 'e', 'u', 'u', 'a', 'c'], $clean_date);
  
  // Format flexible avec normalisation
  if (preg_match('/(\d{1,2})[-\s]+(\w+)[-\s\.]*(\d{2,4})/', $clean_date, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month_name = strtolower($matches[2]);
    $year = $matches[3];
    
    // Convertir année à 2 chiffres en 4 chiffres
    if (strlen($year) == 2) {
      $year = (intval($year) > 50) ? "19$year" : "20$year";
    }
    
    if (isset($months_fr[$month_name])) {
      return "$year-{$months_fr[$month_name]}-$day";
    }
  }
  
  echo "Date non reconnue: '$date_string' (normalisée: '$clean_date')\n";
  return null;
}

/**
 * Fonction pour créer ou récupérer un fichier
 */
function createOrGetFile($filename, $pdf_directory = 'web/modules/custom/import_reglementation/pdf/') {
  if (empty($filename)) {
    return null;
  }
  
  $file_path = $pdf_directory . $filename;
  
  // Vérifier si le fichier existe physiquement
  if (!file_exists($file_path)) {
    // Ignorer silencieusement les fichiers manquants
    return null;
  }
  
  // Rechercher le fichier existant dans Drupal
  $files = \Drupal::entityTypeManager()
    ->getStorage('file')
    ->loadByProperties(['filename' => $filename]);
  
  if ($files) {
    return reset($files);
  }
  
  // Créer une nouvelle entité fichier
  $file_system = \Drupal::service('file_system');
  $destination = 'public://reglementation/pdf/' . $filename;
  
  // Créer le répertoire de destination s'il n'existe pas
  $directory = dirname($destination);
  $file_system->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
  
  // Copier le fichier
  $uri = $file_system->copy($file_path, $destination, FileSystemInterface::EXISTS_REPLACE);
  
  if ($uri) {
    $file = File::create([
      'filename' => $filename,
      'uri' => $uri,
      'status' => 1,
    ]);
    $file->save();
    echo "Fichier créé: $filename\n";
    return $file;
  }
  
  return null;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      return Node::load(reset($nids));
    }
  }
  
  return null;
}

// Récupérer le secteur Aviation civile
$aviation_sector = getTaxonomyTermByName('Aviation civile', 'modes_de_transport');

if (!$aviation_sector) {
  echo "Erreur: Le terme 'Aviation civile' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

// Lire et parser le fichier CSV
$handle = fopen($csv_file_path, 'r');
if (!$handle) {
  echo "Erreur: Impossible d'ouvrir le fichier CSV\n";
  return;
}

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-14)
$line_number = 0;
while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE && $line_number < 14) {
  $line_number++;
}

$imported = 0;
$updated = 0;
$errors = 0;

// Traiter chaque ligne de données
while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE) {
  $line_number++;
  
  // Vérifier que la ligne contient des données valides
  if (empty($data[5]) || empty($data[0]) || count($data) < 7) { // Intitulé FR, Type FR et colonnes minimum
    echo "Ligne $line_number ignorée: données insuffisantes\n";
    continue;
  }
  
  // Vérifier que ce n'est pas une ligne vide ou avec seulement des virgules
  $non_empty_fields = array_filter($data, function($field) {
    return !empty(trim($field));
  });
  
  if (count($non_empty_fields) < 3) {
    echo "Ligne $line_number ignorée: trop peu de données\n";
    continue;
  }
  
  try {
    // Extraire les données
    $type_fr = trim($data[0]);
    $type_ar = trim($data[1]);
    $domaine_fr = trim($data[2]);
    $domaine_ar = trim($data[3]);
    $numero = trim($data[4]);
    $titre_fr = trim($data[5]);
    $titre_ar = trim($data[6]);
    $date_publication = trim($data[7]);
    $pdf_fr = trim($data[8]);
    $pdf_ar = trim($data[9]);
    $remarques = trim($data[10]);
    
    // Récupérer ou créer le domaine d'activité depuis les données CSV
    $domaine_term = null;
    if (!empty($domaine_fr)) {
      $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites', $aviation_sector);
      
      // Ajouter le secteur au domaine d'activité s'il n'est pas déjà assigné
      if ($domaine_term && $domaine_term->hasField('field_secteur')) {
        $current_secteur = $domaine_term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $aviation_sector->id()) {
          $domaine_term->set('field_secteur', ['target_id' => $aviation_sector->id()]);
          $domaine_term->save();
          echo "Secteur Aviation civile assigné au domaine: $domaine_fr\n";
        }
      }
      
      // Ajouter ou mettre à jour la traduction arabe avec les mêmes filtres
      if (!empty($domaine_ar) && $domaine_term) {
        if (!$domaine_term->hasTranslation('ar')) {
          // Créer la traduction avec les mêmes champs de référence
          $ar_translation_data = ['name' => $domaine_ar];
          
          // Hériter du secteur du terme principal
          if ($domaine_term->hasField('field_secteur') && !$domaine_term->get('field_secteur')->isEmpty()) {
            $ar_translation_data['field_secteur'] = $domaine_term->get('field_secteur')->getValue();
          }
          
          $domaine_term->addTranslation('ar', $ar_translation_data);
          $domaine_term->save();
          echo "Traduction arabe ajoutée pour le domaine: $domaine_fr avec secteur\n";
        } else {
          // Mettre à jour la traduction existante si elle n'a pas les bons filtres
          $ar_translation = $domaine_term->getTranslation('ar');
          $needs_update = false;
          
          // Vérifier et mettre à jour le secteur
          if ($domaine_term->hasField('field_secteur') && !$domaine_term->get('field_secteur')->isEmpty()) {
            $main_secteur = $domaine_term->get('field_secteur')->getValue();
            if ($ar_translation->hasField('field_secteur')) {
              $ar_secteur = $ar_translation->get('field_secteur')->getValue();
              if ($ar_secteur !== $main_secteur) {
                $ar_translation->set('field_secteur', $main_secteur);
                $needs_update = true;
              }
            }
          }
          
          if ($needs_update) {
            $ar_translation->save();
            echo "Traduction arabe mise à jour pour le domaine: $domaine_fr\n";
          }
        }
      }
    }
    
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, $numero);
    
    // Utiliser directement le type français du CSV
    $type_name = $type_fr;
    
    // Récupérer ou créer le terme de type de loi
    $type_term = getTaxonomyTermByName($type_name, 'type');
    
    // Ajouter ou mettre à jour la traduction arabe du type avec les mêmes filtres
    if (!empty($type_ar) && $type_term) {
      if (!$type_term->hasTranslation('ar')) {
        // Créer la traduction en héritant de tous les champs du terme principal
        $ar_translation_data = ['name' => $type_ar];
        
        // Hériter des champs de référence du terme principal (secteur, etc.)
        $term_fields = $type_term->getFieldDefinitions();
        foreach ($term_fields as $field_name => $field_definition) {
          // Copier les champs de référence (entity_reference) sauf les champs système
          if ($field_definition->getType() === 'entity_reference' && 
              !in_array($field_name, ['vid', 'parent', 'revision_user']) &&
              $type_term->hasField($field_name) && 
              !$type_term->get($field_name)->isEmpty()) {
            $ar_translation_data[$field_name] = $type_term->get($field_name)->getValue();
          }
        }
        
        $type_term->addTranslation('ar', $ar_translation_data);
        $type_term->save();
        echo "Traduction arabe ajoutée pour le type: $type_name avec filtres\n";
      } else {
        // Mettre à jour la traduction existante si elle n'a pas les bons filtres
        $ar_translation = $type_term->getTranslation('ar');
        $needs_update = false;
        
        // Vérifier et synchroniser tous les champs de référence
        $term_fields = $type_term->getFieldDefinitions();
        foreach ($term_fields as $field_name => $field_definition) {
          if ($field_definition->getType() === 'entity_reference' && 
              !in_array($field_name, ['vid', 'parent', 'revision_user']) &&
              $type_term->hasField($field_name) && 
              !$type_term->get($field_name)->isEmpty()) {
            
            $main_value = $type_term->get($field_name)->getValue();
            if ($ar_translation->hasField($field_name)) {
              $ar_value = $ar_translation->get($field_name)->getValue();
              if ($ar_value !== $main_value) {
                $ar_translation->set($field_name, $main_value);
                $needs_update = true;
              }
            }
          }
        }
        
        if ($needs_update) {
          $ar_translation->save();
          echo "Traduction arabe mise à jour pour le type: $type_name\n";
        }
      }
    }
    
    // Parser la date
    $date_formatted = parseDate($date_publication);
    
    // Gérer les fichiers PDF
    $pdf_fr_file = createOrGetFile($pdf_fr);
    $pdf_ar_file = createOrGetFile($pdf_ar);
    
    // Gérer le titre long (limite de 255 caractères pour le champ title)
    // TOUJOURS utiliser field_titre_long pour les titres complets
    $title_for_node = $titre_fr;
    $use_long_title_field = true;
    
    if (mb_strlen($titre_fr, 'UTF-8') > 255) {
      $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
      echo "Titre tronqué pour: $title_for_node\n";
    }
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'reglementation',
      'title' => $title_for_node,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $aviation_sector->id()],
      'field_domaine_d_activite' => ['target_id' => $domaine_term ? $domaine_term->id() : null],
      'status' => 1,
      'uid' => 1,
      'langcode' => 'fr',
    ];
    
    // Ajouter le titre long (toujours pour les titres complets)
    if ($use_long_title_field) {
      $node_data['field_titre_long'] = ['value' => $titre_fr];
    }
    
    // Ajouter la date si disponible
    if ($date_formatted) {
      $node_data['field_date'] = ['value' => $date_formatted];
    }
    
    // Ajouter le fichier PDF (prioriser le français, sinon l'arabe)
    if ($pdf_fr_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_fr_file->id()];
    } elseif ($pdf_ar_file) {
      $node_data['field_lien_telechargement'] = ['target_id' => $pdf_ar_file->id()];
    }
    
    if ($existing_node) {
      // Mettre à jour le nœud existant et vérifier les champs manquants
      $needs_update = false;
      
      // Vérifier et mettre à jour le secteur s'il manque
      if ($existing_node->hasField('field_secteur')) {
        $current_secteur = $existing_node->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $aviation_sector->id()) {
          $existing_node->set('field_secteur', ['target_id' => $aviation_sector->id()]);
          $needs_update = true;
          echo "Secteur Aviation civile ajouté au nœud existant\n";
        }
      }
      
      // Vérifier et mettre à jour le domaine d'activité s'il manque
      if ($domaine_term && $existing_node->hasField('field_domaine_d_activite')) {
        $current_domaine = $existing_node->get('field_domaine_d_activite')->target_id;
        if (!$current_domaine || $current_domaine != $domaine_term->id()) {
          $existing_node->set('field_domaine_d_activite', ['target_id' => $domaine_term->id()]);
          $needs_update = true;
          echo "Domaine d'activité ajouté au nœud existant\n";
        }
      }
      
      // Vérifier et mettre à jour le type s'il manque
      if ($type_term && $existing_node->hasField('field_type_loi')) {
        $current_type = $existing_node->get('field_type_loi')->target_id;
        if (!$current_type || $current_type != $type_term->id()) {
          $existing_node->set('field_type_loi', ['target_id' => $type_term->id()]);
          $needs_update = true;
          echo "Type de loi ajouté au nœud existant\n";
        }
      }
      
      // Appliquer les autres mises à jour
      foreach ($node_data as $field => $value) {
        if (!in_array($field, ['type', 'field_secteur', 'field_domaine_d_activite', 'field_type_loi'])) {
          $existing_node->set($field, $value);
          $needs_update = true;
        }
      }
      
      if ($needs_update) {
        $existing_node->save();
      }
      
      // Ajouter/mettre à jour la traduction arabe avec vérification des champs manquants
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($title_ar_for_node);
          if ($ar_translation->hasField('field_titre_long')) {
            $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
          }
          
          // Vérifier que la traduction arabe a les mêmes filtres que la version française
          $ar_needs_update = false;
          
          // Synchroniser le secteur
          if ($ar_translation->hasField('field_secteur') && $existing_node->hasField('field_secteur')) {
            $fr_secteur = $existing_node->get('field_secteur')->getValue();
            $ar_secteur = $ar_translation->get('field_secteur')->getValue();
            if ($ar_secteur !== $fr_secteur) {
              $ar_translation->set('field_secteur', $fr_secteur);
              $ar_needs_update = true;
            }
          }
          
          // Synchroniser le domaine d'activité
          if ($ar_translation->hasField('field_domaine_d_activite') && $existing_node->hasField('field_domaine_d_activite')) {
            $fr_domaine = $existing_node->get('field_domaine_d_activite')->getValue();
            $ar_domaine = $ar_translation->get('field_domaine_d_activite')->getValue();
            if ($ar_domaine !== $fr_domaine) {
              $ar_translation->set('field_domaine_d_activite', $fr_domaine);
              $ar_needs_update = true;
            }
          }
          
          // Synchroniser le type de loi
          if ($ar_translation->hasField('field_type_loi') && $existing_node->hasField('field_type_loi')) {
            $fr_type = $existing_node->get('field_type_loi')->getValue();
            $ar_type = $ar_translation->get('field_type_loi')->getValue();
            if ($ar_type !== $fr_type) {
              $ar_translation->set('field_type_loi', $fr_type);
              $ar_needs_update = true;
            }
          }
          
          if ($ar_needs_update) {
            echo "Filtres synchronisés pour la traduction arabe\n";
          }
          
          $ar_translation->save();
        } else {
          // Créer la traduction avec tous les champs de filtrage
          $ar_data = ['title' => $title_ar_for_node];
          $ar_data['field_titre_long'] = ['value' => $titre_ar];
          
          // Copier tous les champs de filtrage depuis la version française
          if ($existing_node->hasField('field_secteur') && !$existing_node->get('field_secteur')->isEmpty()) {
            $ar_data['field_secteur'] = $existing_node->get('field_secteur')->getValue();
          }
          if ($existing_node->hasField('field_domaine_d_activite') && !$existing_node->get('field_domaine_d_activite')->isEmpty()) {
            $ar_data['field_domaine_d_activite'] = $existing_node->get('field_domaine_d_activite')->getValue();
          }
          if ($existing_node->hasField('field_type_loi') && !$existing_node->get('field_type_loi')->isEmpty()) {
            $ar_data['field_type_loi'] = $existing_node->get('field_type_loi')->getValue();
          }
          
          $existing_node->addTranslation('ar', $ar_data);
          $existing_node->save();
          echo "Traduction arabe créée avec tous les filtres\n";
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer un nouveau nœud
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long pour les nouveaux nœuds
        $title_ar_for_node = $titre_ar;
        
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        $ar_data = [
          'title' => $title_ar_for_node,
          'field_titre_long' => ['value' => $titre_ar] // Toujours ajouter le titre complet
        ];
        
        $node->addTranslation('ar', $ar_data);
        $node->save();
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

fclose($handle);

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";