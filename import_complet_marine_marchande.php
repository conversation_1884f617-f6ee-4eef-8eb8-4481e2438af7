<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON><PERSON>\taxonomy\Entity\Term;
use <PERSON><PERSON>al\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation complète des réglementations Marine marchande avec upload des documents
 * Usage: drush php:script import_complet_marine_marchande.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/mtl/reglementation-marine marchande.csv';

// Dossier des fichiers à uploader
$files_directory = '/var/www/mtl/drive/Portail MTL - Collecte de la réglementaire';

// Vérifier si le fichier CSV existe
if (!file_exists($csv_file_path)) {
  echo "ERREUR: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

// Vérifier si le dossier des fichiers existe
if (!is_dir($files_directory)) {
  echo "ERREUR: Le dossier des fichiers n'existe pas: $files_directory\n";
  return;
}

echo "=== DÉBUT DE L'IMPORTATION COMPLÈTE MARINE MARCHANDE ===\n";
echo "Fichier CSV: $csv_file_path\n";
echo "Dossier des fichiers: $files_directory\n\n";

// Statistiques globales
$stats = [
  'total_lines' => 0,
  'nodes_created' => 0,
  'nodes_updated' => 0,
  'files_fr_uploaded' => 0,
  'files_ar_uploaded' => 0,
  'files_existing' => 0,
  'files_errors' => 0,
  'errors' => 0,
];

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 * Met à jour les termes existants s'ils n'ont pas les bons filtres
 */
function getTaxonomyTermByName($name, $vocabulary, $sector_term = null) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    $term = reset($terms);
    
    // Vérifier et mettre à jour le secteur si nécessaire
    if ($sector_term && $vocabulary === 'domaines_d_activites') {
      if ($term->hasField('field_secteur')) {
        $current_secteur = $term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $sector_term->id()) {
          $term->set('field_secteur', ['target_id' => $sector_term->id()]);
          $term->save();
          echo "Secteur mis à jour pour le terme existant: $name\n";
        }
      }
    }
    
    return $term;
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term_data = [
    'vid' => $vocabulary,
    'name' => $name,
    'langcode' => 'fr',
  ];
  
  // Ajouter le secteur si fourni (pour les domaines d'activité)
  if ($sector_term && $vocabulary === 'domaines_d_activites') {
    $term_data['field_secteur'] = ['target_id' => $sector_term->id()];
  }
  
  $term = Term::create($term_data);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour récupérer ou créer un terme de type avec migration du contenu existant
 */
function getOrMigrateTypeTermByName($preferred_name, $vocabulary) {
  // D'abord, chercher le terme préféré (ex: 'Décret', 'Loi')
  $preferred_terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $preferred_name,
      'vid' => $vocabulary,
    ]);
  
  if ($preferred_terms) {
    $preferred_term = reset($preferred_terms);
    echo "Terme existant trouvé: $preferred_name\n";
    return $preferred_term;
  }
  
  // Si le terme préféré n'existe pas, chercher les anciens termes anglais
  $old_mapping = [
    'Décret' => ['Decree'],
    'Loi' => ['Law'],
  ];
  
  if (isset($old_mapping[$preferred_name])) {
    foreach ($old_mapping[$preferred_name] as $old_name) {
      $old_terms = \Drupal::entityTypeManager()
        ->getStorage('taxonomy_term')
        ->loadByProperties([
          'name' => $old_name,
          'vid' => $vocabulary,
        ]);
      
      if ($old_terms) {
        $old_term = reset($old_terms);
        echo "Migration: Renommage de '$old_name' vers '$preferred_name'\n";
        
        // Renommer le terme existant
        $old_term->set('name', $preferred_name);
        $old_term->save();
        
        return $old_term;
      }
    }
  }
  
  // Si aucun terme existant n'été trouvé, créer un nouveau terme
  echo "Création du terme '$preferred_name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $preferred_name,
    'langcode' => 'fr',
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour parser la date du CSV
 */
function parseDate($date_string) {
  if (empty($date_string)) {
    return null;
  }
  
  $date_string = trim($date_string);
  
  // Format "d/m/yyyy" ou "dd/mm/yyyy" (ex: "4/5/1934", "03/08/1962")
  if (preg_match('#(\d{1,2})/(\d{1,2})/(\d{4})#', $date_string, $matches)) {
    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
    $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
    $year = $matches[3];
    return "$year-$month-$day";
  }
  
  return null;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $numero) {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'reglementation')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  // Recherche alternative par numéro si disponible
  if (!empty($numero)) {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $numero . '%', 'LIKE')
      ->accessCheck(FALSE);
    
    $nids = $query->execute();
    
    if ($nids) {
      return Node::load(reset($nids));
    }
  }
  
  return null;
}

/**
 * Fonction pour normaliser les noms de fichiers selon les déformations du serveur
 */
function normalizeFilename($filename) {
  $normalized = $filename;
  
  // Étape 1: Remplacer les accents et caractères spéciaux
  $replacements = [
    'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
    'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a',
    'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
    'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
    'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
    'ç' => 'c', 'ñ' => 'n',
    'É' => 'E', 'È' => 'E', 'Ê' => 'E', 'Ë' => 'E',
    'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A',
    'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U',
    'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
    'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O',
    'Ç' => 'C', 'Ñ' => 'N',
  ];
  
  foreach ($replacements as $search => $replace) {
    $normalized = str_replace($search, $replace, $normalized);
  }
  
  // Étape 2: Supprimer les caractères spéciaux et ponctuation (comme le serveur fait)
  // GARDER les underscores qui peuvent être conservés par le serveur
  $normalized = preg_replace('/[°\/\-\(\)\[\]{}|\\\\:;,.<>?!@#$%^&*+=`~"\']/', '', $normalized);
  
  // Étape 3: Supprimer TOUS les espaces (le serveur les supprime)
  $normalized = preg_replace('/\s+/', '', $normalized);
  
  // Étape 4: Supprimer les caractères non-ASCII restants
  $normalized = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $normalized);
  
  return $normalized;
}

/**
 * Fonction pour créer une version flexible du nom de fichier (gestion des cas complexes)
 */
function createFlexibleMatch($filename) {
  $base = pathinfo($filename, PATHINFO_FILENAME);
  $ext = pathinfo($filename, PATHINFO_EXTENSION);
  
  // Normaliser sans supprimer les underscores
  $normalized = $base;
  
  // Remplacer accents
  $replacements = [
    'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
    'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a',
    'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
    'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
    'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
    'ç' => 'c', 'ñ' => 'n',
    'É' => 'E', 'È' => 'E', 'Ê' => 'E', 'Ë' => 'E',
    'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A',
    'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U',
    'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
    'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O',
    'Ç' => 'C', 'Ñ' => 'N',
  ];
  
  foreach ($replacements as $search => $replace) {
    $normalized = str_replace($search, $replace, $normalized);
  }
  
  // Supprimer caractères spéciaux SAUF underscores
  $normalized = preg_replace('/[°\/\-\(\)\[\]{}|\\\\:;,.<>?!@#$%^&*+=`~"\' ]/', '', $normalized);
  
  // Créer version sans underscores aussi
  $without_underscores = str_replace('_', '', $normalized);
  
  return [
    'with_underscores' => $normalized . '.' . $ext,
    'without_underscores' => $without_underscores . '.' . $ext,
    'pattern' => substr($without_underscores, 0, 30), // Pour matching partiel
    'short_pattern' => substr($without_underscores, 0, 15) // Pattern court pour cas difficiles
  ];
}

/**
 * Fonction pour trouver un fichier à partir du chemin du CSV
 */
function findFileFromCSVPath($base_directory, $csv_path) {
  if (empty($csv_path)) {
    echo "  → Chemin CSV vide\n";
    return null;
  }
  
  echo "  → Chemin CSV: $csv_path\n";
  
  // Essayer d'abord le chemin complet tel que dans le CSV
  $full_path = $base_directory . '/' . $csv_path;
  echo "  → Tentative chemin complet: $full_path\n";
  
  if (file_exists($full_path)) {
    echo "  ✓ Fichier trouvé par chemin complet: $full_path\n";
    return $full_path;
  }
  
  // Si le chemin complet ne marche pas, extraire juste le nom du fichier
  $filename = basename($csv_path);
  $filename_normalized = normalizeFilename($filename);
  
  echo "  → Recherche récursive du fichier: $filename\n";
  if ($filename !== $filename_normalized) {
    echo "  → Nom normalisé: $filename_normalized\n";
  }
  
  $iterator = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($base_directory, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::LEAVES_ONLY
  );
  
  foreach ($iterator as $file) {
    if ($file->isFile()) {
      $server_filename = $file->getFilename();
      
      // Essayer correspondance exacte d'abord
      if ($server_filename === $filename) {
        echo "  ✓ Fichier trouvé par correspondance exacte: " . $file->getPathname() . "\n";
        return $file->getPathname();
      }
      
      // Ensuite correspondance normalisée standard
      $server_filename_normalized = normalizeFilename($server_filename);
      if ($server_filename_normalized === $filename_normalized) {
        echo "  ✓ Fichier trouvé par correspondance normalisée: " . $file->getPathname() . "\n";
        echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
        return $file->getPathname();
      }
      
      // Essayer correspondance flexible (cas complexes avec déformations serveur)
      $filename_flexible = createFlexibleMatch($filename);
      $server_flexible = createFlexibleMatch($server_filename);
      
      // Extensions doivent correspondre
      if (pathinfo($server_filename, PATHINFO_EXTENSION) === pathinfo($filename, PATHINFO_EXTENSION)) {
        
        // 1. Correspondance exacte flexible
        if ($server_flexible['with_underscores'] === $filename_flexible['with_underscores'] || 
            $server_flexible['without_underscores'] === $filename_flexible['without_underscores']) {
          echo "  ✓ Fichier trouvé par correspondance flexible exacte: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          return $file->getPathname();
        }
        
        // 2. Le serveur contient le pattern du CSV (cas de troncature) - PATTERN LONG SEULEMENT
        if (strlen($filename_flexible['pattern']) > 15 && 
            strpos($server_flexible['without_underscores'], $filename_flexible['pattern']) !== false) {
          echo "  ✓ Fichier trouvé par pattern CSV dans serveur: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          echo "    Pattern CSV: {$filename_flexible['pattern']}\n";
          return $file->getPathname();
        }
        
        // 3. Le CSV contient le pattern du serveur (cas inverse) - PATTERN LONG SEULEMENT  
        if (strlen($server_flexible['pattern']) > 15 && 
            strpos($filename_flexible['without_underscores'], $server_flexible['pattern']) !== false) {
          echo "  ✓ Fichier trouvé par pattern serveur dans CSV: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          echo "    Pattern serveur: {$server_flexible['pattern']}\n";
          return $file->getPathname();
        }
        
        // 4. Correspondance par pattern court (pour cas très difficiles) - MINIMUM 15 CHARS
        if (strlen($filename_flexible['short_pattern']) > 15 && 
            (strpos($server_flexible['without_underscores'], $filename_flexible['short_pattern']) !== false ||
             strpos($filename_flexible['without_underscores'], $server_flexible['short_pattern']) !== false)) {
          echo "  ✓ Fichier trouvé par pattern court: " . $file->getPathname() . "\n";
          echo "    Nom serveur: $server_filename -> Nom CSV: $filename\n";
          echo "    Pattern court utilisé: {$filename_flexible['short_pattern']}\n";
          return $file->getPathname();
        }
      }
    }
  }
  
  echo "  ✗ Fichier non trouvé: $csv_path\n";
  echo "    Recherché: $filename (normalisé: $filename_normalized)\n";
  return null;
}

/**
 * Fonction pour uploader un fichier PDF dans Drupal
 */
function uploadFileToDrupal($pdf_filename, $files_directory, &$stats = null) {
  if ($stats === null) {
    global $stats;
  }
  
  if (empty($pdf_filename)) {
    echo "  → PDF filename vide\n";
    return null;
  }
  
  echo "  === UPLOAD FICHIER ===\n";
  echo "  Nom du fichier: $pdf_filename\n";
  
  // Nettoyer le chemin du fichier
  $pdf_filename = trim($pdf_filename);
  
  // Chercher le fichier à partir du chemin CSV
  $file_path = findFileFromCSVPath($files_directory, $pdf_filename);
  
  if (!$file_path || !file_exists($file_path)) {
    echo "  ✗ Fichier non trouvé ou non accessible: $pdf_filename\n";
    return null;
  }
  
  echo "  ✓ Fichier trouvé: $file_path\n";
  
  try {
    // Vérifier si le fichier existe déjà dans Drupal par nom
    $filename = basename($file_path);
    echo "  → Recherche fichier existant: $filename\n";
    
    $existing_files = \Drupal::entityTypeManager()
      ->getStorage('file')
      ->loadByProperties(['filename' => $filename]);
    
    if ($existing_files) {
      $existing_file = reset($existing_files);
      echo "  ⚡ Fichier existant trouvé dans Drupal: " . $existing_file->getFilename() . " (ID: " . $existing_file->id() . ")\n";
      $stats['files_existing']++;
      return $existing_file;
    }
    
    // Préparer le répertoire de destination
    $destination_dir = 'public://reglementation/marine-marchande/';
    \Drupal::service('file_system')->prepareDirectory($destination_dir, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
    
    $file_data = file_get_contents($file_path);
    
    if ($file_data === FALSE) {
      $stats['files_errors']++;
      return null;
    }
    
    // Copier le fichier vers Drupal
    $destination = $destination_dir . basename($file_path);
    
    // Créer l'entité fichier
    $file = \Drupal::service('file.repository')->writeData($file_data, $destination, FileSystemInterface::EXISTS_REPLACE);
    
    if ($file) {
      $file->setPermanent();
      $file->save();
      $stats['files_fr_uploaded']++;
      return $file;
    } else {
      $stats['files_errors']++;
      return null;
    }
    
  } catch (Exception $e) {
    echo "Erreur lors de l'upload: " . $e->getMessage() . "\n";
    $stats['files_errors']++;
    return null;
  }
}

// Récupérer les termes existants pour Marine marchande
$marine_marchande_sector = getTaxonomyTermByName('Marine marchande', 'modes_de_transport');

if (!$marine_marchande_sector) {
  echo "Erreur: Le terme 'Marine marchande' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

// Lire le fichier CSV complet pour traitement personnalisé
$csv_content = file_get_contents($csv_file_path);
if (!$csv_content) {
  echo "Erreur: Impossible de lire le fichier CSV\n";
  return;
}

// Diviser en lignes et nettoyer les retours à la ligne dans les cellules
$lines = explode("\n", $csv_content);

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-16)
$data_lines = array_slice($lines, 16);

// Fonction pour parser une ligne CSV malformée
function parseCSVLine($line) {
  $data = str_getcsv($line, ',', '"');
  
  foreach ($data as &$cell) {
    $cell = trim(str_replace(["\n", "\r"], ' ', $cell));
  }
  
  return $data;
}

// Reconstruire les lignes brisées par les retours à la ligne
$reconstructed_lines = [];
$current_line = '';

foreach ($data_lines as $line) {
  $line = trim($line);
  
  if (empty($line)) {
    continue;
  }
  
  $current_line .= ($current_line ? ' ' : '') . $line;
  $quote_count = substr_count($current_line, '"');
  
  if ($quote_count % 2 == 0) {
    $reconstructed_lines[] = $current_line;
    $current_line = '';
  }
}

if (!empty($current_line)) {
  $reconstructed_lines[] = $current_line;
}

$line_number = 16; // Commencer après les en-têtes

// Traiter chaque ligne de données
foreach ($reconstructed_lines as $line) {
  $line_number++;
  $stats['total_lines']++;
  
  if (empty(trim($line))) {
    continue;
  }
  
  $data = parseCSVLine($line);
  
  // Vérifier que la ligne contient toutes les colonnes attendues
  if (count($data) < 11) {
    echo "Ligne $line_number ignorée (colonnes incomplètes: " . count($data) . ")\n";
    continue;
  }
  
  // Vérifier que la ligne contient des données valides (titre français obligatoire)
  if (empty($data[5])) { // Intitulé FR
    echo "Ligne $line_number ignorée (titre français vide)\n";
    continue;
  }
  
  try {
    // Extraire les données
    $type_fr = trim($data[0]);
    $type_ar = trim($data[1]);
    $domaine_fr = trim($data[2]);
    $domaine_ar = trim($data[3]);
    $numero = trim($data[4]);
    $titre_fr = trim($data[5]);
    $titre_ar = isset($data[6]) ? trim(str_replace(["\n", "\r"], ' ', $data[6])) : '';
    $date_publication = trim($data[7]);
    $pdf_fr = trim($data[8]);
    $pdf_ar = trim($data[9]);
    $remarques = isset($data[10]) ? trim($data[10]) : '';
    
    // Récupérer ou créer le domaine d'activité depuis les données CSV
    $domaine_term = null;
    if (!empty($domaine_fr)) {
      $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites', $marine_marchande_sector);
      
      // Ajouter le secteur au domaine d'activité s'il n'est pas déjà assigné
      if ($domaine_term && $domaine_term->hasField('field_secteur')) {
        $current_secteur = $domaine_term->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $marine_marchande_sector->id()) {
          $domaine_term->set('field_secteur', ['target_id' => $marine_marchande_sector->id()]);
          $domaine_term->save();
          echo "Secteur Marine marchande assigné au domaine: $domaine_fr\n";
        }
      }
      
      // Ajouter ou mettre à jour la traduction arabe avec les mêmes filtres
      if (!empty($domaine_ar) && $domaine_term) {
        if (!$domaine_term->hasTranslation('ar')) {
          $ar_translation_data = ['name' => $domaine_ar];
          
          if ($domaine_term->hasField('field_secteur') && !$domaine_term->get('field_secteur')->isEmpty()) {
            $ar_translation_data['field_secteur'] = $domaine_term->get('field_secteur')->getValue();
          }
          
          $domaine_term->addTranslation('ar', $ar_translation_data);
          $domaine_term->save();
          echo "Traduction arabe ajoutée pour le domaine: $domaine_fr avec secteur\n";
        }
      }
    }
    
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, $numero);
    
    // Mapper les types français vers les termes existants
    $type_mapping = [
      'Dahir' => 'Dahir',
      'Loi organique' => 'Loi',
      'Loi' => 'Loi',
      'DECRET ROYAL' => 'Décret',
      'Décret royal' => 'Décret',
      'Décret' => 'Décret',
      'DECRET' => 'Décret',
      'Arrêté royal' => 'Arrêté',
      'Arrêté' => 'Arrêté',
      'Circulaire' => 'Circulaire',
      'Cahier des charges' => 'Cahier des charges',
    ];
    
    $mapped_type = isset($type_mapping[$type_fr]) ? $type_mapping[$type_fr] : $type_fr;
    
    if (!$mapped_type) {
      echo "Attention: Type '$type_fr' non mappé, ignoré\n";
      continue;
    }
    
    // Récupérer ou créer le terme de type de loi
    $type_term = getOrMigrateTypeTermByName($mapped_type, 'type');
    
    // Ajouter ou mettre à jour la traduction arabe du type
    if (!empty($type_ar) && $type_term) {
      if (!$type_term->hasTranslation('ar')) {
        $ar_translation_data = ['name' => $type_ar];
        $type_term->addTranslation('ar', $ar_translation_data);
        $type_term->save();
        echo "Traduction arabe ajoutée pour le type: $mapped_type\n";
      }
    }
    
    // Parser la date
    $date_formatted = parseDate($date_publication);
    
    // Gérer le titre long (limite de 255 caractères pour le champ title)
    $title_for_node = $titre_fr;
    $long_title = null;
    
    if (mb_strlen($titre_fr, 'UTF-8') > 255) {
      $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
      $long_title = $titre_fr;
      echo "Titre tronqué pour: $title_for_node\n";
    }
    
    // Uploader les fichiers PDF
    $file_fr = null;
    $file_ar = null;
    $files_to_attach = [];
    
    if (!empty($pdf_fr)) {
      $file_fr = uploadFileToDrupal($pdf_fr, $files_directory, $stats);
      if ($file_fr) {
        $files_to_attach[] = [
          'target_id' => $file_fr->id(),
          'description' => 'Version française',
        ];
      }
    }
    
    if (!empty($pdf_ar)) {
      $file_ar = uploadFileToDrupal($pdf_ar, $files_directory, $stats);
      if ($file_ar) {
        $files_to_attach[] = [
          'target_id' => $file_ar->id(),
          'description' => 'Version arabe',
        ];
      }
    }
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'reglementation',
      'title' => $title_for_node,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $marine_marchande_sector->id()],
      'status' => 1,
      'uid' => 1,
    ];
    
    // Ajouter le domaine d'activité si disponible
    if ($domaine_term) {
      $node_data['field_domaine_d_activite'] = ['target_id' => $domaine_term->id()];
    }
    
    // Ajouter le titre long si nécessaire
    if ($long_title) {
      $node_data['field_titre_long'] = ['value' => $long_title];
    }
    
    // Ajouter la date si disponible
    if ($date_formatted) {
      $node_data['field_date'] = ['value' => $date_formatted];
    }
    
    // Ajouter les fichiers si disponibles
    if (!empty($files_to_attach)) {
      $node_data['field_lien_telechargement'] = $files_to_attach;
    }
    
    if ($existing_node) {
      // Mettre à jour le nœud existant et vérifier les champs manquants
      $needs_update = false;
      
      // Vérifier et mettre à jour le secteur s'il manque
      if ($existing_node->hasField('field_secteur')) {
        $current_secteur = $existing_node->get('field_secteur')->target_id;
        if (!$current_secteur || $current_secteur != $marine_marchande_sector->id()) {
          $existing_node->set('field_secteur', ['target_id' => $marine_marchande_sector->id()]);
          $needs_update = true;
          echo "Secteur Marine marchande ajouté au nœud existant\n";
        }
      }
      
      // Vérifier et mettre à jour le domaine d'activité s'il manque
      if ($domaine_term && $existing_node->hasField('field_domaine_d_activite')) {
        $current_domaine = $existing_node->get('field_domaine_d_activite')->target_id;
        if (!$current_domaine || $current_domaine != $domaine_term->id()) {
          $existing_node->set('field_domaine_d_activite', ['target_id' => $domaine_term->id()]);
          $needs_update = true;
          echo "Domaine d'activité ajouté au nœud existant\n";
        }
      }
      
      // Vérifier et mettre à jour le type s'il manque
      if ($type_term && $existing_node->hasField('field_type_loi')) {
        $current_type = $existing_node->get('field_type_loi')->target_id;
        if (!$current_type || $current_type != $type_term->id()) {
          $existing_node->set('field_type_loi', ['target_id' => $type_term->id()]);
          $needs_update = true;
          echo "Type de loi ajouté au nœud existant\n";
        }
      }
      
      // Ajouter les fichiers s'ils n'existent pas
      if (!empty($files_to_attach) && $existing_node->hasField('field_lien_telechargement')) {
        $current_files = $existing_node->get('field_lien_telechargement')->getValue();
        $existing_file_ids = array_column($current_files, 'target_id');
        
        $new_files = [];
        foreach ($files_to_attach as $file_info) {
          if (!in_array($file_info['target_id'], $existing_file_ids)) {
            $new_files[] = $file_info;
          }
        }
        
        if (!empty($new_files)) {
          $all_files = array_merge($current_files, $new_files);
          $existing_node->set('field_lien_telechargement', $all_files);
          $needs_update = true;
          echo "Fichiers ajoutés au nœud existant\n";
        }
      }
      
      // Appliquer les autres mises à jour
      foreach ($node_data as $field => $value) {
        if (!in_array($field, ['type', 'field_secteur', 'field_domaine_d_activite', 'field_type_loi', 'field_lien_telechargement'])) {
          $existing_node->set($field, $value);
          $needs_update = true;
        }
      }
      
      if ($needs_update) {
        $existing_node->save();
      }
      
      // Ajouter/mettre à jour la traduction arabe avec vérification des champs manquants
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($title_ar_for_node);
          
          // Ajouter le titre long arabe si le titre français en a un
          if ($existing_node->hasField('field_titre_long') && !$existing_node->get('field_titre_long')->isEmpty() && $ar_translation->hasField('field_titre_long')) {
            $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
          }
          
          $ar_translation->save();
        } else {
          // Créer la traduction avec tous les champs de filtrage
          $ar_data = ['title' => $title_ar_for_node];
          
          // Ajouter le titre long arabe si le titre français en a un
          if ($existing_node->hasField('field_titre_long') && !$existing_node->get('field_titre_long')->isEmpty()) {
            $ar_data['field_titre_long'] = ['value' => $titre_ar];
          }
          
          // Copier tous les champs de filtrage depuis la version française
          if ($existing_node->hasField('field_secteur') && !$existing_node->get('field_secteur')->isEmpty()) {
            $ar_data['field_secteur'] = $existing_node->get('field_secteur')->getValue();
          }
          if ($existing_node->hasField('field_domaine_d_activite') && !$existing_node->get('field_domaine_d_activite')->isEmpty()) {
            $ar_data['field_domaine_d_activite'] = $existing_node->get('field_domaine_d_activite')->getValue();
          }
          if ($existing_node->hasField('field_type_loi') && !$existing_node->get('field_type_loi')->isEmpty()) {
            $ar_data['field_type_loi'] = $existing_node->get('field_type_loi')->getValue();
          }
          
          $existing_node->addTranslation('ar', $ar_data);
          $existing_node->save();
          echo "Traduction arabe créée avec tous les filtres\n";
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $stats['nodes_updated']++;
    } else {
      // Créer un nouveau nœud avec les fichiers
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter la traduction arabe avec tous les filtres
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long pour les nouveaux nœuds
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        $ar_data = ['title' => $title_ar_for_node];
        
        // Ajouter le titre long arabe si le titre français en a un
        if ($node->hasField('field_titre_long') && !$node->get('field_titre_long')->isEmpty()) {
          $ar_data['field_titre_long'] = ['value' => $titre_ar];
        }
        
        // Copier tous les champs de filtrage depuis le nœud principal
        if ($node->hasField('field_secteur') && !$node->get('field_secteur')->isEmpty()) {
          $ar_data['field_secteur'] = $node->get('field_secteur')->getValue();
        }
        if ($domaine_term && $node->hasField('field_domaine_d_activite')) {
          $ar_data['field_domaine_d_activite'] = $node->get('field_domaine_d_activite')->getValue();
        }
        if ($type_term && $node->hasField('field_type_loi')) {
          $ar_data['field_type_loi'] = $node->get('field_type_loi')->getValue();
        }
        
        $node->addTranslation('ar', $ar_data);
        $node->save();
        echo "Traduction arabe créée avec tous les filtres\n";
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $stats['nodes_created']++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $stats['errors']++;
  }
}

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: {$stats['nodes_created']}\n";
echo "Contenus mis à jour: {$stats['nodes_updated']}\n";
echo "Fichiers français uploadés (nouveaux): {$stats['files_fr_uploaded']}\n";
echo "Fichiers français existants (réutilisés): {$stats['files_existing']}\n";
echo "Total fichiers traités: " . ($stats['files_fr_uploaded'] + $stats['files_existing']) . "\n";
echo "Erreurs fichiers: {$stats['files_errors']}\n";
echo "Erreurs: {$stats['errors']}\n";
echo "Total contenus traités: " . ($stats['nodes_created'] + $stats['nodes_updated']) . "\n";
echo "Importation terminée!\n";