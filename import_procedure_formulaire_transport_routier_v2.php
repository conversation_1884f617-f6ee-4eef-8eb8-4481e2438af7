<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\taxonomy\Entity\Term;
use Drupal\taxonomy\Entity\Vocabulary;
use Drupal\field\Entity\FieldStorageConfig;
use Drupal\field\Entity\FieldConfig;
use Drupal\Core\Entity\Entity\EntityFormDisplay;
use Drupal\Core\Entity\Entity\EntityViewDisplay;

/**
 * Script d'importation des procédures et formulaires avec nouveaux vocabulaires
 * Usage: drush php:script import_procedure_formulaire_transport_routier_v2.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/html/mtl/Canva Procédures et Formulaires.xlsx - Transport routier.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "Début de la configuration et importation des procédures et formulaires...\n";

/**
 * Fonction pour créer un vocabulaire s'il n'existe pas
 */
function createVocabularyIfNotExists($vid, $name, $description = '') {
  $vocabulary = Vocabulary::load($vid);
  
  if (!$vocabulary) {
    echo "Création du vocabulaire '$vid' ($name)\n";
    $vocabulary = Vocabulary::create([
      'vid' => $vid,
      'name' => $name,
      'description' => $description,
    ]);
    $vocabulary->save();
  }
  
  return $vocabulary;
}

/**
 * Fonction pour créer un champ de référence taxonomique
 */
function createTaxonomyField($entity_type, $bundle, $field_name, $field_label, $vocabulary_id, $required = false) {
  // Créer le storage du champ s'il n'existe pas
  $field_storage = FieldStorageConfig::loadByName($entity_type, $field_name);
  if (!$field_storage) {
    echo "Création du storage pour le champ '$field_name'\n";
    $field_storage = FieldStorageConfig::create([
      'field_name' => $field_name,
      'entity_type' => $entity_type,
      'type' => 'entity_reference',
      'settings' => [
        'target_type' => 'taxonomy_term',
      ],
    ]);
    $field_storage->save();
  }
  
  // Créer l'instance du champ s'il n'existe pas
  $field_config = FieldConfig::loadByName($entity_type, $bundle, $field_name);
  if (!$field_config) {
    echo "Création du champ '$field_name' pour le bundle '$bundle'\n";
    $field_config = FieldConfig::create([
      'field_storage' => $field_storage,
      'bundle' => $bundle,
      'label' => $field_label,
      'required' => $required,
      'settings' => [
        'handler' => 'default:taxonomy_term',
        'handler_settings' => [
          'target_bundles' => [
            $vocabulary_id => $vocabulary_id,
          ],
          'sort' => [
            'field' => 'name',
            'direction' => 'asc',
          ],
          'auto_create' => false,
        ],
      ],
    ]);
    $field_config->save();
    
    // Ajouter le champ au formulaire d'édition
    $form_display = EntityFormDisplay::load($entity_type . '.' . $bundle . '.default');
    if ($form_display && !$form_display->getComponent($field_name)) {
      echo "Ajout du champ '$field_name' au formulaire d'édition\n";
      $form_display->setComponent($field_name, [
        'type' => 'options_select',
        'weight' => 10,
      ])->save();
    }
    
    // Ajouter le champ à l'affichage
    $view_display = EntityViewDisplay::load($entity_type . '.' . $bundle . '.default');
    if ($view_display && !$view_display->getComponent($field_name)) {
      echo "Ajout du champ '$field_name' à l'affichage\n";
      $view_display->setComponent($field_name, [
        'type' => 'entity_reference_label',
        'weight' => 10,
      ])->save();
    }
  }
  
  return $field_config;
}

/**
 * Fonction pour récupérer ou créer un terme de taxonomie
 */
function getTaxonomyTermByName($name, $vocabulary) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    return reset($terms);
  }
  
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $name,
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $type = 'procedure_formulaire') {
  $query = \Drupal::entityQuery('node')
    ->condition('type', $type)
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  return null;
}

// === ÉTAPE 1: CRÉATION DES VOCABULAIRES ===
echo "\n=== CRÉATION DES VOCABULAIRES ===\n";

// Créer le vocabulaire "Type de procédure"
$type_procedure_vocab = createVocabularyIfNotExists(
  'type_de_procedure',
  'Type de procédure',
  'Vocabulaire pour distinguer entre Procédures et Formulaires'
);

// Créer le vocabulaire "Domaines d'activité pour procédure et formulaire"
$domaines_pf_vocab = createVocabularyIfNotExists(
  'domaines_activite_pf',
  'Domaines d\'activité pour procédure et formulaire',
  'Domaines d\'activité spécifiques aux procédures et formulaires'
);

// === ÉTAPE 2: CRÉATION DES CHAMPS ===
echo "\n=== CRÉATION DES CHAMPS ===\n";

// Créer le champ pour le type de procédure
createTaxonomyField(
  'node',
  'procedure_formulaire',
  'field_type_de_procedure',
  'Type de procédure',
  'type_de_procedure',
  true
);

// Créer le champ pour les domaines d'activité PF
createTaxonomyField(
  'node',
  'procedure_formulaire',
  'field_domaines_activite_pf',
  'Domaines d\'activité (P&F)',
  'domaines_activite_pf',
  false
);

// === ÉTAPE 3: PRÉPARATION DES TERMES DE BASE ===
echo "\n=== CRÉATION DES TERMES DE BASE ===\n";

// Créer les termes de type de procédure
$terme_procedure = getTaxonomyTermByName('Procédures', 'type_de_procedure');
$terme_formulaire = getTaxonomyTermByName('Formulaires', 'type_de_procedure');

// Récupérer le secteur Transport Routier
$transport_routier_sector = getTaxonomyTermByName('Transport Routier', 'modes_de_transport');
if (!$transport_routier_sector) {
  $transport_routier_sector = getTaxonomyTermByName('Transport routier', 'modes_de_transport');
}

if (!$transport_routier_sector) {
  echo "Erreur: Le terme 'Transport Routier' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

echo "Secteur Transport Routier trouvé: ID {$transport_routier_sector->id()}\n";

// === ÉTAPE 4: LECTURE ET TRAITEMENT DU CSV ===
echo "\n=== IMPORTATION DES DONNÉES CSV ===\n";

if (($handle = fopen($csv_file_path, 'r')) === FALSE) {
  echo "Erreur: Impossible d'ouvrir le fichier CSV\n";
  return;
}

$imported = 0;
$updated = 0;
$errors = 0;
$skipped = 0;
$line_number = 0;

// Ignorer la ligne d'en-tête
fgetcsv($handle);
$line_number++;

echo "En-tête ignoré, début du traitement des données...\n";

while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE) {
  $line_number++;
  
  // Si la ligne a moins de 8 colonnes, l'ignorer
  if (count($data) < 8) {
    echo "Ligne $line_number ignorée (colonnes incomplètes: " . count($data) . ")\n";
    $skipped++;
    continue;
  }
  
  // Nettoyer et extraire les données
  $type_fr = trim($data[0] ?? '');
  $type_ar = trim($data[1] ?? '');
  $domaine_fr = trim($data[2] ?? '');
  $domaine_ar = trim($data[3] ?? '');
  $titre_fr = trim($data[4] ?? '');
  $titre_ar = trim($data[5] ?? '');
  $pdf_fr = trim($data[6] ?? '');
  $pdf_ar = trim($data[7] ?? '');
  
  // Vérifier si c'est une ligne d'exemple
  if (empty($type_fr) || $type_fr === 'Ex' || strpos($type_fr, 'Ex  :') !== false) {
    echo "Ligne $line_number ignorée (exemple détecté)\n";
    $skipped++;
    continue;
  }
  
  // Vérifier les données minimales
  if (empty($titre_fr)) {
    echo "Ligne $line_number ignorée (titre français vide)\n";
    $skipped++;
    continue;
  }
  
  if (empty($type_fr) || empty($domaine_fr)) {
    echo "Ligne $line_number ignorée (type ou domaine vide)\n";
    $skipped++;
    continue;
  }
  
  try {
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, 'procedure_formulaire');
    
    // Récupérer ou créer le terme de type de procédure
    $type_procedure_term = getTaxonomyTermByName($type_fr, 'type_de_procedure');
    
    // Récupérer ou créer le domaine d'activité PF
    $domaine_pf_term = getTaxonomyTermByName($domaine_fr, 'domaines_activite_pf');
    
    // Récupérer ou créer le domaine d'activité général (ancien système)
    $domaine_general_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites');
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'procedure_formulaire',
      'title' => $titre_fr,
      'field_type_de_procedure' => ['target_id' => $type_procedure_term->id()],
      'field_domaines_activite_pf' => ['target_id' => $domaine_pf_term->id()],
      'field_secteur' => ['target_id' => $transport_routier_sector->id()],
      'field_domaine_d_activite' => ['target_id' => $domaine_general_term->id()],
      'status' => 1,
    ];
    
    if ($existing_node) {
      // Mettre à jour
      foreach ($node_data as $field => $value) {
        if ($field !== 'type') {
          $existing_node->set($field, $value);
        }
      }
      $existing_node->save();
      
      // Ajouter traduction arabe
      if (!empty($titre_ar)) {
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($titre_ar);
          $ar_translation->save();
        } else {
          $existing_node->addTranslation('ar', ['title' => $titre_ar]);
          $existing_node->save();
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer nouveau
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter traduction arabe
      if (!empty($titre_ar)) {
        $node->addTranslation('ar', ['title' => $titre_ar]);
        $node->save();
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

fclose($handle);

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Lignes ignorées: $skipped\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";

echo "\n=== VOCABULAIRES CRÉÉS ===\n";
echo "- type_de_procedure : Type de procédure\n";
echo "- domaines_activite_pf : Domaines d'activité pour procédure et formulaire\n";

echo "\n=== CHAMPS CRÉÉS ===\n";
echo "- field_type_de_procedure : Type de procédure (requis)\n";
echo "- field_domaines_activite_pf : Domaines d'activité (P&F)\n";

echo "\nImportation terminée avec succès!\n";