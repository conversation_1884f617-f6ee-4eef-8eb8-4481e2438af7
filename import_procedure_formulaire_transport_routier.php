<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\taxonomy\Entity\Term;
use Dr<PERSON>al\taxonomy\Entity\Vocabulary;
use Drupal\field\Entity\FieldStorageConfig;
use Drupal\field\Entity\FieldConfig;
use Drupal\Core\Entity\Entity\EntityFormDisplay;
use Drupal\Core\Entity\Entity\EntityViewDisplay;

/**
 * Script d'importation des procédures et formulaires avec création automatique des vocabulaires et champs
 * Usage: drush php:script import_procedure_formulaire_transport_routier_final.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/mtl/Canva Procédures et Formulaires.xlsx - Transport routier.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "=== SCRIPT D'IMPORT PROCÉDURES & FORMULAIRES ===\n";
echo "Début de la configuration automatique et importation...\n";

/**
 * Fonction pour créer un vocabulaire s'il n'existe pas
 */
function createVocabularyIfNotExists($vid, $name, $description = '') {
  $vocabulary = Vocabulary::load($vid);
  
  if (!$vocabulary) {
    echo "✓ Création du vocabulaire '$vid' ($name)\n";
    $vocabulary = Vocabulary::create([
      'vid' => $vid,
      'name' => $name,
      'description' => $description,
      'langcode' => 'fr',
    ]);
    $vocabulary->save();
  } else {
    echo "✓ Vocabulaire '$vid' existe déjà\n";
  }
  
  return $vocabulary;
}

/**
 * Fonction pour créer un champ de référence taxonomique
 */
function createTaxonomyField($entity_type, $bundle, $field_name, $field_label, $vocabulary_id, $required = false) {
  // Vérifier si le storage du champ existe
  $field_storage = FieldStorageConfig::loadByName($entity_type, $field_name);
  if (!$field_storage) {
    echo "✓ Création du storage pour le champ '$field_name'\n";
    $field_storage = FieldStorageConfig::create([
      'field_name' => $field_name,
      'entity_type' => $entity_type,
      'type' => 'entity_reference',
      'settings' => [
        'target_type' => 'taxonomy_term',
      ],
      'cardinality' => 1,
    ]);
    $field_storage->save();
  }
  
  // Vérifier si l'instance du champ existe
  $field_config = FieldConfig::loadByName($entity_type, $bundle, $field_name);
  if (!$field_config) {
    echo "✓ Création du champ '$field_name' pour le bundle '$bundle'\n";
    $field_config = FieldConfig::create([
      'field_storage' => $field_storage,
      'bundle' => $bundle,
      'label' => $field_label,
      'required' => $required,
      'settings' => [
        'handler' => 'default:taxonomy_term',
        'handler_settings' => [
          'target_bundles' => [
            $vocabulary_id => $vocabulary_id,
          ],
          'sort' => [
            'field' => 'name',
            'direction' => 'asc',
          ],
          'auto_create' => false,
        ],
      ],
    ]);
    $field_config->save();
    
    // Ajouter le champ au formulaire d'édition
    $form_display = EntityFormDisplay::load($entity_type . '.' . $bundle . '.default');
    if ($form_display) {
      $component = $form_display->getComponent($field_name);
      if (!$component) {
        echo "✓ Ajout du champ '$field_name' au formulaire d'édition\n";
        $form_display->setComponent($field_name, [
          'type' => 'options_select',
          'weight' => 10,
          'settings' => [],
          'third_party_settings' => [],
        ])->save();
      }
    }
    
    // Ajouter le champ à l'affichage
    $view_display = EntityViewDisplay::load($entity_type . '.' . $bundle . '.default');
    if ($view_display) {
      $component = $view_display->getComponent($field_name);
      if (!$component) {
        echo "✓ Ajout du champ '$field_name' à l'affichage\n";
        $view_display->setComponent($field_name, [
          'type' => 'entity_reference_label',
          'weight' => 10,
          'settings' => [
            'link' => true,
          ],
          'third_party_settings' => [],
        ])->save();
      }
    }
  } else {
    echo "✓ Champ '$field_name' existe déjà\n";
  }
  
  return $field_config;
}

/**
 * Fonction pour récupérer ou créer un terme de taxonomie avec traduction arabe
 */
function getTaxonomyTermByName($name_fr, $vocabulary, $name_ar = '') {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name_fr,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    $term = reset($terms);
    // Ajouter la traduction arabe si elle n'existe pas et qu'elle est fournie
    if (!empty($name_ar) && !$term->hasTranslation('ar')) {
      echo "→ Ajout traduction arabe pour '$name_fr': $name_ar\n";
      $term->addTranslation('ar', ['name' => $name_ar])->save();
    }
    return $term;
  }
  
  echo "→ Création du terme '$name_fr' dans '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $name_fr,
    'langcode' => 'fr',
  ]);
  $term->save();
  
  // Ajouter la traduction arabe si fournie
  if (!empty($name_ar)) {
    echo "→ Ajout traduction arabe pour '$name_fr': $name_ar\n";
    $term->addTranslation('ar', ['name' => $name_ar])->save();
  }
  
  return $term;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $type = 'procedure_formulaire') {
  $query = \Drupal::entityQuery('node')
    ->condition('type', $type)
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  return null;
}

// === ÉTAPE 1: CRÉATION DES VOCABULAIRES ===
echo "\n--- ÉTAPE 1: CRÉATION DES VOCABULAIRES ---\n";

$type_procedure_vocab = createVocabularyIfNotExists(
  'type_de_procedure',
  'Type de procédure',
  'Vocabulaire pour distinguer entre Procédures et Formulaires'
);

$domaines_pf_vocab = createVocabularyIfNotExists(
  'domaines_activite_pf',
  'Domaines d\'activité pour P&F',
  'Domaines d\'activité spécifiques aux procédures et formulaires'
);

// === ÉTAPE 2: CONFIGURATION MULTILINGUE ===
echo "\n--- ÉTAPE 2: CONFIGURATION MULTILINGUE ---\n";

// Activer la traduction de contenu pour procedure_formulaire
$config = \Drupal::configFactory()->getEditable('language.content_settings.node.procedure_formulaire');
$config->set('third_party_settings.content_translation.enabled', true);
$config->set('language_alterable', true);
$config->save();
echo "✓ Traduction de contenu activée pour procedure_formulaire\n";

// Configurer le champ title comme translatable
$title_config = \Drupal::configFactory()->getEditable('core.base_field_override.node.procedure_formulaire.title');
if ($title_config->isNew()) {
  $title_config->set('langcode', 'fr');
  $title_config->set('status', true);
  $title_config->set('dependencies.config', ['node.type.procedure_formulaire']);
  $title_config->set('id', 'node.procedure_formulaire.title');
  $title_config->set('field_name', 'title');
  $title_config->set('entity_type', 'node');
  $title_config->set('bundle', 'procedure_formulaire');
  $title_config->set('label', 'Titre');
  $title_config->set('required', true);
  $title_config->set('translatable', true);
  $title_config->set('field_type', 'string');
} else {
  $title_config->set('translatable', true);
}
$title_config->save();
echo "✓ Champ title configuré comme translatable\n";

// === ÉTAPE 3: CRÉATION DES CHAMPS ===
echo "\n--- ÉTAPE 3: CRÉATION DES CHAMPS ---\n";

createTaxonomyField(
  'node',
  'procedure_formulaire',
  'field_type_de_procedure',
  'Type de procédure',
  'type_de_procedure',
  true
);

createTaxonomyField(
  'node',
  'procedure_formulaire',
  'field_domaines_activite_pf',
  'Domaines d\'activité (P&F)',
  'domaines_activite_pf',
  false
);

// === ÉTAPE 4: CRÉATION DES TERMES DE BASE ===
echo "\n--- ÉTAPE 4: CRÉATION DES TERMES DE BASE ---\n";

$terme_procedure = getTaxonomyTermByName('Procédures', 'type_de_procedure', 'الإجراءات');
$terme_formulaire = getTaxonomyTermByName('Formulaires', 'type_de_procedure', 'الاستمارات');

// Récupérer le secteur Transport Routier existant
$transport_routier_sector = null;
$possible_names = ['Transport Routier', 'Transport routier', 'Road Transport'];

foreach ($possible_names as $name) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => 'modes_de_transport',
    ]);
  
  if ($terms) {
    $transport_routier_sector = reset($terms);
    echo "✓ Secteur '$name' trouvé (ID: {$transport_routier_sector->id()})\n";
    break;
  }
}

if (!$transport_routier_sector) {
  echo "⚠ Création du secteur 'Transport Routier'\n";
  $transport_routier_sector = getTaxonomyTermByName('Transport Routier', 'modes_de_transport');
}

// === ÉTAPE 5: TRAITEMENT DU CSV ===
echo "\n--- ÉTAPE 5: IMPORTATION DES DONNÉES CSV ---\n";

if (($handle = fopen($csv_file_path, 'r')) === FALSE) {
  echo "❌ Erreur: Impossible d'ouvrir le fichier CSV\n";
  return;
}

$imported = 0;
$updated = 0;
$errors = 0;
$skipped = 0;
$line_number = 0;

// Ignorer la ligne d'en-tête
fgetcsv($handle);
$line_number++;

echo "Début du traitement des données...\n";

while (($data = fgetcsv($handle, 0, ',', '"')) !== FALSE) {
  $line_number++;
  
  // Vérifier le nombre de colonnes
  if (count($data) < 8) {
    echo "⚠ Ligne $line_number ignorée (colonnes incomplètes: " . count($data) . ")\n";
    $skipped++;
    continue;
  }
  
  // Nettoyer les données
  $type_fr = trim($data[0] ?? '');
  $type_ar = trim($data[1] ?? '');
  $domaine_fr = trim($data[2] ?? '');
  $domaine_ar = trim($data[3] ?? '');
  $titre_fr = trim($data[4] ?? '');
  $titre_ar = trim($data[5] ?? '');
  $pdf_fr = trim($data[6] ?? '');
  $pdf_ar = trim($data[7] ?? '');
  
  // Ignorer les lignes d'exemple
  if (empty($type_fr) || $type_fr === 'Ex' || strpos($type_fr, 'Ex  :') !== false || 
      strpos($domaine_fr, 'EX ') !== false || empty($titre_fr)) {
    echo "⚠ Ligne $line_number ignorée (exemple ou données vides)\n";
    $skipped++;
    continue;
  }
  
  try {
    echo "→ Ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe
    $existing_node = getExistingNode($titre_fr, 'procedure_formulaire');
    
    // Récupérer/créer les termes avec traductions arabes
    $type_procedure_term = getTaxonomyTermByName($type_fr, 'type_de_procedure', $type_ar);
    $domaine_pf_term = getTaxonomyTermByName($domaine_fr, 'domaines_activite_pf', $domaine_ar);
    
    // Préparer les données du nœud (UNIQUEMENT avec domaines_activite_pf)
    $node_data = [
      'type' => 'procedure_formulaire',
      'title' => $titre_fr,
      'field_type_de_procedure' => ['target_id' => $type_procedure_term->id()],
      'field_domaines_activite_pf' => ['target_id' => $domaine_pf_term->id()],
      'field_secteur' => ['target_id' => $transport_routier_sector->id()],
      'status' => 1,
    ];
    
    if ($existing_node) {
      // Mise à jour
      foreach ($node_data as $field => $value) {
        if ($field !== 'type') {
          $existing_node->set($field, $value);
        }
      }
      $existing_node->save();
      
      // Traduction arabe
      if (!empty($titre_ar)) {
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($titre_ar);
        } else {
          $ar_translation = $existing_node->addTranslation('ar');
          $ar_translation->setTitle($titre_ar);
          $ar_translation->set('field_type_de_procedure', ['target_id' => $type_procedure_term->id()]);
          $ar_translation->set('field_domaines_activite_pf', ['target_id' => $domaine_pf_term->id()]);
          $ar_translation->set('field_secteur', ['target_id' => $transport_routier_sector->id()]);
          $ar_translation->set('status', 1);
        }
        $ar_translation->save();
      }
      
      echo "  ✓ Mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Création
      $node = Node::create($node_data);
      $node->save();
      
      // Traduction arabe
      if (!empty($titre_ar)) {
        $ar_translation = $node->addTranslation('ar');
        $ar_translation->setTitle($titre_ar);
        $ar_translation->set('field_type_de_procedure', ['target_id' => $type_procedure_term->id()]);
        $ar_translation->set('field_domaines_activite_pf', ['target_id' => $domaine_pf_term->id()]);
        $ar_translation->set('field_secteur', ['target_id' => $transport_routier_sector->id()]);
        $ar_translation->set('status', 1);
        $ar_translation->save();
      }
      
      echo "  ✓ Créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "  ❌ Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

fclose($handle);

// === RÉSULTATS FINAUX ===
echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "✅ Nouveaux contenus créés: $imported\n";
echo "📝 Contenus mis à jour: $updated\n";
echo "⚠ Lignes ignorées: $skipped\n";
echo "❌ Erreurs: $errors\n";
echo "📊 Total traité: " . ($imported + $updated) . "\n";

echo "\n=== CONFIGURATION CRÉÉE ===\n";
echo "📁 Vocabulaires:\n";
echo "   - type_de_procedure (Type de procédure)\n";
echo "   - domaines_activite_pf (Domaines d'activité P&F)\n";
echo "🏷️ Champs:\n";
echo "   - field_type_de_procedure (requis)\n";
echo "   - field_domaines_activite_pf (UNIQUEMENT ce vocabulaire)\n";
echo "ℹ️  Note: Le contenu est lié UNIQUEMENT au vocabulaire domaines_activite_pf\n";

echo "\n🚀 Import terminé avec succès!\n";
echo "Le script est prêt pour le déploiement en production.\n";