<?php

use <PERSON><PERSON>al\node\Entity\Node;
use <PERSON><PERSON>al\taxonomy\Entity\Term;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'importation des procédures et formulaires Transport routier depuis un fichier CSV
 * Usage: drush php:script import_procedure_formulaire_transport_routier.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/html/mtl/Canva Procédures et Formulaires.xlsx - Transport routier.csv';

// Vérifier si le fichier existe
if (!file_exists($csv_file_path)) {
  echo "Erreur: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

echo "Début de l'importation des procédures et formulaires Transport routier...\n";

/**
 * Fonction pour récupérer ou créer un terme de taxonomie par son nom
 */
function getTaxonomyTermByName($name, $vocabulary) {
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  if ($terms) {
    return reset($terms);
  }
  
  // Créer le terme s'il n'existe pas
  echo "Création du terme '$name' dans le vocabulaire '$vocabulary'\n";
  $term = Term::create([
    'vid' => $vocabulary,
    'name' => $name,
  ]);
  $term->save();
  
  return $term;
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title, $type = 'procedure_formulaire') {
  $query = \Drupal::entityQuery('node')
    ->condition('type', $type)
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    return Node::load(reset($nids));
  }
  
  return null;
}

// Récupérer le terme Transport Routier dans le vocabulaire modes_de_transport
$transport_routier_sector = getTaxonomyTermByName('Transport Routier', 'modes_de_transport');

if (!$transport_routier_sector) {
  // Essayer avec "Transport routier" (première lettre minuscule)
  $transport_routier_sector = getTaxonomyTermByName('Transport routier', 'modes_de_transport');
}

if (!$transport_routier_sector) {
  echo "Erreur: Le terme 'Transport Routier' n'existe pas dans le vocabulaire 'modes_de_transport'\n";
  return;
}

// Lire le fichier CSV
$csv_content = file_get_contents($csv_file_path);
if (!$csv_content) {
  echo "Erreur: Impossible de lire le fichier CSV\n";
  return;
}

// Diviser en lignes
$lines = explode("\n", $csv_content);

// Ignorer la ligne d'en-tête et les exemples (ligne 1 = en-têtes, ligne 2+ = données)
$data_lines = array_slice($lines, 1);

$imported = 0;
$updated = 0;
$errors = 0;
$line_number = 1;

// Traiter chaque ligne de données
foreach ($data_lines as $line) {
  $line_number++;
  
  if (empty(trim($line))) {
    continue;
  }
  
  // Parser la ligne CSV
  $data = str_getcsv($line, ',', '"');
  
  // Vérifier que la ligne contient toutes les colonnes attendues (8 colonnes)
  if (count($data) < 8) {
    echo "Ligne $line_number ignorée (colonnes incomplètes: " . count($data) . ")\n";
    continue;
  }
  
  // Vérifier que la ligne contient des données valides
  if (empty(trim($data[4]))) { // Titre FR obligatoire
    echo "Ligne $line_number ignorée (titre FR vide): " . implode(' | ', array_slice($data, 0, 6)) . "\n";
    continue;
  }
  
  try {
    // Extraire les données selon la structure du CSV
    // Type FR,Type AR,Domaine d'activité Fr,Domaines d'activité ar,Titre en français,Titre en arabe,PDF FR,PDF AR
    $type_fr = trim($data[0]);
    $type_ar = trim($data[1]);
    $domaine_fr = trim($data[2]);
    $domaine_ar = trim($data[3]);
    $titre_fr = trim($data[4]);
    $titre_ar = trim($data[5]);
    $pdf_fr = trim($data[6]);
    $pdf_ar = trim($data[7]);
    
    echo "\nTraitement ligne $line_number: $titre_fr\n";
    
    // Vérifier si le contenu existe déjà
    $existing_node = getExistingNode($titre_fr, 'procedure_formulaire');
    
    // Mapper les types français vers les termes de taxonomie 'type' existants
    $type_mapping = [
      'Procédures' => 'Procédures',
      'Formulaires' => 'Formulaires',
    ];
    
    $mapped_type = isset($type_mapping[$type_fr]) ? $type_mapping[$type_fr] : $type_fr;
    
    // Récupérer le terme de type (ne pas créer s'il n'existe pas)
    $type_term = null;
    $terms = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties([
        'name' => $mapped_type,
        'vid' => 'type',
      ]);
    
    if ($terms) {
      $type_term = reset($terms);
    } else {
      echo "Attention: Type '$mapped_type' non trouvé dans le vocabulaire 'type', création...\n";
      $type_term = getTaxonomyTermByName($mapped_type, 'type');
    }
    
    // Récupérer ou créer le domaine d'activité depuis les données CSV
    $domaine_term = null;
    if (!empty($domaine_fr)) {
      $domaine_term = getTaxonomyTermByName($domaine_fr, 'domaines_d_activites');
    }
    
    // Gérer le titre long (limite de 255 caractères pour le champ title)
    $title_for_node = $titre_fr;
    $long_title = null;
    
    if (mb_strlen($titre_fr, 'UTF-8') > 255) {
      $title_for_node = mb_substr($titre_fr, 0, 250, 'UTF-8') . '...';
      $long_title = $titre_fr;
      echo "Titre tronqué pour: $title_for_node\n";
    }
    
    // Préparer les données du nœud
    $node_data = [
      'type' => 'procedure_formulaire',
      'title' => $title_for_node,
      'field_type_loi' => ['target_id' => $type_term->id()],
      'field_secteur' => ['target_id' => $transport_routier_sector->id()],
      'status' => 1,
      'uid' => 1,
    ];
    
    // Ajouter le domaine d'activité si disponible
    if ($domaine_term) {
      $node_data['field_domaine_d_activite'] = ['target_id' => $domaine_term->id()];
    }
    
    // Ajouter le titre long si nécessaire (si le champ existe)
    if ($long_title) {
      $node_data['field_titre_long'] = ['value' => $long_title];
    }
    
    if ($existing_node) {
      // Mettre à jour le nœud existant
      foreach ($node_data as $field => $value) {
        if ($field !== 'type') {
          $existing_node->set($field, $value);
        }
      }
      $existing_node->save();
      
      // Ajouter/mettre à jour la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long
        $title_ar_for_node = $titre_ar;
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
        }
        
        if ($existing_node->hasTranslation('ar')) {
          $ar_translation = $existing_node->getTranslation('ar');
          $ar_translation->setTitle($title_ar_for_node);
          if (mb_strlen($titre_ar, 'UTF-8') > 255 && $ar_translation->hasField('field_titre_long')) {
            $ar_translation->set('field_titre_long', ['value' => $titre_ar]);
          }
          $ar_translation->save();
        } else {
          $ar_data = ['title' => $title_ar_for_node];
          if (mb_strlen($titre_ar, 'UTF-8') > 255) {
            $ar_data['field_titre_long'] = ['value' => $titre_ar];
          }
          $existing_node->addTranslation('ar', $ar_data);
          $existing_node->save();
        }
      }
      
      echo "Nœud mis à jour: {$existing_node->id()}\n";
      $updated++;
    } else {
      // Créer un nouveau nœud
      $node = Node::create($node_data);
      $node->save();
      
      // Ajouter la traduction arabe
      if (!empty($titre_ar)) {
        // Gérer aussi le titre arabe long pour les nouveaux nœuds
        $title_ar_for_node = $titre_ar;
        $ar_data = ['title' => $title_ar_for_node];
        
        if (mb_strlen($titre_ar, 'UTF-8') > 255) {
          $title_ar_for_node = mb_substr($titre_ar, 0, 250, 'UTF-8') . '...';
          $ar_data['title'] = $title_ar_for_node;
          $ar_data['field_titre_long'] = ['value' => $titre_ar];
        }
        
        $node->addTranslation('ar', $ar_data);
        $node->save();
      }
      
      echo "Nouveau nœud créé: {$node->id()}\n";
      $imported++;
    }
    
  } catch (Exception $e) {
    echo "Erreur ligne $line_number: " . $e->getMessage() . "\n";
    $errors++;
  }
}

echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
echo "Nouveaux contenus créés: $imported\n";
echo "Contenus mis à jour: $updated\n";
echo "Erreurs: $errors\n";
echo "Total traité: " . ($imported + $updated) . "\n";
echo "Importation terminée!\n";