<?php

use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\media\Entity\Media;

/**
 * Script d'importation des actualités par batch
 * Usage: drush php:script import_actualites_batch.php [batch_size] [start_index]
 * Exemple: drush php:script import_actualites_batch.php 50 0
 */

// Paramètres
$batch_size = isset($argv[1]) ? (int)$argv[1] : 50;
$start_index = isset($argv[2]) ? (int)$argv[2] : 0;

$json_file_path = '/var/www/html/mtl/output-fr.json';

if (!file_exists($json_file_path)) {
  echo "Erreur: Le fichier JSON n'existe pas: $json_file_path\n";
  return;
}

echo "=== IMPORT PAR BATCH ===\n";
echo "Taille du batch: $batch_size\n";
echo "Index de départ: $start_index\n\n";

$term_cache = [];

function getTaxonomyTermByName($name, $vocabulary) {
  global $term_cache;
  
  $cache_key = $vocabulary . ':' . $name;
  if (isset($term_cache[$cache_key])) {
    return $term_cache[$cache_key];
  }
  
  $terms = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_term')
    ->loadByProperties([
      'name' => $name,
      'vid' => $vocabulary,
    ]);
  
  $term = $terms ? reset($terms) : null;
  $term_cache[$cache_key] = $term;
  
  return $term;
}

function processSecteurField($secteur_string) {
  if (empty($secteur_string)) {
    return ['secteurs' => [], 'types' => []];
  }
  
  $secteurs = array_map('trim', explode(';', $secteur_string));
  $secteur_terms = [];
  $type_terms = [];
  
  $secteur_mapping = [
    'Accueil' => 'Transport Routier',
    'Transport Aérien' => 'Aviation Civile',
    'Transport Routier' => 'Transport Routier',
    'Transport routier' => 'Transport Routier',
    'Marine Marchande' => 'Marine Marchande',
    'Ferroviaire' => 'Transport Routier',
    'Logistique' => 'Transport Routier',
  ];
  
  $type_mapping = [
    'Coopération' => 'Coopération internationale',
    'Gouvernance' => 'Gouvernance',
    'Parlement' => 'Parlement',
  ];
  
  foreach ($secteurs as $secteur_name) {
    if (isset($secteur_mapping[$secteur_name])) {
      $mapped_name = $secteur_mapping[$secteur_name];
      $term = getTaxonomyTermByName($mapped_name, 'modes_de_transport');
      if ($term) {
        $secteur_terms[] = ['target_id' => $term->id()];
        continue;
      }
    }
    
    if (isset($type_mapping[$secteur_name])) {
      $mapped_name = $type_mapping[$secteur_name];
      $term = getTaxonomyTermByName($mapped_name, 'type_d_actualites');
      if ($term) {
        $type_terms[] = ['target_id' => $term->id()];
        continue;
      }
    }
    
    $term = getTaxonomyTermByName($secteur_name, 'modes_de_transport');
    if ($term) {
      $secteur_terms[] = ['target_id' => $term->id()];
      continue;
    }
    
    $term = getTaxonomyTermByName($secteur_name, 'type_d_actualites');
    if ($term) {
      $type_terms[] = ['target_id' => $term->id()];
      continue;
    }
  }
  
  return ['secteurs' => $secteur_terms, 'types' => $type_terms];
}

function createMediaFromUrl($image_url, $title, $language = 'fr') {
  if (empty($image_url)) {
    return null;
  }
  
  try {
    $ch = curl_init();
    curl_setopt_array($ch, [
      CURLOPT_URL => $image_url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_TIMEOUT => 45, // Timeout augmenté pour serveur lent
      CURLOPT_SSL_VERIFYPEER => false,
      CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; DrupalImporter)',
      CURLOPT_FOLLOWLOCATION => true,
    ]);
    
    $file_data = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if (!$file_data || $http_code !== 200) {
      // Debug silencieux - ne pas polluer l'output
      return null;
    }
    
    $file_name = basename(parse_url($image_url, PHP_URL_PATH));
    if (empty($file_name) || strpos($file_name, '.') === false) {
      $file_name = 'actualite_image_' . time() . '_' . rand(100,999) . '.jpg';
    } else {
      $file_name = 'actualite_' . time() . '_' . $file_name;
    }
    
    $file_repository = \Drupal::service('file.repository');
    $file = $file_repository->writeData($file_data, 'public://' . $file_name);
    
    if (!$file) {
      return null;
    }
    
    $media = Media::create([
      'bundle' => 'image',
      'name' => $title,
      'field_media_image' => [
        'target_id' => $file->id(),
        'alt' => $title,
        'title' => $title
      ],
      'status' => 1,
      'langcode' => $language,
    ]);
    
    $media->save();
    return $media;
    
  } catch (\Exception $e) {
    return null;
  }
}

function findExistingNode($title, $date = null, $content_preview = '') {
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'actualite')
    ->condition('title', $title)
    ->accessCheck(FALSE);
    
  $nids = $query->execute();
  
  if (!empty($nids)) {
    $nodes = Node::loadMultiple($nids);
    
    if (count($nodes) === 1) {
      return reset($nodes);
    }
    
    if ($date) {
      foreach ($nodes as $node) {
        $node_date = $node->get('field_date')->value;
        if ($node_date === $date) {
          return $node;
        }
      }
    }
    
    if (!empty($content_preview)) {
      $content_start = substr(strip_tags($content_preview), 0, 100);
      foreach ($nodes as $node) {
        $node_content = $node->get('body')->value;
        $node_start = substr(strip_tags($node_content), 0, 100);
        if (similar_text($content_start, $node_start) > 80) {
          return $node;
        }
      }
    }
    
    usort($nodes, function($a, $b) { return $b->id() <=> $a->id(); });
    return reset($nodes);
  }
  
  return null;
}

function mergeTerms($existing_terms, $new_terms) {
  $existing_ids = [];
  foreach ($existing_terms as $term) {
    $existing_ids[] = $term['target_id'];
  }
  
  foreach ($new_terms as $new_term) {
    if (!in_array($new_term['target_id'], $existing_ids)) {
      $existing_terms[] = $new_term;
    }
  }
  
  return $existing_terms;
}

// Lire le fichier JSON
$json_content = file_get_contents($json_file_path);
if ($json_content === false) {
  echo "Erreur: Impossible de lire le fichier JSON\n";
  return;
}

$data = json_decode($json_content, true);
if ($data === null) {
  echo "Erreur: Le fichier JSON n'est pas valide\n";
  return;
}

$total_items = count($data);
$end_index = min($start_index + $batch_size, $total_items);
$batch_data = array_slice($data, $start_index, $batch_size);

echo "Total actualités: $total_items\n";
echo "Batch: " . ($start_index + 1) . " à $end_index\n";
echo "Nombre à traiter: " . count($batch_data) . "\n\n";

$created_count = 0;
$updated_count = 0;
$skipped_count = 0;
$images_success = 0;
$images_failed = 0;

$start_time = microtime(true);

foreach ($batch_data as $index => $item) {
  $current_index = $start_index + $index + 1;
  echo "[$current_index/$total_items] ";
  echo substr($item['titre'], 0, 50) . "...\n";
  
  if (empty($item['titre']) || empty($item['articleHtml'])) {
    echo "  ⚠️ Ignoré: données manquantes\n";
    $skipped_count++;
    continue;
  }
  
  // Traiter la date
  $date_field = null;
  if (!empty($item['datePublication'])) {
    try {
      $date = \DateTime::createFromFormat('d/m/Y H:i', $item['datePublication']);
      if (!$date) {
        $date = \DateTime::createFromFormat('d/m/Y', $item['datePublication']);
      }
      if ($date) {
        $date_field = $date->format('Y-m-d');
      }
    } catch (\Exception $e) {
      // Ignorer erreurs de date
    }
  }
  
  // Vérifier existence
  $content_preview = substr(strip_tags($item['articleHtml']), 0, 200);
  $existing_node = findExistingNode($item['titre'], $date_field, $content_preview);
  
  if ($existing_node) {
    echo "  📝 Mise à jour (ID: " . $existing_node->id() . ")\n";
    $node = $existing_node;
    $updated_count++;
  } else {
    echo "  ✨ Création\n";
    $node = Node::create(['type' => 'actualite']);
    $created_count++;
  }
  
  // Définir les champs
  $node->set('title', $item['titre']);
  $node->set('body', [
    'value' => $item['articleHtml'],
    'format' => 'full_html',
  ]);
  $node->set('status', ($item['visible'] === 'oui') ? 1 : 0);
  $node->set('langcode', 'fr');
  
  if ($date_field) {
    $node->set('field_date', $date_field);
  }
  
  // Traiter taxonomies
  if (!empty($item['secteur'])) {
    $taxonomy_data = processSecteurField($item['secteur']);
    
    if (!empty($taxonomy_data['secteurs'])) {
      if ($existing_node) {
        $existing_secteurs = $node->get('field_secteur')->getValue();
        $merged_secteurs = mergeTerms($existing_secteurs, $taxonomy_data['secteurs']);
        $node->set('field_secteur', $merged_secteurs);
      } else {
        $node->set('field_secteur', $taxonomy_data['secteurs']);
      }
    }
    
    if (!empty($taxonomy_data['types'])) {
      if ($existing_node) {
        $existing_types = $node->get('field_type_d_actualites')->getValue();
        $merged_types = mergeTerms($existing_types, $taxonomy_data['types']);
        $node->set('field_type_d_actualites', $merged_types);
      } else {
        $node->set('field_type_d_actualites', $taxonomy_data['types']);
      }
    }
  }
  
  // Traiter image (avec timeout réduit)
  if (!empty($item['image'])) {
    echo "  🖼️ Image...";
    $media = createMediaFromUrl($item['image'], $item['titre']);
    if ($media) {
      $node->set('field_image_media', ['target_id' => $media->id()]);
      echo " ✅\n";
      $images_success++;
    } else {
      echo " ❌\n";
      $images_failed++;
    }
  }
  
  // Sauvegarder
  try {
    $node->save();
    echo "  💾 Sauvegardé (ID: " . $node->id() . ")\n";
  } catch (\Exception $e) {
    echo "  ❌ Erreur: " . $e->getMessage() . "\n";
    $skipped_count++;
    if (!$existing_node) $created_count--;
    if ($existing_node) $updated_count--;
  }
  
  echo "\n";
  
  // Libérer mémoire tous les 10 éléments
  if (($index + 1) % 10 === 0) {
    gc_collect_cycles();
  }
}

$end_time = microtime(true);
$duration = round($end_time - $start_time, 2);

echo "=== RÉSUMÉ DU BATCH ===\n";
echo "Créés: $created_count\n";
echo "Mis à jour: $updated_count\n";
echo "Ignorés: $skipped_count\n";
echo "Images réussies: $images_success\n";
echo "Images échouées: $images_failed\n";
echo "Temps d'exécution: {$duration}s\n";
echo "Vitesse: " . round(count($batch_data) / $duration, 2) . " actualités/seconde\n\n";

$remaining = $total_items - $end_index;
if ($remaining > 0) {
  $next_start = $end_index;
  echo "Prochaine commande pour continuer:\n";
  echo "./vendor/bin/drush php:script import_actualites_batch.php $batch_size $next_start\n\n";
  
  $batches_remaining = ceil($remaining / $batch_size);
  $estimated_time = round($batches_remaining * $duration / 60, 1);
  echo "Reste: $remaining actualités ($batches_remaining batches)\n";
  echo "Temps estimé: ~{$estimated_time} minutes\n";
} else {
  echo "🎉 IMPORT TERMINÉ !\n";
}