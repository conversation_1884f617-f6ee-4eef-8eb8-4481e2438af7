<?php

/**
 * Script de debug pour voir la correspondance entre URLs et fichiers du serveur
 * Usage: drush php:script debug_images_matching.php
 */

$json_file_path = '/var/www/mtl/output-fr.json';
$images_folder = '/var/www/mtl/images-actualites/';

echo "=== DEBUG - CORRESPONDANCE IMAGES ===\n\n";

// Lire le fichier JSON
$json_content = file_get_contents($json_file_path);
$data = json_decode($json_content, true);

// Limiter aux 20 premières pour debug
$data = array_slice($data, 0, 20);

// Lister tous les fichiers du dossier
$server_files = [];
if (is_dir($images_folder)) {
    $files = scandir($images_folder);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && is_file($images_folder . $file)) {
            $server_files[] = $file;
        }
    }
}

echo "📁 Fichiers trouvés dans $images_folder: " . count($server_files) . "\n\n";

// Fonction de nettoyage identique au script principal
function cleanFilename($url) {
    $filename = basename(parse_url($url, PHP_URL_PATH));
    
    // Multiple URL decoding
    $filename = urldecode($filename);
    $filename = urldecode($filename);
    
    // Clean invalid characters
    $filename = preg_replace('/[<>:"\/\\|?*]/', '', $filename);
    $filename = preg_replace('/\s+/', ' ', $filename);
    $filename = trim($filename);
    
    return $filename;
}

// Fonction pour enlever les accents
function removeAccents($string) {
    $string = preg_replace_callback('/#U([0-9A-Fa-f]{4})/', function($matches) {
        return mb_convert_encoding(pack('H*', $matches[1]), 'UTF-8', 'UCS-2BE');
    }, $string);
    
    $accents = [
        'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a',
        'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e',
        'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
        'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
        'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
        'ç' => 'c', 'ñ' => 'n',
        'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A',
        'È' => 'E', 'É' => 'E', 'Ê' => 'E', 'Ë' => 'E',
        'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
        'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O',
        'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U',
        'Ç' => 'C', 'Ñ' => 'N'
    ];
    
    return strtr($string, $accents);
}

$failed_images = [];
$count = 0;

foreach ($data as $item) {
    if (!empty($item['image'])) {
        $count++;
        $url = $item['image'];
        $clean_filename = cleanFilename($url);
        
        echo "[$count] ===================\n";
        echo "🔗 URL: $url\n";
        echo "🧹 Nettoyé: $clean_filename\n";
        
        $found = false;
        $found_method = '';
        $found_file = '';
        
        // Test 1: Nom exact
        if (file_exists($images_folder . $clean_filename)) {
            $found = true;
            $found_method = 'Nom exact';
            $found_file = $clean_filename;
        }
        // Test 2: Sans accents
        else {
            $no_accents = removeAccents($clean_filename);
            echo "🔤 Sans accents: $no_accents\n";
            
            if (file_exists($images_folder . $no_accents)) {
                $found = true;
                $found_method = 'Sans accents';
                $found_file = $no_accents;
            }
            // Test 3: Format serveur Unicode
            else {
                $server_format = str_replace(['é', 'è', 'à', 'ç', 'ù', 'ô', 'î', 'ê', 'â'], ['#U00e9', '#U00e8', '#U00e0', '#U00e7', '#U00f9', '#U00f4', '#U00ee', '#U00ea', '#U00e2'], $clean_filename);
                echo "🖥️ Format serveur: $server_format\n";
                
                if (file_exists($images_folder . $server_format)) {
                    $found = true;
                    $found_method = 'Format serveur';
                    $found_file = $server_format;
                }
                // Test 4: Recherche insensible à la casse
                else {
                    $clean_lower = strtolower($clean_filename);
                    foreach ($server_files as $server_file) {
                        if (strtolower($server_file) === $clean_lower) {
                            $found = true;
                            $found_method = 'Insensible casse';
                            $found_file = $server_file;
                            break;
                        }
                    }
                    
                    // Test 5: Recherche fuzzy (similitude)
                    if (!$found) {
                        $best_match = '';
                        $best_similarity = 0;
                        
                        foreach ($server_files as $server_file) {
                            similar_text($clean_lower, strtolower($server_file), $similarity);
                            if ($similarity > $best_similarity && $similarity > 80) {
                                $best_similarity = $similarity;
                                $best_match = $server_file;
                            }
                        }
                        
                        if ($best_match) {
                            echo "🎯 Meilleure correspondance: $best_match (similarité: $best_similarity%)\n";
                        }
                    }
                }
            }
        }
        
        if ($found) {
            echo "✅ TROUVÉ: $found_file (méthode: $found_method)\n";
        } else {
            echo "❌ NON TROUVÉ\n";
            $failed_images[] = [
                'url' => $url,
                'expected' => $clean_filename,
                'titre' => substr($item['titre'], 0, 30) . '...'
            ];
            
            // Afficher les 5 fichiers les plus similaires
            echo "🔍 Fichiers similaires dans le dossier:\n";
            $similarities = [];
            foreach ($server_files as $server_file) {
                similar_text(strtolower($clean_filename), strtolower($server_file), $similarity);
                $similarities[] = ['file' => $server_file, 'similarity' => $similarity];
            }
            usort($similarities, function($a, $b) { return $b['similarity'] <=> $a['similarity']; });
            
            for ($i = 0; $i < 5 && $i < count($similarities); $i++) {
                if ($similarities[$i]['similarity'] > 30) {
                    echo "   • {$similarities[$i]['file']} ({$similarities[$i]['similarity']}% similaire)\n";
                }
            }
        }
        
        echo "\n";
    }
}

echo "=== RÉSUMÉ ===\n";
echo "Images testées: $count\n";
echo "Images non trouvées: " . count($failed_images) . "\n\n";

if (!empty($failed_images)) {
    echo "❌ LISTE DES IMAGES NON TROUVÉES:\n";
    foreach ($failed_images as $failed) {
        echo "• {$failed['expected']}\n";
        echo "  Titre: {$failed['titre']}\n";
        echo "  URL: {$failed['url']}\n\n";
    }
}

echo "🔧 DEBUG TERMINÉ !\n";