<?php

use PhpOffice\PhpSpreadsheet\IOFactory;

$xlsm_file_path = '/var/www/html/mtl/reglementation.xlsm';

try {
  $spreadsheet = IOFactory::load($xlsm_file_path);
  $worksheet = $spreadsheet->getSheetByName('Transport routier');
  
  $highestRow = $worksheet->getHighestRow();
  echo "=== ANALYSE COMPLÈTE DES DONNÉES ===\n";
  echo "Total lignes dans l'onglet: $highestRow\n\n";
  
  $stats = [
    'total_analysees' => 0,
    'avec_titre_fr' => 0,
    'avec_type_fr' => 0,
    'avec_domaine_fr' => 0,
    'avec_pdf' => 0,
    'lignes_vides' => 0,
    'lignes_partielles' => 0
  ];
  
  echo "--- ÉCHANTILLONS DE DONNÉES ---\n";
  $samples = [];
  
  for ($row = 20; $row <= $highestRow; $row++) {
    $stats['total_analysees']++;
    
    $type_fr = trim($worksheet->getCell('A' . $row)->getValue() ?? '');
    $domaine_fr = trim($worksheet->getCell('C' . $row)->getValue() ?? '');
    $numero = trim($worksheet->getCell('E' . $row)->getValue() ?? '');
    $titre_fr = trim($worksheet->getCell('F' . $row)->getValue() ?? '');
    $titre_ar = trim($worksheet->getCell('G' . $row)->getValue() ?? '');
    
    // Vérifier si ligne complètement vide
    if (empty($type_fr) && empty($domaine_fr) && empty($numero) && empty($titre_fr) && empty($titre_ar)) {
      $stats['lignes_vides']++;
      continue;
    }
    
    // Compter les différents types de données
    if (!empty($titre_fr)) $stats['avec_titre_fr']++;
    if (!empty($type_fr)) $stats['avec_type_fr']++;
    if (!empty($domaine_fr)) $stats['avec_domaine_fr']++;
    
    // Vérifier les PDF (sans extraction complète pour aller plus vite)
    $cell_i = $worksheet->getCell('I' . $row);
    $cell_j = $worksheet->getCell('J' . $row);
    if ($cell_i->hasHyperlink() || $cell_j->hasHyperlink()) {
      $stats['avec_pdf']++;
    }
    
    // Lignes partiellement remplies (pas vides mais pas de titre FR)
    if (empty($titre_fr) && (!empty($type_fr) || !empty($domaine_fr) || !empty($numero))) {
      $stats['lignes_partielles']++;
      
      // Garder un échantillon pour analyse
      if (count($samples) < 10) {
        $samples[] = [
          'row' => $row,
          'type_fr' => $type_fr,
          'domaine_fr' => $domaine_fr,
          'numero' => $numero,
          'titre_fr' => $titre_fr,
          'titre_ar' => substr($titre_ar, 0, 50) . '...'
        ];
      }
    }
    
    // Afficher des échantillons de lignes valides
    if (!empty($titre_fr) && $stats['avec_titre_fr'] <= 10) {
      echo "LIGNE $row (VALIDE): Type='$type_fr', Domaine='$domaine_fr', Titre='" . substr($titre_fr, 0, 60) . "...'\n";
    }
    
    // Arrêter l'analyse si on a dépassé une certaine limite pour éviter les timeouts
    if ($row > 500) {
      echo "\n⚠️  Analyse limitée aux 500 premières lignes pour éviter les timeouts\n";
      break;
    }
  }
  
  echo "\n=== STATISTIQUES COMPLÈTES ===\n";
  echo "Total lignes analysées: {$stats['total_analysees']}\n";
  echo "Lignes complètement vides: {$stats['lignes_vides']}\n";
  echo "Lignes avec titre français: {$stats['avec_titre_fr']}\n";
  echo "Lignes avec type français: {$stats['avec_type_fr']}\n";
  echo "Lignes avec domaine français: {$stats['avec_domaine_fr']}\n";
  echo "Lignes avec PDF (liens): {$stats['avec_pdf']}\n";
  echo "Lignes partiellement remplies: {$stats['lignes_partielles']}\n";
  
  if (!empty($samples)) {
    echo "\n--- ÉCHANTILLONS DE LIGNES PARTIELLES ---\n";
    echo "(Lignes qui ont des données mais pas de titre français)\n";
    foreach ($samples as $sample) {
      echo "Ligne {$sample['row']}: Type='{$sample['type_fr']}', Domaine='{$sample['domaine_fr']}', Numéro='{$sample['numero']}'\n";
      echo "  Titre FR: '{$sample['titre_fr']}'\n";
      echo "  Titre AR: '{$sample['titre_ar']}'\n\n";
    }
  }
  
  // Vérification spéciale : chercher des données dans d'autres plages
  echo "\n--- VÉRIFICATION PLAGES DE DONNÉES ---\n";
  $plages = [
    [200, 250],
    [300, 350], 
    [400, 450],
    [500, 550]
  ];
  
  foreach ($plages as [$start, $end]) {
    $count_plage = 0;
    for ($row = $start; $row <= min($end, $highestRow); $row++) {
      $titre_fr = trim($worksheet->getCell('F' . $row)->getValue() ?? '');
      if (!empty($titre_fr)) {
        $count_plage++;
      }
    }
    echo "Lignes $start-$end: $count_plage lignes avec titre français\n";
    
    if ($count_plage > 0) {
      echo "  ⚠️  DONNÉES TROUVÉES AU-DELÀ DE LA LIGNE 121 !\n";
    }
  }
  
} catch (Exception $e) {
  echo "Erreur: " . $e->getMessage() . "\n";
}