.swiperTabs {
    position: inherit;
    width: 90%;
    padding: 20px 0;
    .swiper-slide {
        font-size: rem(16);
        padding: 12px 5px;
        border-color: $primary;
        border-width: 1px;
        border-style: solid;
        border-radius: 24px;
        cursor: pointer;
        text-align: center;
        transition: backgroun 450ms ease, color 450ms ease;
        span {
            color: $primary;
        }
        &:hover, &.swiper-slide-thumb-active {
            background-color: $primary;
            span {
                color: $white;
            }
        }
    }
    .navigation-swiper {
        display: block;
        .swiper-button-prev, .swiper-button-next {
            background: $primary;
            width: 40px;
            height: 40px;
            border-radius: 100%;
            top: 96px;
            &:after {
                font-size: 16px;
                color: $white;
            }
        } 
        .swiper-button-prev {
            left: 30px;
            @media(max-width:481px) {
                left: 15px;
            }
        }
        .swiper-button-next {
            right: 30px;
            @media(max-width:481px) {
                right: 15px;
            }
        }
        @media(min-width:1025px) {
            display: none;
        }
    }
    @media(min-width:1025px) {
        position: relative;
        width: 100%;
    }
    @media(max-width:481px) {
        width: 75%;
    }
}