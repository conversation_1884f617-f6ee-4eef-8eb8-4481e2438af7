.actu-listing {
    width: 100%;
    .card {
        height: 100%;
        overflow: hidden;
        .card-img-top {
          object-fit: cover;
          transform: scale(1);
          height: 260px;
          transition: transform 700ms ease;
          @media(max-width:576px) {
            height: auto;
          }
        }
        .card-body {
            p {
                font-family: $rubik-bold;
                font-weight: bold;
                margin-bottom: rem(30);
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                }
            }
            span {
                position: absolute;
                bottom: 20px;
            }
        }
        &:hover {
          box-shadow: 0 10px 12px rgba(67, 101, 151, 0.27);
          .card-img-top {
            transform: scale(1.02);
          }
        }
    }
    &.region-maps {
        .card {
            height: 100%;
            .card-body {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                padding: 20px 40px;
                p {
                    font-family: $quicksand-regular;
                    line-height: 1.6;
                    margin-bottom: rem(30);
                    html[dir="rtl"] & {
                      font-family: "Cairo", sans-serif;
                      font-weight: 400;
                    }
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
            @media(max-width:600px) {
                .card-body {
                    padding: 20px;
                    h2 {
                        margin-bottom: 15px;
                    }
                    p {
                        margin-bottom: rem(15);
                    }
                }
            }
        }
    }

    &.searche {
      form#views-exposed-form-search-page-1 {
        input[type="text"] {
          height: 59px;
          margin-top: 0;
        }
        @media(max-width: 600px) {
          gap: 20px;
        }
      }
      .views-row {
        &:last-of-type {
          margin-bottom: 100px;
        }
      }
        .row {
          &.card {
            flex-direction: row;
            padding: 20px 10px;
            padding-bottom: 20px;
            position: relative;
            margin: 60px 0;
            &:first-of-type {
                &:before {
                 display: block;
                }
            }
           
            &:before, &:after {
                content: "";
                background: $primary;
                position: absolute;
                width: 100%;
                height: .8px;
                left: 0;
            }
            &:before {
                top: -30px;
                display: none;
            }
            &:after {
                bottom: -30px;
            }
            .picture {
              width: 100%;
              overflow: hidden;
              border-radius: 14px;
            }
            img {
              height: 100%;
              border-radius: 14px;
              transform: scale(1);
              transition: transform 450ms ease;
              &:hover {
                transform: scale(1.02);
              }
            }
            span {
              color: $black;
            }
            time {
              font-family: $quicksand-regular;
              color: $black;
               html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 400;
                }
        
            }
            h2 {
              font-size: 22px;
              line-height: 1.3;
              margin-bottom: 0;
              padding: 10px 0;
              a {
                color: $primary;
                transition: all 450ms ease;
                &:hover {
                  color: $secondary;
                }
              }
            }
            p {
              color: $black;
            }
          }
        }
        .late-news {
          .wrapper-news {
            position: relative;
            overflow: hidden;
            &:hover {
              .content {
                height: 100%;
                transform: translateY(-50%);
                top: 50%;
                background: rgba($primary, 0.4);
                flex-direction: column;
                justify-content: center;
                display: flex;
              }
            }
          }
          .picture {
            height: 100%;
            img {
              border-radius: 0 0 14px 14px;
            }
          }
          .content {
            position: absolute;
            transform: translateY(-100%);
            width: 100%;
            padding: 10px 15px;
            background: rgba($primary, 0.8);
            transition: all 300ms ease;
            border-radius: 0 0 14px 14px;
            span.date {
              position: relative;
              padding-left: 1.25rem;
              &:after {
                content: "\e901";
                font-family: 'icomoon';
                position: absolute;
                font-size: 1rem;
                color: $white;
                left: 0;
                top: 0;
              }
            }
            span, h3 {
              font-family: $quicksand-regular;
              color: $white;
               html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 400;
                }
            }
            h3 {
              font-size: 18px;
              margin-bottom: 0;
              line-height: 1.2;
              padding-top: 5px;
            }
          }
        
        }
    }
    form {
      .form--inline {
        .form-item {
          margin-right: 0;
        }
      }
    }
}