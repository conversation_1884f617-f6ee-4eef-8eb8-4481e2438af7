.bloc-decouvrir {
    width: 100%;
    @media(max-width: 600px) {
        h2 {
            margin-bottom: 15px !important;
        }
    }
    .items {
        width: 100%;
        .card {
            padding: 0;
            overflow: hidden;
            img {
                width: 100%;
                height: 200px;
                margin: 0 !important;
                border-radius: 14px 14px 0 0 !important;
                padding: 0 !important;
                transform: scale(1);
                transition: transform 700ms ease;
            }
            .card-body {
                padding: 36px 25px !important;
                a.link-decouvrir {
                    font-family: $rubik-bold;
                    font-weight: bold;
                    font-size: rem(16);
                    color: $black;
                    padding: 0;
                    text-decoration: none !important;
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                    }
                }
            }
            &:hover {
                box-shadow: 0 10px 12px rgba(67, 101, 151, 0.27);
                .card-img-top {
                  transform: scale(1.02);
                }
            }
            @media(max-width: 480px) {
                padding: 0 !important;
                .card-body {
                    padding: 20px !important;
                }
            }
        }
    }

}