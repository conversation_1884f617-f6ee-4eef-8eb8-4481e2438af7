"use strict";

(function ($) {
  var app;
  app = {
    init: function init() {
      this._readyGlobal();
      this._resizeGlobal();
      this._loadGlobal();
      this._menu();
      this._search();
      this._addscroll();
      this._swiper();
      this._accordion();
      this._tabs();
      this._video();
      this._select();
      this._validationsJs();
      //  AOS.init();
    },

    //_readyGlobal
    _readyGlobal: function _readyGlobal() {
      $('#accessPanel').removeAttr( 'style' );
      // Go to top
      document.addEventListener('DOMContentLoaded', function () {
        const goToTop = document.querySelector('.goTop');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                goToTop.classList.add('is-visible');
            } else {
                goToTop.classList.remove('is-visible');
            }
        });
    
        goToTop.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        let $header = $('.header');
        if ($header.length > 0) {
          let $paddingTop = $('body.toolbar-fixed').css('padding-top');
          $header.css('top', $paddingTop);
        }
        if (document.querySelector('.tabs-button')) {
          document.body.classList.add('has-tabs-button');
        }
        if (document.querySelector('.e-services-wysiw') || document.querySelector('.accord-wysiw') ) {
          document.body.classList.add('page-content');
        }
        if (document.querySelector('.not-found')) {
          document.body.classList.add('page-404');
        } 
        if (document.querySelector('form.user-login-form')) {
          document.body.classList.add('page-log');
        }
        if (document.querySelector('#coop-multi')) {
          document.body.classList.add('coop-multi');
        }
        if (document.querySelector('.boc-light')) {
          document.body.classList.add('bodyorg');
        }
        if (document.querySelector('#views-exposed-form-procedure-formulaire-page-1')) {
          document.body.classList.add('proc-form');
        }
        const wysiwyg = document.querySelector('.bannerHp__page-interne + div > .m-organigramme > .container > #c-wysiwyg');
        if (wysiwyg && wysiwyg.querySelector('.organigrame')) {
          document.body.classList.add('body-org');
        }
        const scrollWrapper = document.querySelector('#tab3 > .paragraph--type--organigrame');
        if (scrollWrapper && scrollWrapper.querySelector('.scroll-wrapper')) {
          document.body.classList.add('marg-org');
        }
        //append social-media-sharing
        const ul = document.querySelector(".social-media-sharing ul");
        const a2aSpan = document.querySelector(".a2a_kit");

        if (ul && a2aSpan) {
          const lastLi = ul.querySelector("li:last-child");
          const newLi = document.createElement("li");
          newLi.appendChild(a2aSpan);
          ul.appendChild(newLi);
        }
        
      });
      
      
      // const sticky = new Sticky('.tabs-button');

      //AjaxComplet
      $(document).ajaxComplete(function () {
          function corrigerTexte() {
          $('body').find('*').each(function () {
            $(this).contents().filter(function () {
            return this.nodeType === Node.TEXT_NODE;
            }).each(function () {
            this.textContent = this.textContent
            .replace(/&#039;/g, "'")
            .replace(/&quot;/g, '"');
            });
          });
        }
        corrigerTexte();
        //e-serices
        if ($(".swiper-services").length > 0) {
          var swiper = new Swiper(".swiper-services", {
              direction: 'horizontal',
              slidesPerView: 3,
              spaceBetween: 0,
              centeredSlides: true,
              loop: true,
              initialSlide: 1,
              navigation: {
                  nextEl: ".swiper-button-next",
                  prevEl: ".swiper-button-prev",
              },
              autoplay: {
                delay: 2000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              },
              speed: 2500,
              breakpoints: {
                  0: { slidesPerView: 1, speed: 2500,},
                  482: { slidesPerView: 2, initialSlide: 0, centeredSlides: false},
                  1024: { slidesPerView: 3 }
              }
          });
        }
        setTimeout(function() {
          var controller = new ScrollMagic.Controller();
          var revealElements = document.getElementsByClassName("digit");
          for (var i=0; i<revealElements.length; i++) {
              new ScrollMagic.Scene({
              triggerElement: revealElements[i],
              offset: 50,
              triggerHook: 0.9,
            })
            .setClassToggle(revealElements[i], "visible")
            .addTo(controller);
          }
          new ScrollMagic.Scene({
            triggerElement: "#trigger1",
            triggerHook: 0.9,
            duration: "0",
            offset: 50
          })
          .setClassToggle("#reveal1", "visible")
          .addTo(controller);
        },10)
        
      })
      document.addEventListener("DOMContentLoaded", function () {
        Fancybox.bind("[data-fancybox]", {
          Thumbs: {
            autoStart: false,
          },
          buttons: [
            "zoom",
            "slideShow",
            "thumbs",
            "close"
          ]
        });
      });
    },
    //resizeGlobal
    _resizeGlobal: function _resizeGlobal() {
      $(document).on('resize', function () {

      });
    },
    //loadGlobal
    _loadGlobal: function _loadGlobal() {
      // $(window).on('load', function () {
      //   const path = window.location.pathname;
      //   if (!path.match(/^\/(fr|en)(\/|$)/)) return;

      //   function wrapFirstLetters() {
      //     $('h1, h2, h3').each(function () {
      //       const $el = $(this);
        
      //       // Ignorer si déjà traité
      //       if ($el.find('#first-letter').length) return;
        
      //       // Ignorer si tout le contenu est un seul <a>
      //       const children = $el.children();
      //       if (children.length === 1 && children.is('a')) return;
        
      //       const textOnly = $el.text().trim();
      //       if (textOnly.length === 0) return;
        
      //       const firstChar = textOnly.charAt(0).toUpperCase();
      //       const restText = textOnly.slice(1);
        
      //       const newText = `<span id="first-letter">${firstChar}</span>${restText}`;
      //       $el.html(newText);
      //     });
      //   }

      //   wrapFirstLetters();

      //   const observer = new MutationObserver(wrapFirstLetters);
      //   observer.observe(document.body, { childList: true, subtree: true });

      // });
    },

    //Menu
    _menu: function _menu() {
      //form search menu
      $(document).ready(function () {
       
        //Decouvrir plus priority
        $('.share-buttons').insertBefore('.bloc-decouvrir');

        // $('form#views-exposed-form-projects-page-1 select').on('change', function () {
        //   $('form#views-exposed-form-projects-page-1 input[type="submit"]').prop('disabled', false);
        // });
        // $('form#views-exposed-form-projects-page-1 input[type="submit"]').on('click', function () {
        //  $('form#views-exposed-form-projects-page-1').submit();
        // });
        // $('form#views-exposed-form-projects-page-1 input[type="radio"]').on('change', function () {
        //   $('form#views-exposed-form-projects-page-1').submit();
        // });
        $('.search-icon').click(function () {
            if ($(window).width() > 992) { 
                $(this).toggleClass('search-icon__active');
                $(this).closest('.wrapper-menu-form').toggleClass('disable-menu');
            }
            const $overlay = $('.header__bottom--overlay');
            const $over = $('.overlay');
            const $body = $('body');
            const $input = $overlay.find('input[type="text"]');
            $($over).toggleClass('open');
            $($body).addClass('no-scroll');
            if (!$overlay.is(':visible')) {
                $overlay.slideDown('fast', function () {
                    $input.val('').focus();
                });
            } else {
                $($body).removeClass('no-scroll');
                $overlay.slideUp('fast');
            }
        });
        $('body').click(function (e) {
          if (!$(e.target).closest('.search-icon').length && !$(e.target).closest('.header__bottom--overlay').length) {
            const $over = $('.overlay');
            $($over).removeClass('open');
            $('body').removeClass('no-scroll')
            $('.search-icon').removeClass('search-icon__active');
            $('.header__bottom--overlay').css('display', 'none');
            $('.wrapper-menu-form ').removeClass('disable-menu');
          }
        });
        let toggle_label = $('.nav-toggle-label');
        $(toggle_label).on('click', function () {
            if (window.matchMedia("(max-width: 992px)").matches) {
                $('body,html').toggleClass('toggle');
            }
        });

        //reload
          let mediaQuery = window.matchMedia("(max-width: 992px)");
          let lastState = mediaQuery.matches ? 'mobile' : 'desktop';

          mediaQuery.addEventListener('change', function (event) {
            let newState = event.matches ? 'mobile' : 'desktop';
            if (newState !== lastState) {
              lastState = newState;
              location.reload();
            }
          });
            const headerBottomNav = document.querySelector('.header__bottom nav[role="navigation"]');
            const overlay = document.querySelector('.header__bottom--overlay');
            const langSwitcher = document.querySelector('.header__top--lang.language-switcher-language-url');
            const headerTopMenu = document.querySelector('.header__top--menu.animation-link');
            const headerTopRsociaux = document.querySelector('.header__top--rsociaux');
            const headerTopMobile = document.createElement('div');
            headerTopMobile.classList.add('header-bottom-mobile');

            if (window.innerWidth <= 992) {
              headerTopMobile.appendChild(headerTopMenu);
              headerTopMobile.appendChild(headerTopRsociaux);
              headerBottomNav.appendChild(headerTopMobile);

              const headerBotMobile = document.createElement('div');
              headerBotMobile.classList.add('header-top-mobile');
              headerBotMobile.appendChild(langSwitcher);
              headerBotMobile.appendChild(overlay);
              headerBottomNav.prepend(headerBotMobile);
            }
            
            //organigrame
            $(".quotes").closest(".container").addClass("wysi-quotes");

            // var maxHeight = 0;
  
            // $('.mtl-tv__items--item h3,.reglementation .card h2.title-reg, .partenaire .col-md-4 h3').each(function() {
            //   var thisHeight = $(this).height();
            //   if (thisHeight > maxHeight) {
            //     maxHeight = thisHeight;
            //   }
            // });

            // $('.mtl-tv__items--item h3, .reglementation .card h2.title-reg, .partenaire .col-md-4 h3').height(maxHeight);
            function uniformiserHauteursTitres() {
            var maxHeight = 0;
            var $titres = $('.mtl-tv__items--item h3, .partenaire h3');

            // Réinitialiser les hauteurs pour ne pas cumuler
            $titres.css('height', 'auto');

            // Trouver la hauteur max
            $titres.each(function () {
            var thisHeight = $(this).height();
            if (thisHeight > maxHeight) {
            maxHeight = thisHeight;
            }
            });

            // Appliquer la hauteur max à tous
            $titres.height(maxHeight);
            }

            // Appel initial au chargement
            $(document).ready(function () {
              uniformiserHauteursTitres();
            });

            // Appel au resize (responsive)
            $(window).on('resize', function () {
              uniformiserHauteursTitres();
            });

            
            if ($('.organigrame__membres--two img').length > 0) {
              $('.tabs-content .organigrame').addClass('organisation');
            }

              $('.bloc-odd').each(function () {
                const $left = $(this).find('.presentation-left');
                const $right = $(this).find('.presentation-right');
                if ($right.index() < $left.index()) {
                  $(this).addClass('is-reversed');
                }
              });


              //tronquage 
              function truncateTitle() {
                var maxWords = 14;
                var titleElement = $('.breadCrumb ul li:last-child');
                var originalText = titleElement.data('original-text');
              
                if (!originalText) {
                  originalText = titleElement.text().trim();
                  titleElement.data('original-text', originalText);
                }
              
                var words = originalText.split(/\s+/);
              
                if (words.length > maxWords) {
                  var truncatedText = words.slice(0, maxWords).join(' ') + '...';
                  titleElement.text(truncatedText);
                } else {
                  titleElement.text(originalText);
                }
              }
              
              truncateTitle();
              $(window).on('resize', truncateTitle);

              //Chiffre Cle Dyna
              $('.keyfigure-counter').each(function() {
                var $this = $(this);
                var target = parseInt($this.text().replace(/\D/g, ''), 10); 
                $this.data('target', target).text('0'); 
              });
              // scroll event
              var scrolling = false;
              $(window).scroll(function() {
                if (!scrolling) {
                  scrolling = true;
                  setTimeout(function() {
                    $('.keyfigure-counter').each(function() {
                      var $this = $(this);
                      if ($this.data('animated')) return;
                      
                      var elementTop = $this.offset().top;
                      var viewportBottom = $(window).scrollTop() + $(window).height();
                      
                      if (elementTop < viewportBottom) {
                        $this.data('animated', true);
                        var target = $this.data('target');
                        
                        $this.prop('count', 0).animate({ count: target }, {
                          duration: 2500,
                          easing: 'swing',
                          step: function(now) {
                            // $this.text(Math.floor(now));
                            $this.text(Math.floor(now).toLocaleString('fr-FR').replace(/\s/g, ' '));
                          },
                          complete: function() {
                            // $this.text(target); 
                            $this.text(target.toLocaleString('fr-FR').replace(/\s/g, ' ')); 
                          }
                        });
                      }
                    });
                    scrolling = false;
                  }, 100);
                }
              }).trigger('scroll'); 

                  //acc wysi
                  // $('.wysi-accordion img').each(function () {
                  //   var $img = $(this);
                  //   var $textElements = $(); // Contiendra les <p>, <ul>, <ol> associés

                  //   // Parcourt les éléments suivants jusqu'à la prochaine image
                  //   $img.nextAll().each(function () {
                  //     if ($(this).is('img')) return false; // Stop si une autre image est trouvée
                  //     if ($(this).is('p, ul, ol')) {
                  //       $textElements = $textElements.add(this);
                  //     }
                  //   });

                  //   // Crée les conteneurs
                  //   var $wrapper = $('<div class="image-block"></div>');
                  //   var $pictureDiv = $('<div class="picture"></div>');
                  //   var $content = $('<div class="content"></div>');

                  //     // Insère le wrapper avant l'image
                  //     $img.before($wrapper);
                  //     $wrapper.append($pictureDiv);     // Ajoute la div.picture
                  //     $pictureDiv.append($img);         // Ajoute l'image dans .picture
                  //     $wrapper.append($content);        // Ajoute la div.content
                  //     $content.append($textElements);   // Déplace les éléments textuels dans .content
                  // });

                  // // Étape 2 : grouper tous les .image-block dans un conteneur .image-block-wrapper
                  // var $allBlocks = $('.wysi-accordion .image-block');
                  // if ($allBlocks.length > 0) {
                  //   var $wrapper = $('<div class="image-block-wrapper"></div>');
                  //   $allBlocks.first().before($wrapper);
                  //   $allBlocks.appendTo($wrapper);
                  // }

                  // $('.wysi-accordion img').each(function () {
                  //   var $img = $(this);
                  //   var $textElements = $();
                  //   $img.nextAll().each(function () {
                  //     if ($(this).is('img')) return false;
                  //     if ($(this).is('p, ul, ol')) {
                  //       $textElements = $textElements.add(this);
                  //     }
                  //   });
                  //   var $wrapper = $('<div class="image-block col-12 col-md-6 col-lg-4"></div>');
                  //   var $pictureDiv = $('<div class="picture"></div>');
                  //   var $content = $('<div class="content container"></div>');
                  //   $content.hide();
                  //   $img.before($wrapper);
                  //   $wrapper.append($pictureDiv);
                  //   $pictureDiv.append($img);
                  //   $wrapper.append($content);
                  //   $content.append($textElements);
                  //     $pictureDiv.css('cursor', 'pointer').on('click', function() {
                  //       $('.wysi-accordion .content').not($content).slideUp();
                  //       $content.slideToggle();
                  //     });
                  // });

                  // var $allBlocks = $('.wysi-accordion .image-block');
                  // if ($allBlocks.length > 0) {
                  //   var $wrapper = $('<div class="image-block-wrapper row g-3"></div>');
                  //   $allBlocks.first().before($wrapper);
                  //   $allBlocks.appendTo($wrapper);
                  // }

                  $('.wysi-accordion img').each(function () {
                    var $img = $(this);
                    var $textElements = $();
                    $img.nextAll().each(function () {
                      if ($(this).is('img')) return false;
                      if ($(this).is('p, ul, ol, h2, h3, h4, h5')) {
                        $textElements = $textElements.add(this);
                      }
                    });

                    var $wrapper = $('<div class="image-block col-12 col-md-6 col-lg-4"></div>');
                    var $pictureDiv = $('<div class="picture"></div>');
                    var $content = $('<div class="content container"></div>');
                    var $containerWrapper = $('<div class="container-wrapper"></div>');
                    $containerWrapper.append($textElements);
                    $content.append($containerWrapper);

                    $content.hide();

                    $img.before($wrapper);
                    $wrapper.append($pictureDiv);
                    $pictureDiv.append($img);
                    $wrapper.append($content);

                    $pictureDiv.css('cursor', 'pointer').on('click', function () {
                      var isCurrentlyOpen = $(this).hasClass('open');
                      $('.wysi-accordion .content').not($content).slideUp(300);
                      $('.wysi-accordion .picture').removeClass('open');
                      if (!isCurrentlyOpen) {
                        $(this).addClass('open');
                      }
                      $content.slideToggle(300);
                    });
                  });
                  var $allBlocks = $('.wysi-accordion .image-block');
                  if ($allBlocks.length > 0) {
                    var $wrapper = $('<div class="image-block-wrapper row g-3"></div>');
                    $allBlocks.first().before($wrapper);
                    $allBlocks.appendTo($wrapper);
                    $allBlocks.first().find('.content').show();
                    $allBlocks.first().find('.picture').addClass('open');
                  }
          //en wrap
          $("#webform-submission-newsletter-add-form").wrap('<div class="container"></div>');
      });
      //Page Coopération
      // $(document).ready(function () {
      //   const links = $('ul > li:first-child > ul > li:nth-child(3) .card-body .card-link');
      //   links.eq(0).attr('href', '#coop-bil');
      //   links.eq(1).attr('href', '#coop-multi');
      // });
      // $(document).ready(function() {
      //   const links = $('ul > li:first-child > ul > li:nth-child(3) .card-body .card-link');

      //   links.on('click', function(e) {
      //     e.preventDefault();
      //     links.removeClass('active');
      //     $(this).addClass('active');
      //     var target = $(this).attr('href');
      //     if (target && target.startsWith('#') && $(target).length) {
      //       $('html, body').animate({
      //         scrollTop: $(target).offset().top - 160
      //       }, 800);
      //     }
      //   });
      // });


      //Menu mobile
        function initMenuScripts() {
          if ($(window).width() <= 992) {
            $('.header__bottom nav ul > li:has(ul) > a').off('click').on('click', function(e) {
              e.preventDefault();
              var $this = $(this);
              var $submenu = $this.next('ul');
        
              $('.header__bottom .card .card-body > div').slideUp();
              $('.header__bottom .card .card-body > div').filter(function() {
                return $(this).text().trim() === "";
              }).closest('.card-body').addClass('card-body--toggle');
              
              if ($submenu.is(':visible')) {
                $submenu.slideUp();
                $this.removeClass('open');
                $('.header__bottom .card span.card-title').removeClass('open');
              } else {
                $('.header__bottom nav ul > li:has(ul) ul:visible').slideUp().prev('a').removeClass('open');
                $submenu.slideDown();
                $this.addClass('open');
              }
            });
            
            $('.header__bottom .card > div.card-body > span.card-title').off('click').on('click', function(e) {
              e.preventDefault();
              var $this = $(this);
              var $content = $this.next('div:has(*)');
        
              $('.header__bottom .card span.card-title.open').not($this).removeClass('open').next('div:visible').slideUp();
        
              $content.slideToggle(function() {
                $this.toggleClass('open', $content.is(':visible'));
              });
            });
          } else {
            $('.header__bottom nav ul > li:has(ul) > a').off('click');
            $('.header__bottom .card .span.card-title').off('click');
          }
        }
        // Exécuter au chargement et lors du redimensionnement
        $(document).ready(initMenuScripts);
        $(window).on('resize', function() {
          initMenuScripts();
        });
        

        //link Active
        document.querySelectorAll('.header__bottom nav ul > li:has(ul)').forEach(item => {
          item.addEventListener('mouseover', function() {
              this.classList.add('active');
          });
          item.addEventListener('mouseout', function() {
              this.classList.remove('active');
          });
        });
        $(function() {
          // Récupérer la valeur du paramètre dans l'URL
          var params = new URLSearchParams(window.location.search);
          var secteurId = params.get('field_secteur_target_id');
          
          if (secteurId) {
            $('body').addClass('suppFilter');
          }
          var paramsId = new URLSearchParams(window.location.search);

          // Chercher tous les paramètres commençant par 'field_secteur_target_id['
          if (paramsId.has('field_secteur_target_id[0]')) {
            $('body').addClass('suppSecteur');
          }
        });
      
    },
    _search: function _search() {
      setTimeout(function () {
        let currentUrl = window.location.href;
        let $langueSwitcher = $('.language-switcher-language-url ul.langue-switcher');
      
        // Mettre à jour l'ordre et la classe active
        $langueSwitcher.find('li').each(function () {
          let $link = $(this).find('a');
          if (currentUrl.includes($link.attr('href'))) {
            $langueSwitcher.prepend($(this)).find('li').removeClass('is-active').eq(0).addClass('is-active');
          }
        });
      
        // **Desktop - Hover**
        if (window.innerWidth >= 992) {
          $langueSwitcher.on('mouseenter', function () {
            $(this).addClass('list__is-visible').find('li:not(:first-child)').show();
          });
      
          $langueSwitcher.on('mouseleave', function () {
            $(this).removeClass('list__is-visible').find('li:not(:first-child)').hide();
          });
        }
      
        // **Mobile - Click**
        if (window.innerWidth < 992) {
          $langueSwitcher.on('click', function (e) {
            e.stopPropagation(); // Empêche la propagation du clic
            if ($(this).hasClass('list__is-visible')) {
              // Fermer le menu avec un effet rapide
              $(this).removeClass('list__is-visible').find('li:not(:first-child)').fadeOut(100); // 100ms pour une fermeture rapide
            } else {
              // Ouvrir le menu avec un effet rapide
              $(this).addClass('list__is-visible').find('li:not(:first-child)').fadeIn(100); // 100ms pour une ouverture rapide
            }
          });
        
          // Fermer si clic en dehors
          $(document).on('click', function () {
            if ($langueSwitcher.hasClass('list__is-visible')) {
              $langueSwitcher.removeClass('list__is-visible').find('li:not(:first-child)').fadeOut(100);
            }
          });
        }
        
      }, 500);
      
    },

    _addscroll: function _addscroll() {
      $(document).on('scroll', function () {
        var scrollTop = $(window).scrollTop();
        var scrollLimit = window.matchMedia("(max-width: 992px)").matches ? 20 : 62;
    
        $("body").toggleClass('window_scroll', scrollTop > scrollLimit);
      });
    },

    //swiper
    _swiper: function _swiper() {
      if ($(".sliderHeader__items").length > 0) {
        var swiper = new Swiper(".sliderHeader__items", {
          direction: 'horizontal',
          slidesPerView: 4,
          spaceBetween: 20,
          // initialSlide: 0,
          // loop: true,
          autoplay: {
            delay: 2000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          },
          speed: 2500,
          breakpoints: {
              0: { slidesPerView: 2, speed: 2000 },
              601: { slidesPerView: 3 },
              1025: { slidesPerView: 4 }
          },
          
        });
      }

      //Mtl tv
      if ($(".swiper-video").length > 0) {
        var swiper = new Swiper(".swiper-video", {
            direction: 'horizontal',
            slidesPerView: 3,
            spaceBetween: 20,
            initialSlide: 0,
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
              0: { slidesPerView: 1, speed: 2000},
              601: { slidesPerView: 2 },
              769: { slidesPerView: 3 }
          },
        });
      }

      //e-serices
      if ($(".swiper-services").length > 0) {
        var swiper = new Swiper(".swiper-services", {
            direction: 'horizontal',
            slidesPerView: 3,
            spaceBetween: 0,
            centeredSlides: true,
            loop: true,
            initialSlide: 1,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 1300,
            breakpoints: {
                0: { slidesPerView: 1.4, speed: 2500, spaceBetween: 20, centeredSlides: false,},
                601: { slidesPerView: 2, initialSlide: 0, centeredSlides: false},
                1024: { slidesPerView: 3 }
            },
            // on: {
            //   slideChangeTransitionEnd: function () {
            //       $(".swiper-slide").removeClass("animate-slide");
            //       $(".swiper-slide-active").addClass("animate-slide");
            //   }
            // }
        });
      }
     

      //Chiffres cles
      if ($(".chiffre-cles").length > 0) {
        var swiper = new Swiper(".chiffre-cles", {
            direction: 'horizontal',
            slidesPerView: 4,
            spaceBetween: 20,
            loop: false,
            // initialSlide: 0,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
                0: { slidesPerView: 2, spaceBetween: 10, speed: 2500, },
                768: { slidesPerView: 3 },
                1025: { slidesPerView: 4 }
            },
            on: {
              init: function () {
                  setEqualHeight();
              },
              slideChange: function () {
                  setEqualHeight();
              }
            }
        });
        function setEqualHeight() {
          let maxHeight = 0;
          let $slides = $(".chiffre-cles .swiper-slide .picture");
  
          // Réinitialiser les hauteurs
          $slides.css("height", "");
  
          // Trouver la hauteur maximale
          $slides.each(function () {
              let slideHeight = $(this).outerHeight();
              if (slideHeight > maxHeight) {
                  maxHeight = slideHeight;
              }
          });
  
          // Appliquer la hauteur maximale à tous les slides
          $slides.css("height", maxHeight + "px");
        }
  
        $(window).on("resize", setEqualHeight);
      }

      //Organisme
      $(document).ready(function () { 
        if ($(".organisme").length > 0) {
          var swiper = new Swiper(".organisme", {
            direction: 'horizontal',
            slidesPerView: 4,
            spaceBetween: 20,
            loop: false,
            // initialSlide: 0,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            // autoplay: {
            //   delay: 2000,
            //   disableOnInteraction: false,
            //   pauseOnMouseEnter: true,
            // },
            // speed: 2500,
            breakpoints: {
                0: { slidesPerView: 1.3},
                482: { slidesPerView: 2},
                991: { slidesPerView: 3 },
                1200: { slidesPerView: 4 }
            },
            on: {
                init: function() {
                    setTimeout(function() { sameHeight(); }, 200);
                },
                resize: function() {
                    setTimeout(function() { sameHeight(); }, 200);
                },
                slideChangeTransitionEnd: function() {
                    sameHeight();
                }
              }
          });
          function sameHeight() {
            const pictures = document.querySelectorAll('.organisme p');
            let maxHeight = 0;
            pictures.forEach(function(pic) {
                pic.style.height = 'auto';
            });
            pictures.forEach(function(pic) {
                const height = pic.offsetHeight;
                if (height > maxHeight) maxHeight = height;
            });
            pictures.forEach(function(pic) {
                pic.style.height = maxHeight + 'px';
            });
          }
        }

        //caractère speciaux
        function corrigerTexte() {
          $('body').find('*').each(function () {
            $(this).contents().filter(function () {
            return this.nodeType === Node.TEXT_NODE;
            }).each(function () {
            this.textContent = this.textContent
            .replace(/&#039;/g, "'")
            .replace(/&quot;/g, '"');
            });
          });
        }
        corrigerTexte();
      })

      //Organisme Listing
      if ($(".organisme-listing").length > 0) {
        var isWrapperOrganisme = function isWrapperOrganisme() {
          return window.innerWidth <= 600;
        };
        if (isWrapperOrganisme()) {
          var swiper = new Swiper(".organisme-listing", {
            direction: 'horizontal',
            slidesPerView: 1.4,
            spaceBetween: 20,
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
              0: { slidesPerView: 1.4, speed: 2000},
              602: { slidesPerView: 8 }
            },
            
          });
        }
      }

      //Liens Utiles
      $(document).ready(function () { 
        if ($(".liens-utile").length > 0) {
          var swiper = new Swiper(".liens-utile", {
              direction: 'horizontal',
              slidesPerView: 4,
              spaceBetween: 20,
              loop: false,
              // initialSlide: 0,
              pagination: {
                el: '.swiper-pagination',
                clickable: true,
              },
              // autoplay: {
              //   delay: 2000,
              //   disableOnInteraction: false,
              //   pauseOnMouseEnter: true,
              // },
              // speed: 2500,
              breakpoints: {
                  0: { slidesPerView: 1.5},
                  482: { slidesPerView: 2 },
                  768: { slidesPerView: 3 },
                  1025: { slidesPerView: 4 }
              },
              // on: {
              //   init: function() {
              //       setTimeout(function() { sameHeight(); }, 200);
              //   },
              //   resize: function() {
              //       setTimeout(function() { sameHeight(); }, 200);
              //   },
              //   slideChangeTransitionEnd: function() {
              //       sameHeight();
              //   }
              // }
          });
          // function sameHeight() {
          //   const pictures = document.querySelectorAll('.liens-utile .picture');
          //   let maxHeight = 0;
          //   pictures.forEach(function(pic) {
          //       pic.style.height = 'auto';
          //   });
          //   pictures.forEach(function(pic) {
          //       const height = pic.offsetHeight;
          //       if (height > maxHeight) maxHeight = height;
          //   });
          //   pictures.forEach(function(pic) {
          //       pic.style.height = maxHeight + 'px';
          //   });
          // }
        }
      })
      
      //Projet
      if ($(".projet__items").length > 0) {
          const isRTL = $('html').attr('dir') === 'rtl';
          if (isRTL) { $('.projet__items').attr('dir', 'rtl'); }
          var swiper = new Swiper(".projet__items", {
            direction: 'horizontal',
            slidesPerView: 3,
            spaceBetween: 20,
            loop: false,
            initialSlide: 0,
            watchOverflow: true,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            // autoplay: {
            //   delay: 2000,
            //   disableOnInteraction: false,
            //   pauseOnMouseEnter: true,
            // },
            // speed: 2500,
            breakpoints: {
                0: { slidesPerView: 1.1, loop:true, rewind:true},
                482: { slidesPerView: 2, loop: true, rewind: true},
                769: { slidesPerView: 3, loop: false, rewind: false }
            },
        });
      }

      //Partenaire
      if ($(".bloc-partenaire .partenaire").length > 0) {
        var swiper = new Swiper(".bloc-partenaire .partenaire", {
            direction: 'horizontal',
            slidesPerView: 4,
            spaceBetween: 20,
            // loop: true,
            initialSlide: 0,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
                0: { slidesPerView: 1.5, speed: 2000},
                482: { slidesPerView: 2 },
                768: { slidesPerView: 3 },
                1025: { slidesPerView: 4 }
            },
        });
      }

      //Secteur
      if ($(".wrapper-trans .swiper-wrapper-trans").length > 0) {
        var swiper = new Swiper(".wrapper-trans .swiper-wrapper-trans", {
          direction: 'horizontal',
          slidesPerView: 3,
          loop: false,
          grid: {
            rows: 2,
          },
          spaceBetween: 15,
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
          },
          // autoplay: {
          //   delay: 2000,
          //   disableOnInteraction: false,
          //   pauseOnMouseEnter: true,
          // },
          // speed: 2500,
          breakpoints: {
            // --- Mobile ---
            0: {
              slidesPerView: 1.2,
              loop: true,
              rewind: false,
              // speed: 2500,
            },
            // --- Tablette ---
            601: { 
              slidesPerView: 2,
              grid: { rows: 3 },
              loop: false,
              rewind: true,
            },
            // --- Desktop ---
            1092: {
              slidesPerView: 3,
              grid: { rows: 2 },
              loop: false,
            }
          },
        });
      }


      //Agenda
      let agendaSwiper = null;

      function initAgendaSwiper() {
        const $blocAgenda = $('.itemsWrapper .bloc-agenda');

        if ($(window).width() <= 640) {
          if (!agendaSwiper) {
            // Vérifie si swiper-wrapper existe déjà
            if (!$blocAgenda.find('.swiper-wrapper').length) {
              const $cards = $blocAgenda.find('.card');
              $cards.wrap('<div class="swiper-slide"></div>');
              $cards.parent().wrapAll('<div class="swiper-wrapper"></div>');
              $blocAgenda.find('.swiper-wrapper').wrap('<div class="swiper swiper-bloc-agenda"></div>');
            }

            agendaSwiper = new Swiper('.swiper-bloc-agenda', {
              autoplay: {
                delay: 2000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              },
              speed: 2500,
              slidesPerView: 1.2,
              spaceBetween: 10,
              loop: true,
            });
          }
        } else {
          if (agendaSwiper) {
            agendaSwiper.destroy(true, true);
            agendaSwiper = null;

            // Nettoyer le DOM si nécessaire (optionnel)
            const $bloc = $('.itemsWrapper .bloc-agenda');
            const $cards = $bloc.find('.swiper-slide .card').unwrap(); // enlever .swiper-slide
            $bloc.find('.swiper-wrapper').replaceWith($cards); // enlever swiper-wrapper
            $bloc.find('.swiper-bloc-agenda').children().unwrap(); // enlever swiper
          }
        }
      }

      // Initialisation
      $(document).ready(function () {
        initAgendaSwiper();
      });

      $(window).on('resize', function () {
        initAgendaSwiper();
      });

      
      


      //blocTabs
        // $(document).ready(function () {
        //   var swiperOnglet  = new Swiper(".thumbsSlider-secteur .swiper-onglet", {
        //     direction: 'vertical',
        //     spaceBetween: 20,
        //     slidesPerView: 3,
        //     freeMode: false,
        //     loop: false,
        //     watchSlidesProgress: true,
        //     watchSlidesVisibility: true,
        //     resistanceRatio: 0,
        //     breakpoints: {
        //       0: { slidesPerView: 1, direction: 'horizontal', spaceBetween: 0},
        //       769: { slidesPerView: 3, direction: 'vertical', spaceBetween: 20}
        //     },
        //   });
        //   var swiperContents = new Swiper(".thumbsSlider-secteur .contentsOnglet", {
        //     spaceBetween: 0,
        //     effect: 'fade',
        //     fadeEffect: {
        //       crossFade: true
        //     },
        //     thumbs: {
        //       swiper: swiperOnglet,
        //     },
        //     navigation: {
        //       nextEl: ".swiper-button-next",
        //       prevEl: ".swiper-button-prev",
        //     },
        //     breakpoints: {
        //       0: { allowTouchMove: true},
        //       768: { allowTouchMove: false},
        //     },
        //     on: {
        //       slideChange: function () {
        //       swiperOnglet.slideTo(this.activeIndex);
        //     },
        //     },
        //   });
        //   $('.swiper-onglet .swiper-slide').on('mouseenter', function () {
        //     const index = $(this).index();
        //     swiperContents.slideTo(index);
        //   });
        // });
        
        // window.addEventListener('resize', () => {
        //   var swiperOnglet  = new Swiper(".thumbsSlider-secteur .swiper-onglet", {
        //     direction: 'vertical',
        //     spaceBetween: 20,
        //     slidesPerView: 3,
        //     freeMode: false,
        //     loop: false,
        //     breakpoints: {
        //       0: { slidesPerView: 1, direction: 'horizontal', spaceBetween: 30},
        //       769: { slidesPerView: 3}
        //     },
        //   });
        //   var swiperContents = new Swiper(".thumbsSlider-secteur .contentsOnglet", {
        //     spaceBetween: 0,
        //     effect: 'fade',
        //     fadeEffect: {
        //       crossFade: true
        //     },
        //     thumbs: {
        //       swiper: swiperOnglet,
        //     },
        //     navigation: {
        //       nextEl: ".swiper-button-next",
        //       prevEl: ".swiper-button-prev",
        //     },
        //     breakpoints: {
        //       0: { allowTouchMove: true},
        //       768: { allowTouchMove: false },
        //     },
        //   });
        // });

      $(document).ready(function () {
        // Détection RTL auto
        const isRTL = $('html').attr('dir') === 'rtl';

        var swiperOnglet = new Swiper(".thumbsSlider-secteur .swiper-onglet", {
          direction: $(window).width() < 769 ? 'horizontal' : 'vertical',
          spaceBetween: $(window).width() < 769 ? 0 : 20,
          slidesPerView: $(window).width() < 769 ? 1 : 3,
          freeMode: false,
          loop: false,
          watchSlidesProgress: true,
          watchSlidesVisibility: true,
          resistanceRatio: 0,
          rtl: isRTL, // active uniquement si langue arabe
        });

        var swiperContents = new Swiper(".thumbsSlider-secteur .contentsOnglet", {
          spaceBetween: 0,
          effect: 'fade',
          fadeEffect: { crossFade: true },
          watchSlidesProgress: true,
          rtl: isRTL,
          thumbs: { swiper: swiperOnglet },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          },
          breakpoints: {
            0: { allowTouchMove: true },
            768: { allowTouchMove: false },
          },
          on: {
            slideChange: function () {
              swiperOnglet.slideTo(this.activeIndex);
            },
          },
        });

        // Hover sur les thumbs
        $('.swiper-onglet .swiper-slide').on('mouseenter', function () {
          const index = $(this).index();
          swiperContents.slideTo(index);
        });

        // Resize → juste update, pas recréer
        $(window).on('resize', function () {
          swiperOnglet.changeDirection($(window).width() < 769 ? 'horizontal' : 'vertical');
          swiperOnglet.params.spaceBetween = $(window).width() < 769 ? 0 : 20;
          swiperOnglet.params.slidesPerView = $(window).width() < 769 ? 1 : 3;
          swiperOnglet.update();
          swiperContents.update();
        });
      });
       $(document).ready(function () {
          var swiperTabs = new Swiper(".swiperTabs", {
            direction: 'horizontal',
            slidesPerView: 4,
            spaceBetween: 10,
            centeredSlides: false,
            navigation: {
              nextEl: ".navigation-swiper .swiper-button-next",
              prevEl: ".navigation-swiper .swiper-button-prev",
            },
            breakpoints: {
              0:   { slidesPerView: 1 },
              481: { slidesPerView: 2 },
              992: { slidesPerView: 3 },
              1025: { slidesPerView: 4 }
            },
            on: {
              slideChange: function () {
                swiperContentTabs.slideTo(this.activeIndex);
              }
            }
          });
          var swiperContentTabs = new Swiper(".contentsTabs", {
            spaceBetween: 10,
            effect: 'fade',
            fadeEffect: { crossFade: true },
            autoHeight: true,
            thumbs: {
              swiper: swiperTabs
            },
            allowTouchMove: false
          });
      });
      
    },
    _accordion: function _accordion() {
      //Accordion Footer 
      $('.accord-wysiw').parent().each(function() {
        var $parentContainer = $(this);
        var $accordeonCards = $parentContainer.find('.card.accord-wysiw');
        $accordeonCards.find('ul').addClass('menu');
        $accordeonCards.each(function(index) {
          var $card = $(this);
          var $h3 = $card.find('h3');
          var $div = $card.find('div');
          if (index === 0) {
            $h3.addClass('active');
            $div.show();
          } else {
            $h3.removeClass('active');
            $div.hide();
          }
        });
      });
      // $('.faq .views-field-title').parent().each(function() {
      //   var $parentContainer = $(this);
      //   var $accordeonCards = $parentContainer.find('.card.accord-wysiw');
      //   $accordeonCards.find('ul').addClass('menu');
      //   $accordeonCards.each(function(index) {
      //     var $card = $(this);
      //     var $h3 = $card.find('h3');
      //     var $div = $card.find('div');
      //     if (index === 0) {
      //       $h3.addClass('active');
      //       $div.show();
      //     } else {
      //       $h3.removeClass('active');
      //       $div.hide();
      //     }
      //   });
      // });
      $('.footer__middle > ul > li > a, .accord-wysiw > h3').on('click', function (e) {
        e.preventDefault();
        var $this = $(this);
        var $ul = $this.next('ul');
        var $div = $this.next('div');
      
        // ----- FOOTER -----
        if ($ul.length) {
          if ($ul.is(':visible')) {
            $this.removeClass('active');
            $ul.slideUp();
          } else {
            $('.footer__middle > ul > li > ul').slideUp().prev('a').removeClass('active');
            $this.addClass('active');
            $ul.slideDown();
          }
        }
      
        // ----- ACCORDÉON WYSIWYG -----
        if (('.accord-wysiw').length) {
          if ($div.is(':visible')) {
            $this.removeClass('active');
            $div.slideUp();
          } else {
            $('.accord-wysiw div').slideUp().prev('h3').removeClass('active');
            $this.addClass('active');
            $div.slideDown(); 
          }
        }

      });
      $(document).ready(function () {
        var $firstTitle = $('.faq .views-field-title').first();
        var $firstBody = $firstTitle.next('.views-field-body');

        $firstTitle.addClass('active');
        $firstBody.slideDown();
      });
      $('.faq .views-field-title').on('click', function (e) {
        e.preventDefault();
        var $this = $(this);
        var $body = $this.next('.views-field-body');

        if ($body.is(':visible')) {
          $this.removeClass('active');
          $body.slideUp();
        } else {
            $('.faq .views-field-title').removeClass('active');
            $('.faq .views-field-body').slideUp();
            $this.addClass('active');
            $body.slideDown();
        }
      });
      

      $(document).ready(function () {
        $(".e-services__content--tabs .tab").click(function (e) {
          e.preventDefault();
            var index = $(this).index(); // Récupère l'index de l'onglet cliqué
    
            // Désactiver les onglets et les contenus
            $(".e-services__content--tabs .tab").removeClass("active");
            $(".tab-content").removeClass("active").fadeOut(300); // Disparaît avec animation
    
            // Activer l'onglet et le contenu correspondant avec animation
            $(this).addClass("active");
            $(".tab-content").eq(index).fadeIn(300).addClass("active");
        });
      });

      //tabs page presentation secteur
      $(document).ready(function() {
        const $tabButtons = $('.card.tabs-button');
        const $tabLinks = $tabButtons.find('a');
        const $sections = $($tabLinks.map(function() {
          return $(this).attr('href');
        }));

          // 1. Gestion du clic avec votre méthode de scroll
          $tabLinks.on('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
              window.scrollTo({
                top: targetElement.offsetTop - 400, // Votre offset spécifique
                behavior: 'smooth'
              });
            }
          });
          // 2. Gestion du hover
          $tabLinks.on('mouseenter', function() {
            $tabLinks.removeClass('active');
            $(this).addClass('active');
            $(this).parent().addClass('open');
          });

          $tabButtons.on('mouseleave', function() {
            $(this).removeClass('open');
          });
          // Nouveau: Activation au scroll
          $(window).on('scroll', function() {
            const scrollPosition = $(this).scrollTop() + 1; // +1 pour détection précise
            
            $sections.each(function(index) {
              const sectionTop = $(this).offset().top - 400;
              
              // Active quand le haut de la fenêtre atteint le haut de la section
              if (scrollPosition >= sectionTop && 
                  scrollPosition < sectionTop + $(this).outerHeight()) {
                $tabLinks.removeClass('active').eq(index).addClass('active');
                return false; // Sortie anticipée
              }
            });
          }).trigger('scroll');
      });
        
    
    },

    _tabs: function _tabs() {
        // tabbed content
        $('.tabs li:first-child').addClass('active');
        $(".tabs li:not(:first-child)").removeClass("active")
        $(".tab_content").hide();
        $(".tab_content:first").show();

        $("ul.tabs li").click(function() {
          $(".tab_content").hide();
          var activeTab = $(this).attr("rel");  
          $("#"+activeTab).fadeIn();
        
          $("ul.tabs li").removeClass("active");
          $(this).addClass("active");
  
        $(".tab_drawer_heading").removeClass("d_active");
        $(".tab_drawer_heading[rel^='"+activeTab+"']").addClass("d_active");
        
        });
    },
    _video: function() {
      var $body = $("body");
      function toggle_video_modal() {
        $body.on("click", ".js-trigger-video-modal", function (e) {
            e.preventDefault();
            var id = $(this).attr("data-youtube-id");
            var autoplay = "?autoplay=1";
            var related_no = "&rel=0";
            var mute_no = "&mute=0";
            var src = "//www.youtube.com/embed/" + id + autoplay + related_no + mute_no;
            $("#youtube").attr("src", src);
            $body.addClass("show-video-modal noscroll");
        });
    
        function close_video_modal() {
            $body.removeClass("show-video-modal noscroll");
            $("#youtube").attr("src", "");
        }
        $body.on("click", ".close-video-modal", function () {
            close_video_modal();
        }
        );
        $body.on("keyup", function (e) {
            if (e.keyCode == 27) {
                close_video_modal();
            }
        });
        $body.on("click", ".video-modal", function (e) {
          if (!$(e.target).closest(".video-container").length) {
              close_video_modal();
          }
      });
      }
      toggle_video_modal();
    },
    _select: function (){
      /**** Event select ****/
      if (typeof $.trim !== 'function') {
        $.trim = function(value) {
          return typeof value === 'string' ? value.trim() : '';
        };
      }
      if (typeof jQuery.isFunction !== 'function') {
        jQuery.isFunction = function(obj) {
          return typeof obj === 'function';
        };
      }
      if (typeof jQuery.fn.selectric === 'function') {
        jQuery('.form-select').selectric({ nativeOnMobile: false });
      }
      $(document).ajaxComplete(function() {
        if (typeof jQuery.isFunction !== 'function') {
          jQuery.isFunction = function(obj) {
            return typeof obj === 'function';
          };
        }
        if (typeof jQuery.fn.selectric === 'function') {
          jQuery('.form-select').selectric({ nativeOnMobile: false });
        }
        $('.faq .views-field-title').on('click', function (e) {
          e.preventDefault();
          var $this = $(this);
          var $faq = $this.next('.views-field-body');
        
          // ----- FAQ -----
          if ($faq.length) {
            // Fermer tous les autres sauf celui cliqué
            $('.faq .views-field-body').not($faq).slideUp().prev('.views-field-title').removeClass('active');
        
            // Toggle celui cliqué
            if ($faq.is(':visible')) {
              $this.removeClass('active');
              $faq.slideUp();
            } else {
              $this.addClass('active');
              $faq.slideDown();
            }
          }
        });
          // $('form#views-exposed-form-projects-page-1 select').on('change', function () {
          //   $('form#views-exposed-form-projects-page-1 input[type="submit"]').prop('disabled', false);
          // });
          // $('form#views-exposed-form-projects-page-1 input[type="submit"]').on('click', function () {
          //  $('form#views-exposed-form-projects-page-1').submit();
          // });
          // $('form#views-exposed-form-projects-page-1 input[type="radio"]').on('change', function () {
          //   $('form#views-exposed-form-projects-page-1').submit();
          // });

          $('.mtl-tv__items--item h3').each(function() {
            var thisHeight = $(this).height();
            if (thisHeight > maxHeight) {
              maxHeight = thisHeight;
            }
          });

          $('.mtl-tv__items--item h3').height(maxHeight);
         
      });
    },
    
    _validationsJs: function () {
      $.validator.addMethod("phonenu", function (value, element) {
        if ( /^\d{3}-?\d{3}-?\d{4}$/g.test(value)) {
            return true;
        } else {
            return false;
        };

      }, Drupal.t("Numéro de téléphone invalide."));

      $.validator.addMethod("laxEmail", function(value, element) {
        // allow any non-whitespace characters as the host part
        if ( /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i.test(value)) {
            return true;
        } else {
            return false;
        };
      }, Drupal.t("S'il vous plaît, mettez une adresse email valide."));

      $.validator.addMethod("time24", function(value, element) {
        if (!/^\d{2}:\d{2}:\d{2}$/.test(value)) return false;
        var parts = value.split(':');
        if (parts[0] > 23 || parts[1] > 59 || parts[2] > 59) return false;
        return true;
      }, "Invalid time format.");

      // Validation Filter Header
      if (($("#views-exposed-form-recherches-searchapi-page-1").length > 0)) {
        $("#views-exposed-form-recherches-searchapi-page-1").validate({
          rules: {
            search_api_fulltext: {
              required: true,
              minlength: 3
            }
          },
          messages: {
            search_api_fulltext: {
              required: Drupal.t("Ce champ est obligatoire."),
              minlength: Drupal.t("Saisir au moins 3 caractères.")
            }
          }
        });
      }

    }
  };
  app.init();
})(jQuery);

(function ($) {
  var $links = $('ul > li:first-child > ul > li:nth-child(3) .card-body .card-link').slice(0, 2);
  var targets = ['coop-bil', 'coop-multi'];

  $links.each(function (index) {
    var $link = $(this);
    var targetId = targets[index];

    if (targetId) {
      $link.attr('data-target', targetId);

      $link.off('click.smoothScroll').on('click.smoothScroll', function (e) {
        var currentPath = window.location.pathname;
        var linkPath = new URL(this.href).pathname;

        // Gestion classe active
        $links.removeClass('active');
        $link.addClass('active');

        if (currentPath !== linkPath) {
          sessionStorage.setItem('smoothScrollTarget', targetId);
          // navigation normale, pas preventDefault
        } else {
          e.preventDefault();
          var $target = $('#' + targetId);
          if ($target.length) {
            $('html, body').animate({
              scrollTop: $target.offset().top - 160
            }, 600);
          }
        }
      });
    }
  });

  // Au chargement, gérer la classe active selon la cible scrollée
  $(window).on('load', function () {
    var targetId = sessionStorage.getItem('smoothScrollTarget');

    if (targetId) {
      $links.removeClass('active');
      $links.filter('[data-target="' + targetId + '"]').addClass('active');

      var $target = $('#' + targetId);
      if ($target.length) {
        setTimeout(function () {
          $('html, body').animate({
            scrollTop: $target.offset().top - 160
          }, 600);
          sessionStorage.removeItem('smoothScrollTarget');
        }, 200);
      } else {
        sessionStorage.removeItem('smoothScrollTarget');
      }
    } else {
      var hash = window.location.hash;
      if (hash) {
        var $target = $(hash);
        if ($target.length) {
          $links.removeClass('active');
          $links.filter('[data-target="' + hash.substring(1) + '"]').addClass('active');

          setTimeout(function () {
            $('html, body').animate({
              scrollTop: $target.offset().top - 160
            }, 600);
          }, 200);
        }
      }
    }
  });
})(jQuery);

(function ($) {
  var DEFAULT_TARGET = 'd-activite'; // fallback global
  var OFFSET         = 160;          // hauteur header par défaut
  var KEY            = '__smoothScrollTarget';
  var BIND_FLAG      = '__smoothScrollBound';

  // Evite double-binding si le script est injecté deux fois
  if (window[BIND_FLAG]) return;
  window[BIND_FLAG] = true;

  // --- utils
  function samePath(a, b){ return a.replace(/\/$/, '') === b.replace(/\/$/, ''); }
  function currentOffset(){
    var $h = $('header:visible, .site-header:visible, .navbar:visible').first();
    return $h.length ? $h.outerHeight() : OFFSET;
  }

  function getTargetById(id){
    return $('#' + id + ', [data-target="' + id + '"]').first();
  }

  function scrollNowFor(id){
    var $el = getTargetById(id);
    if (!$el.length || !$el.is(':visible')) return false;
    $('html, body').stop(true).animate({
      scrollTop: Math.max(0, $el.offset().top - currentOffset())
    }, 600);
    return true;
  }

  function tryScrollWithRetriesFor(id, maxMs){
    var start = Date.now(), limit = maxMs || 5000;
    (function tick(){
      if (scrollNowFor(id)) return;
      if (Date.now() - start > limit) return;
      setTimeout(tick, 100);
    })();
  }

  // sessionStorage safe
  function setStore(v){ try{ sessionStorage.setItem(KEY, v); }catch(_){ } }
  function takeStore(){ try{ var v=sessionStorage.getItem(KEY); if(v!==null) sessionStorage.removeItem(KEY); return v; }catch(_){ return null; } }

  // Index réel du slide (gère les duplicats de Swiper)
  function realIndex($slide){
    var a = $slide.attr('data-swiper-slide-index');
    if (a != null) {
      var n = parseInt(a, 10);
      if (!isNaN(n)) return n;
    }
    return $slide.index('.contentsOnglet .swiper-slide:not(.swiper-slide-duplicate)');
  }

  // ---- MAPPING par position SI pas de data-target sur le lien
  //     (1er, 2e, 3e .btn-success dans le 2e slide)
  var TARGETS_BY_POS = ['d-activite', 'd-projets', 'd-contact']; // <-- adapte ces IDs

  function linkTarget($a, $slide){
    // Priorité au data-target du lien (recommandé)
    var t = $a.attr('data-target') || $a.data('target') || $a.data('scrollTarget');
    if (t) return String(t);

    // Sinon: mapping par position du lien dans le slide
    var i = $slide.find('a.btn-success').index($a); // 0,1,2...
    return TARGETS_BY_POS[i] || DEFAULT_TARGET;
  }

  // ---- Binding principal (gère TOUS les a.btn-success via délégation)
  function bind(){
    $(document)
      .off('click.smoothScroll', '.contentsOnglet .swiper-slide a.btn-success')
      .on('click.smoothScroll',  '.contentsOnglet .swiper-slide a.btn-success', function(e){
        var $a     = $(this);
        var $slide = $a.closest('.swiper-slide');

        // On ne gère que le 2e "vrai" slide
        if (realIndex($slide) !== 1) return;

        var dest = linkTarget($a, $slide);
        var u    = new URL(this.href, location.origin);

        // Autre page -> mémoriser la cible et laisser naviguer
        if (!samePath(location.pathname, u.pathname)) {
          setStore(dest);
          return;
        }

        // Même page -> empêcher la nav et scroller vers la bonne ancre
        e.preventDefault();
        tryScrollWithRetriesFor(dest, 5000);
      });
  }

  // ---- À l'arrivée (inclut retour via BFCache)
  function onArrival(){
    var want = takeStore();
    if (want) {
      tryScrollWithRetriesFor(want, 5000);
    }
  }

  bind();
  $(onArrival);
  $(window).on('load pageshow', function(){
    bind();      // re-sécurise le binding après réinits éventuelles
    onArrival(); // rejoue le scroll s’il y avait une intention stockée
  });
})(jQuery);

//Ancre Org
(function ($) {

  // --- utils
  function normalizeHash(h){
    if (!h) return '';
    return '#' + String(h).replace(/^#+/, '').trim();
  }

  // à partir d'un hash, produire une liste de sélecteurs candidats
  // ex: "#..._de_la_dsi-organisation_chart" -> ["#..._de_la_dsi-organisation_chart", "#..._dsi-organisation_chart"]
  function buildCandidates(hash){
    var norm = normalizeHash(hash);
    if (!norm) return [];
    var arr = [norm];

    var alt = norm.replace('org-chart-organigramme_de_la_', 'org-chart-organigramme_');
    if (alt !== norm) arr.push(alt);

    // dédoublonnage simple
    return arr.filter(function(v, i, a){ return a.indexOf(v) === i; });
  }

  // ouvrir une carte .card.accord-wysiw dans SON groupe uniquement
  function openAccCard($card){
    if (!$card || !$card.length) return;

    var $group = $card.parent().find('> .card.accord-wysiw');

    // fermer les sœurs
    $group.each(function(){
      var $c = $(this);
      var $h3 = $c.children('h3').first();
      var $panel = $h3.next('div');
      $h3.removeClass('active');
      $panel.stop(true, true).slideUp(200);
    });

    // ouvrir la cible
    var $h3 = $card.children('h3').first();
    var $panel = $h3.next('div');
    $h3.addClass('active');
    $panel.stop(true, true).slideDown(200);
  }

  // retirer display:none en remontant jusqu'au panneau de la carte
  function unhideUpTo($el, $stopAt){
    var $node = $el;
    while ($node.length && (!$stopAt.length || $node[0] !== $stopAt[0])) {
      var el = $node[0];
      if (el && el.style && el.style.removeProperty) {
        // si display:none inline ou style="...display: none..."
        if (el.style.display === 'none' || /(^|;)\s*display\s*:\s*none/i.test(el.getAttribute('style') || '')) {
          el.style.removeProperty('display');
        }
      }
      $node = $node.parent();
    }
  }

  // tentative unique : chercher la carte .accord-wysiw qui contient l'ID (ou son alias)
  function tryOpenOnce(){
    var candidates = buildCandidates(window.location.hash);
    if (!candidates.length) return false;

    var found = false;

    // 1) parcours direct des cartes, on regarde DANS leur panneau
    $('.card.accord-wysiw').each(function(){
      if (found) return;
      var $card  = $(this);
      var $panel = $card.children('div').first();

      for (var i = 0; i < candidates.length; i++) {
        var sel = candidates[i];
        var $t  = $panel.find(sel).first();
        if ($t.length) {
          // rendre visibles les wrappers masqués jusqu'au panneau
          unhideUpTo($t, $panel);
          openAccCard($card);
          found = true;
          break;
        }
      }
    });

    // 2) fallback global (au cas où l'ID ne serait pas strictement sous le panneau)
    if (!found) {
      for (var j = 0; j < candidates.length && !found; j++) {
        var $tg = $(candidates[j]).first();
        if ($tg.length) {
          var $card2  = $tg.closest('.card.accord-wysiw');
          if ($card2.length) {
            var $panel2 = $card2.children('div').first();
            unhideUpTo($tg, $panel2);
            openAccCard($card2);
            found = true;
          }
        }
      }
    }

    return found;
  }

  // petit retry (Drupal/lazy)
  function tryOpenWithRetry(maxTries, delay){
    var n = 0;
    (function tick(){
      if (tryOpenOnce()) return;
      if (++n < maxTries) setTimeout(tick, delay);
    })();
  }

  // au chargement + sur hashchange
  $(function(){ tryOpenWithRetry(12, 120); });
  $(window).on('hashchange', function(){ tryOpenWithRetry(12, 120); });

})(jQuery);


// Ajoute une classe au <body> selon le node ID (toutes langues)
// 1 classe par page, groupée par NIDs (même classe pour toutes les langues d’une même page)
// (function ($, Drupal, drupalSettings, once) {
//   Drupal.behaviors.mtlPageFlags = {
//     attach: function (context) {
//       $(once('mtlPageFlags', 'body', context)).each(function () {
//         var $body = $(this);
//         var nid = getNid();

//         // Map "classe commune" -> NIDs par langue
//         var MAP = {
//           'mot-du-ministre':        [1834],   // ← ajoute ici les autres NIDs de cette page si existent (AR/EN/AMZ)
//           'biographie-du-ministre': [1835],   // ← idem
//           'missions-et-gouvernance':[],
//           'organisation':           [1837]
//         };

//         // 1) retirer d'abord toutes les classes gérées par le MAP
//         Object.keys(MAP).forEach(function (cls) { $body.removeClass(cls); });

//         if (!nid) return;

//         // 2) ajouter UNE seule classe correspondant au NID courant
//         var added = false;
//         $.each(MAP, function (cls, list) {
//           if (added) return false; // break
//           list = (list || []).map(String).filter(Boolean);
//           if (list.indexOf(String(nid)) !== -1) {
//             $body.addClass(cls);
//             added = true;
//           }
//         });
//       });
//     }
//   };

//   // ----- Helpers -----
//   function getNid() {
//     var m;

//     // a) via classe body: page--nid-XXXX
//     m = (document.body.className || '').match(/(?:^|\s)page--nid-(\d+)(?=\s|$)/);
//     if (m) return m[1];

//     // b) via data attr du body (Drupal)
//     var attr = document.body.getAttribute('data-history-node-id')
//            || document.body.getAttribute('data-drupal-node-id')
//            || document.body.getAttribute('data-node-id');
//     if (attr) return attr;

//     // c) via drupalSettings: path.currentPath = "node/XXXX"
//     var cp = drupalSettings && drupalSettings.path && drupalSettings.path.currentPath;
//     m = cp && cp.match(/(?:^|\/)node\/(\d+)(?:$|\/)?/);
//     if (m) return m[1];

//     // d) via <link rel="shortlink|canonical" href=".../node/XXXX">
//     var ln = document.querySelector('link[rel="shortlink"], link[rel="canonical"]');
//     if (ln && ln.href) {
//       m = ln.href.match(/(?:^|\/)node\/(\d+)(?:$|\/)?/);
//       if (m) return m[1];
//     }

//     // e) fallback URL: retire préfixe langue, cherche /node/XXXX
//     var p = location.pathname.replace(/\/$/, '');
//     var seg1 = (p.split('/')[1] || '').toLowerCase();
//     if (['fr','en','ar','amz'].indexOf(seg1) !== -1) p = p.slice(seg1.length + 1);
//     m = p.match(/(?:^|\/)node\/(\d+)(?:$|\/)?/);
//     return m ? m[1] : null;
//   }
// })(jQuery, Drupal, drupalSettings, once);

(function ($, Drupal, drupalSettings, once) {
  Drupal.behaviors.mtlPageFlags = {
    attach: function (context) {
      $(once('mtlPageFlags', 'body', context)).each(function () {
        var $body = $(this);
        var nid = getNid();

        // même logique que tes autres pages : mapping par NID
        var MAP = {
          'mot-du-ministre':        [1834],   // FR (+ ajoute AR/EN/AMZ si besoin)
          'biographie-du-ministre': [1835],   // AMZ (+ ajoute FR/AR/EN si besoin)
          'missions-et-gouvernance':[1839],
          'organisation':           [2534],
          'strategie':              [2285],
          'strategie-rh-et-statistiques':              [1843],
          'valorisation-du-capital-humain':              [1844],
          'recrutement-et-carrieres':              [1844],
        };

        // 1) on retire d'abord les classes gérées
        Object.keys(MAP).forEach(function (cls) { $body.removeClass(cls); });

        // 2) on ajoute UNE seule classe en fonction du NID courant
        var added = false;
        $.each(MAP, function (cls, list) {
          if (added) return false; // break
          list = (list || []).map(String).filter(Boolean);
          if (nid && list.indexOf(String(nid)) !== -1) {
            $body.addClass(cls);
            added = true;
          }
        });

        // 3) Fallback: si pas de NID détecté mais URL finie par /organisation|/organization
        if (!added) {
          var p = location.pathname.toLowerCase().replace(/\/$/, '');
          // retire le préfixe langue (fr/en/ar/amz)
          var seg1 = (p.split('/')[1] || '').toLowerCase();
          if (['fr','en','ar','amz'].indexOf(seg1) !== -1) {
            p = '/' + p.split('/').slice(2).join('/');
          }
          if (/\/(organisation|organization)(\/|$)/.test(p)) {
            $body.addClass('organisation');
          }
        }
      });
    }
  };

  // ----- Helpers -----
  function getNid() {
    var m;
    // a) via classe body: page--nid-XXXX
    m = (document.body.className || '').match(/(?:^|\s)page--nid-(\d+)(?=\s|$)/);
    if (m) return m[1];

    // b) via data-* du body
    var attr = document.body.getAttribute('data-history-node-id')
            || document.body.getAttribute('data-drupal-node-id')
            || document.body.getAttribute('data-node-id');
    if (attr) return attr;

    // c) via drupalSettings: path.currentPath = "node/XXXX"
    var cp = drupalSettings && drupalSettings.path && drupalSettings.path.currentPath;
    m = cp && cp.match(/(?:^|\/)node\/(\d+)(?:$|\/)?/);
    if (m) return m[1];

    // d) via <link rel="shortlink|canonical" href=".../node/XXXX">
    var ln = document.querySelector('link[rel="shortlink"], link[rel="canonical"]');
    if (ln && ln.href) {
      m = ln.href.match(/(?:^|\/)node\/(\d+)(?:$|\/)?/);
      if (m) return m[1];
    }

    // e) fallback URL: retire préfixe langue, cherche /node/XXXX
    var p = location.pathname.replace(/\/$/, '');
    var seg1 = (p.split('/')[1] || '').toLowerCase();
    if (['fr','en','ar','amz'].indexOf(seg1) !== -1) p = p.slice(seg1.length + 1);
    m = p.match(/(?:^|\/)node\/(\d+)(?:$|\/)?/);
    return m ? m[1] : null;
  }
})(jQuery, Drupal, drupalSettings, once);

















