{% block paragraph %}
  {% block content %}
    {% set body = paragraph.field_body[0].value %}
    {% set image_uri = paragraph.field_image[0].entity.uri.value %}
    {% set right = paragraph.field_right_left[0].value %}
    {% if image_uri %}
      <div class="mt-5">
        <div class="bloc-odd">
          <div class="presentation-{{ right ? 'right' : 'left' }} card">
            <div id="c-wysiwyg">
              <div class="card px-4 py-5 p-sm-5">{{ body|raw }}</div>
            </div>
          </div>
          <div class="presentation-{{ right ? 'left' : 'right' }} card">
            <img class="card-img-top" src="{{ file_url(image_uri) }}" alt="{{ image_alt }}" loading="lazy" />
          </div>
        </div>
      </div>
    {% else %}
    <div class="margeBlock">
      <div class="mt-4 clearfix">
        <div id="c-wysiwyg">
          <div class="card px-4 py-5 p-sm-5">
            {{ body|raw }}
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  {% endblock %}
{% endblock %}
