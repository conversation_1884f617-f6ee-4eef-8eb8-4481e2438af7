{#
/**
 * @file
 * Default view template to display all the fields in a row.
 *
 * Available variables:
 * - view: The view in use.
 * - fields: A list of fields, each one contains:
 *   - content: The output of the field.
 *   - raw: The raw data for the field, if it exists. This is NOT output safe.
 *   - class: The safe class ID to use.
 *   - handler: The Views field handler controlling this field.
 *   - inline: Whether or not the field should be inline.
 *   - wrapper_element: An HTML element for a wrapper.
 *   - wrapper_attributes: List of attributes for wrapper element.
 *   - separator: An optional separator that may appear before a field.
 *   - label: The field's label text.
 *   - label_element: An HTML element for a label wrapper.
 *   - label_attributes: List of attributes for label wrapper.
 *   - label_suffix: Colon after the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 *   - has_label_colon: A boolean indicating whether to display a colon after
 *     the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 * - row: The raw result from the query, with all data it fetched.
 *
 * @see template_preprocess_views_view_fields()
 *
 * @ingroup themeable
 */
#}

{#
/**
 * @file
 * Default view template to display all the fields in a row.
 *
 * Available variables:
 * - view: The view in use.
 * - fields: A list of fields, each one contains:
 *   - content: The output of the field.
 *   - raw: The raw data for the field, if it exists. This is NOT output safe.
 *   - class: The safe class ID to use.
 *   - handler: The Views field handler controlling this field.
 *   - inline: Whether or not the field should be inline.
 *   - wrapper_element: An HTML element for a wrapper.
 *   - wrapper_attributes: List of attributes for wrapper element.
 *   - separator: An optional separator that may appear before a field.
 *   - label: The field's label text.
 *   - label_element: An HTML element for a label wrapper.
 *   - label_attributes: List of attributes for label wrapper.
 *   - label_suffix: Colon after the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 *   - has_label_colon: A boolean indicating whether to display a colon after
 *     the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 * - row: The raw result from the query, with all data it fetched.
 *
 * @see template_preprocess_views_view_fields()
 *
 * @ingroup themeable
 */
#}

{% if row._entity.field_image_media.entity.field_media_image.entity.uri.value %}
        {# <img class="card-img-top" 
             src="{{file_url(row._entity.field_image_media.entity.field_media_image.entity.uri.value|image_style('616_x_352'))}}" 
             alt="{{row._entity.field_image_media.entity.field_media_image.alt}}"
             fetchpriority="high"
             loading="lazy">
    {% else %}
        <img class="card-img-top" 
             src="{{file_url(row._entity.field_image.entity.uri.value|image_style('616_x_352'))}}" 
             alt="{{row._entity.field_image.alt}}"
             fetchpriority="high"
             loading="lazy">
    {% endif %} #}
    <img class="card-img-top" 
             src="{{file_url(row._entity.field_image_media.entity.field_media_image.entity.uri.value)}}" 
             alt="{{row._entity.field_image_media.entity.field_media_image.alt}}"
             fetchpriority="high"
             loading="lazy">
    {% else %}
        <img class="card-img-top" 
             src="{{file_url(row._entity.field_image.entity.uri.value)}}" 
             alt="{{row._entity.field_image.alt}}"
             fetchpriority="high"
             loading="lazy">
    {% endif %}
<div class="card-body">
    <h3 class="h2-title h3-title black">{{ fields.title.content }}</h3>
    <p> {{ fields.body.content }} </p>
    <span class="date">{{ fields.field_date.content|striptags|date('d/m/Y') }}</span>
</div>
<a href="{{ path('entity.node.canonical', {'node': row._entity.id}) }}" 
       class="click-me">
</a>
