{#
/**
 * @file
 * Default theme implementation to display a taxonomy term.
 *
 * Available variables:
 * - url: URL of the current term.
 * - name: (optional) Name of the current term.
 * - content: Items for the content of the term (fields and description).
 *   Use 'content' to print them all, or print a subset such as
 *   'content.description'. Use the following code to exclude the
 *   printing of a given child element:
 *   @code
 *   {{ content|without('description') }}
 *   @endcode
 * - attributes: HTML attributes for the wrapper.
 * - page: Flag for the full page state.
 * - term: The taxonomy term entity, including:
 *   - id: The ID of the taxonomy term.
 *   - bundle: Machine name of the current vocabulary.
 * - view_mode: View mode, e.g. 'full', 'teaser', etc.
 *
 * @see template_preprocess_taxonomy_term()
 *
 * @ingroup themeable
 */
#}
<section class="actu-listing margeBlock region-maps">
  <div class="container">
    <div class="row g-3 align-items-center">
      <div class="col-md-12 col-lg-6">
        <div class="card carte-item pt-2">
          <div class="card-body">
            <h2 class="h2-title">{{ name }}</h2>
            {% if content.description|render|striptags('<strong><em><a><img>')|trim is not empty %}
              <div class="description-wrapper">{{ content.description }}</div>
            {% endif %}
          </div>
        </div>
      </div>
      <div class="col-md-12 col-lg-6">{{ drupal_block('region_map') }}</div>
    </div>
  </div>
</section>
<div id="tooltip"></div>
<div class="margeBlock">
  <div class="container">
    <div class="row">
      <div class="col-12">{{ drupal_view('chiffres_cles', 'block_region') }}</div>
    </div>
  </div>
</div>
{{ drupal_view('projects', 'block_region') }}
{{ drupal_view('actualite', 'block_region') }}
{# {{ drupal_view('publication', 'block_region') }} #}

{% if term.field_region_contacts is not empty %}
  <div class="margeBlock">
    <div class="container infos-region">
      <div class="row g-3">
        <h2 class="h2-title d-flex justify-content-center">{{ 'Contacts des responsables'|t }}</h2>
        {{ content.field_region_contacts }}
      </div>
    </div>
  </div>
{% endif %}
<div class="margeBlock">
  <div class="share-buttons">
    <h2 class="h2-title h3-title transform-none pb-2">{{ 'Partager cette page'|t }}</h2>
    <div class="share-links d-flex flex-wrap gap-2 a2a_kit a2a_kit_size_32 a2a_default_style">
      <!-- Links -->
      <a href="/#facebook" class="btn btn-outline-secondary p-2 a2a_button_facebook" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur Facebook" data-bs-original-title="{{ 'Share on Facebook'|t }}" target="_blank" rel="nofollow noopener"><i class="fab fa-facebook-f"></i></a>
      <a href="/#whatsapp" class="btn btn-outline-secondary p-2 a2a_button_whatsapp" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur whatsapp" data-bs-original-title="{{ 'Share on whatsapp'|t }}" target="_blank" rel="nofollow noopener"><i class="fa-brands fa-whatsapp"></i></a>
      <a href="/#linkedin" class="btn btn-outline-secondary p-2 a2a_button_linkedin" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur Linkedin" data-bs-original-title="{{ 'Share on Linkedin'|t }}" target="_blank" rel="nofollow noopener"><i class="fa-brands fa-linkedin-in"></i></a>
      <a href="/#x" class="btn btn-outline-secondary p-2 a2a_button_x" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur x" data-bs-original-title="{{ 'Share on X'|t }}" target="_blank" rel="nofollow noopener"><i class="fa-brands fa-x-twitter"></i></a>
      <a href="/#x" class="btn btn-outline-secondary p-2 a2a_button_email" data-toggle="tooltip" data-placement="top" aria-label="Partagez sur email" data-bs-original-title="{{ 'Share on Email'|t }}" target="_blank" rel="nofollow noopener"><i class="fa-brands fa-google"></i></a>
      <a href="#" class="btn btn-outline-secondary p-2 a2a_button_print" data-toggle="tooltip" data-placement="top" aria-label="click to print" data-bs-original-title="{{ 'Click to print'|t }}"><i class="fas fa-print"></i></a>
      <a href="#" class="btn btn-outline-secondary p-2 a2a_dd" data-toggle="tooltip" data-placement="top" aria-label="click to share"><i class="fas fa-plus"></i></a>
      <div style="clear: both;"></div>
    </div>
  </div>
</div>

{{ drupal_view('other_regions', 'block_1') }}
