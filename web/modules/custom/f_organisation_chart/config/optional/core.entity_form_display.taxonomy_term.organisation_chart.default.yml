langcode: en
status: true
dependencies:
  config:
    - field.field.taxonomy_term.organisation_chart.field_description
    - field.field.taxonomy_term.organisation_chart.field_media_image
    - taxonomy.vocabulary.organisation_chart
  module:
    - media_library
    - path
id: taxonomy_term.organisation_chart.default
targetEntityType: taxonomy_term
bundle: organisation_chart
mode: default
content:
  field_description:
    type: string_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_media_image:
    type: media_library_widget
    weight: 2
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 3
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  translation:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  description: true
