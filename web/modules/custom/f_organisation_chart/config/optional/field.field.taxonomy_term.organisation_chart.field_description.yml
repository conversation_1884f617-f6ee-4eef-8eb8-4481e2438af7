langcode: en
status: true
dependencies:
  config:
    - field.storage.taxonomy_term.field_description
    - taxonomy.vocabulary.organisation_chart
id: taxonomy_term.organisation_chart.field_description
field_name: field_description
entity_type: taxonomy_term
bundle: organisation_chart
label: Description
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string_long
