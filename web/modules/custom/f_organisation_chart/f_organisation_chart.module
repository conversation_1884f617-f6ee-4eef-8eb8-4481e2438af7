<?php

/**
 * @file
 * Contains f_organisation_chart.module.
 */

use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function f_organisation_chart_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.f_organisation_chart':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Organisation Chart module provides functionality to create and display multiple hierarchical organisation charts using taxonomy terms.') . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Creating Charts') . '</dt>';
      $output .= '<dd>' . t('Go to Administration » Configuration » Content » Organisation Charts to create and manage multiple charts.') . '</dd>';
      $output .= '<dt>' . t('Displaying Charts') . '</dt>';
      $output .= '<dd>' . t('Use the Organisation Chart block to display charts on your pages. Each block can be configured to show a specific chart.') . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_theme().
 */
function f_organisation_chart_theme($existing, $type, $theme, $path) {
  return [
    'organisation_chart' => [
      'variables' => [
        'data' => NULL,
      ],
      'template' => 'organisation-chart',
    ],
  ];
}
