<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organigramme collapse</title>
    <style>
        html,
        body {
            margin: 0px;
            padding: 0px;
            width: 100%;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            min-height: 100vh;
            /* height: 100%; */
            /* overflow: hidden; */
        }

        #fornet {
            --org-color-title: #3c77ce;
            --org-color-name: #2e2e2e;
            --org-border-color: #75abff;
            --org-bg-color: #fafafa;
            background-color: transparent;
            width: 100%;
            /* height: 100%; */
            /* height: auto; */
            margin: 0 auto 60px;
        }
        #fornet .link path {
            stroke: var(--org-border-color);
        }
        #fornet .boc-fill {
            stroke: var(--org-border-color);
            fill: var(--org-border-color);
        }

        #fornet .boc-fill ~ line {
            stroke: #ffffff;
            stroke-width: 2;
        }

        #fornet .node {
            background-color: #fafafa !important;
            border: 1px solid #ccc;
        }

        /* #fornet .node rect {
            fill: #fafafa !important;
        } */

        #fornet .organigram-title {
            font-size: 16px;
            color: var(--org-color-title);
            text-align: center;
            line-height: 20px;
            font-weight: 700;
        }
        #fornet .organigram-name {
            font-size: 16px;
            color: var(--org-color-name);
            text-align: center;
        }
        #fornet .boc-search {
            display: none;
        }

        /* Custom style card */
        #fornet [data-n-id="1"] rect {
            fill: #042246;
        }
        #fornet [data-n-id="3"] rect,
        #fornet [data-n-id="8"] rect {
            fill: #004ca5;
        }
        #fornet [data-n-id="1"] .organigram-title,
        #fornet [data-n-id="1"] .organigram-name,
        #fornet [data-n-id="8"] .organigram-title,
        #fornet [data-n-id="8"] .organigram-name,
        #fornet [data-n-id="3"] .organigram-title,
        #fornet [data-n-id="3"] .organigram-name {
            color: white;
        }

        #fornet [data-sl="2"] rect,         /* sub level 2 */
        #fornet [data-n-id="2"] rect {
            fill: #e2edfa;
            stroke: #e2edfa;
        }
        #fornet [data-n-id="2"] .organigram-name {
            color: #3c77ce;
        }
        #fornet [data-l="3"] rect {
            fill: #fff;
            stroke: #fff;
        }

        /* partner firt level */
        #fornet [data-n-id="98"] rect {
            fill: transparent;
        }

        /* additional style */
        .container {
            max-width: 1024px;
            margin: 0 auto;
        }
        header {
            text-align: center;
            padding: 20px 0 40px;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }
        
        h1:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #4a8efb 0%, #75abff 100%);
            border-radius: 2px;
        }
    </style>
</head>

<body>

    <div class="container">
         <header>
            <h1>dynamic Organigramme</h1>
            <p class="description">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptates voluptatibus ullam adipisci! Sit soluta sapiente suscipit quae sed repudiandae saepe pariatur animi totam. Eius sit, doloribus aut vitae quia velit.</p>
        </header>
    </div>
    <div id="fornet"></div>


    <script src="https://balkan.app/js/OrgChart.js"></script>
    <script>
        OrgChart.templates.custom = Object.assign({}, OrgChart.templates.ana);
        OrgChart.templates.custom.size = [280, 120];
        OrgChart.templates.custom.node = '<rect x="0" y="0" width="280" height="120" rx="16" fill="#ffffff" stroke="#75abff" stroke-width="1"/>';
        OrgChart.templates.custom.link = '<path stroke-linejoin="round" stroke="#aeaeae" stroke-width="1px" fill="none" d="{edge}" />';
        OrgChart.templates.custom.img_0 = '<foreignObject x="55" y="-10" width="70" height="70"><img style="display:block;margin:0 auto;border-radius:50%;width:70px;height:70px;box-shadow:0 2px 8px rgba(0,0,0,0.15);" src="{val}" /></foreignObject>';
        OrgChart.templates.custom.field_0 = '<foreignObject x="0" y="20" width="280" height="50"><div class="organigram-title">{val}</div></foreignObject>';
        OrgChart.templates.custom.field_1 = '<foreignObject x="0" y="55" width="280" height="24"><div class="organigram-name">{val}</div></foreignObject>';
        OrgChart.templates.custom.field_2 = '<foreignObject x="0" y="165" width="180" height="30"><div style="text-align:center;margin-top:4px;"><a href="{val}" target="_blank" style="color:#1a0dab;text-decoration:underline;font-size:13px;">Voir le profil</a></div></foreignObject>';

        var chart = new OrgChart(document.getElementById("fornet"), {
            template: "custom",
            siblingSeparation: 20,      // space between siblings
            levelSeparation: 60,        // space between levels
            nodeBinding: {
                field_0: "desc",
                field_1: "name",
                img_0: "img",
                field_2: "link"
            },
            // mouseScrool: OrgChart.action.none,
            mouseScrool: OrgChart.action.scroll,
            scale: false,
            nodeMouseClick: OrgChart.action.none,

            // merge nodes with the same parent
            layout: OrgChart.mixed,

            // disable zoom
            zoom: {
                speed: 0,
                smooth: 0
            },
            scaleMin: 1,
            scaleMax: 1,
            scaleInitial: 1,            
            enableDragDrop: false,
            
            // align: OrgChart.align.center,
            // layout: OrgChart.layout.normal,
            scaleInitial: OrgChart.match.boundary,
            padding: 20,
            nodes: [
                {
                    id: 1,
                    name: "M. Tarik TALIBI",
                    desc: "Direction Générale de l'Aviation Civile",
                    // img: "https://placehold.co/70",
                },
                {
                    id: 99,
                    pid: 1,
                    name: "Mme. Samira BOUHOUIYEK",
                    desc: "Service des Marchés relatifs à l'Aviation Civile",
                    // img: "https://placehold.co/70",
                    tags: ['partner']
                },
                {
                    id: 98,
                    pid: 1,
                    name: "",
                    desc: "Service des Affaires Généraux",
                    // img: "https://placehold.co/70",
                    tags: ['partner']
                },
                {
                    id: 2,
                    pid: 1,
                    name: "Dib nour eddine",
                    desc: "Inspection générale",
                    // img: "https://placehold.co/70"
                },
                {
                    id: 3,
                    pid: 1,
                    name: "Cherkaoui khalid",
                    desc: "Inspection générale pedagogique",
                    // img: "https://placehold.co/70"
                },
                {
                    id: 8,
                    pid: 1,
                    name: "Cherkaoui aziz ",
                    desc: "Direction de Systèmes d'information",
                    // img: "https://placehold.co/70"
                },
                {
                    id: 4,
                    pid: 3,
                    name: "Elliot Patel",
                    desc: "Direction de la stategie du pilotage et de la coordination des transports",
                    // img: "https://placehold.co/70",
                    // link: "https://example.com/fran-parsons"
                },
                {
                    id: 5,
                    pid: 3,
                    name: "Lynn Hussain",
                    desc: "Designer UI/UX",
                    // img: "https://placehold.co/70",
                    // link: "https://example.com/fran-parsons"
                },
                {
                    id: 6,
                    pid: 2,
                    name: "Tanner May",
                    desc: "Testeur QA",
                    // img: "https://placehold.co/70",
                },
                {
                    id: 7,
                    pid: 3,
                    name: "Fran Parsons",
                    desc: "Support technique",
                    // img: "https://placehold.co/70",
                    // link: "https://example.com/fran-parsons"
                },
                {
                    id: 9,
                    pid: 8,
                    name: "Fran Parsons",
                    desc: "Support technique",
                    // img: "https://placehold.co/70",
                    // link: "https://example.com/fran-parsons"
                },
                {
                    id: 10,
                    pid: 3,
                    name: "Fran Parsons",
                    desc: "Support technique",
                    // img: "https://placehold.co/70",
                    // link: "https://example.com/fran-parsons"
                },
                {
                    id: 11,
                    pid: 6,
                    name: "Fran Parsons",
                    desc: "Support technique",
                    // img: "https://placehold.co/70",
                    // link: "https://example.com/fran-parsons"
                },
                {
                    id: 12,
                    pid: 6,
                    name: "Fran Parsons",
                    desc: "Support technique",
                    // img: "https://placehold.co/70",
                    // link: "https://example.com/fran-parsons"
                },

                // level 3 with aparent Lynn Hussain
                {
                    id: 13,
                    pid: 5,
                    name: "hussam al-ali",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
                {
                    id: 14,
                    pid: 5,
                    name: "M. Mohamed Essaghir LMATI",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
                {
                    id: 15,
                    pid: 5,
                    name: "M. Mohamed Essaghir LMATI",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
                {
                    id: 16,
                    pid: 5,
                    name: "M. Mohamed Essaghir LMATI",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
                // level 3 with aparent Elliot Patel
                {
                    id: 17,
                    pid: 4,
                    name: "hussam al-ali",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
                {
                    id: 18,
                    pid: 4,
                    name: "M. Mohamed Essaghir LMATI",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
                {
                    id: 19,
                    pid: 4,
                    name: "M. Mohamed Essaghir LMATI",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
                {
                    id: 20,
                    pid: 4,
                    name: "M. Mohamed Essaghir LMATI",
                    desc: "Service de la Navigabilité des Aéronefs",
                },
            ]
        });

        // adjust container height
        // chart.on('redraw', function() {
        //     var container = document.getElementById("fornet");
        //     var svg = container.querySelector('svg');
        //     if (svg) {
        //         var svgHeight = svg.getBBox().height + 40; // Add some padding
        //         container.style.height = svgHeight + 'px';
        //     }
        // });

        // on chart init. center and hieght container
        chart.on('init', function() {
            var svg = document.querySelector('#fornet svg');
            if (svg) {
                var bbox = svg.getBBox();
                var container = document.getElementById("fornet");
                var viewWidth = container.offsetWidth;

                var centerX = bbox.x + bbox.width / 2;
                var newX = centerX - viewWidth / 2;

                var svgWidth = svg.getBBox().width + 40; // Add some padding
                var svgHeight = svg.getBBox().height + 40; // Add some padding

                svg.setAttribute('viewBox', `${newX} 0 ${svgWidth} ${svgHeight}`);
                document.getElementById("fornet").style.height = svgHeight + 'px';
            }
        });

        chart.on('redraw', function() {
            // organigramme title
            var titleDivs = document.querySelectorAll('.organigram-title');
            // organigramme Name
            var nameDivs = document.querySelectorAll('.organigram-name');
            
            // set all foreignObject in svg to org title height
            titleDivs.forEach(function(div) {
                var foreignObject = div.closest('foreignObject');
                var foreignObjectName = foreignObject.nextElementSibling;

                if (foreignObject) {
                    // actual height
                    var contentHeight = div.scrollHeight;
                    // Update foreignObject height
                    foreignObject.setAttribute('height', contentHeight + 10);
                }

                if (foreignObjectName) {
                    foreignObjectName.setAttribute('y', parseInt(foreignObject.getAttribute('y')) + contentHeight + 10);
                }
            });
            
        });
    </script>
</body>

</html>