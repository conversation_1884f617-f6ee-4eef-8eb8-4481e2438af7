<?php

namespace Drupal\f_organisation_chart\Form;

use Drupal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use Drupal\Core\Url;
use Drupal\taxonomy\Entity\Vocabulary;

/**
 * Form for adding/editing organisation charts.
 */
class OrganisationChartForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['f_organisation_chart.settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'f_organisation_chart_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, $chart_id = NULL) {
    $config = $this->config('f_organisation_chart.settings');
    $charts = $config->get('charts') ?: [];
    $chart = $chart_id && isset($charts[$chart_id]) ? $charts[$chart_id] : [];

    $form['chart_id'] = [
      '#type' => 'value',
      '#value' => $chart_id,
    ];

    $form['name'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Chart Name'),
      '#required' => TRUE,
      '#default_value' => $chart['name'] ?? '',
      '#description' => $this->t('Human readable name for this organisation chart.'),
    ];

    $form['machine_name'] = [
      '#type' => 'machine_name',
      '#title' => $this->t('Machine Name'),
      '#required' => TRUE,
      '#default_value' => $chart_id ?? '',
      '#machine_name' => [
        'exists' => [$this, 'chartExists'],
        'source' => ['name'],
      ],
      '#disabled' => !empty($chart_id),
      '#description' => $this->t('Machine name for this chart. Used in block configuration.'),
    ];

    // Get available vocabularies
    $vocabularies = Vocabulary::loadMultiple();
    $vocab_options = [];
    foreach ($vocabularies as $vocabulary) {
      $vocab_options[$vocabulary->id()] = $vocabulary->label();
    }

    $form['vocabulary'] = [
      '#type' => 'select',
      '#title' => $this->t('Taxonomy Vocabulary'),
      '#required' => TRUE,
      '#options' => $vocab_options,
      '#default_value' => $chart['vocabulary'] ?? 'organisation_chart',
      '#description' => $this->t('Select the taxonomy vocabulary to use for this chart.'),
    ];

    $form['status'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Enabled'),
      '#default_value' => $chart['status'] ?? TRUE,
      '#description' => $this->t('Enable this organisation chart.'),
    ];

    $form['description'] = [
      '#type' => 'textarea',
      '#title' => $this->t('Description'),
      '#default_value' => $chart['description'] ?? '',
      '#description' => $this->t('Optional description for this chart.'),
    ];

    // Chart styling options
    $form['styling'] = [
      '#type' => 'details',
      '#title' => $this->t('Chart Styling'),
      '#open' => FALSE,
    ];

    $form['styling']['node_width'] = [
      '#type' => 'number',
      '#title' => $this->t('Node Width'),
      '#default_value' => $chart['styling']['node_width'] ?? 180,
      '#min' => 100,
      '#max' => 400,
      '#description' => $this->t('Width of each node in pixels.'),
    ];

    $form['styling']['node_height'] = [
      '#type' => 'number',
      '#title' => $this->t('Node Height'),
      '#default_value' => $chart['styling']['node_height'] ?? 220,
      '#min' => 100,
      '#max' => 400,
      '#description' => $this->t('Height of each node in pixels.'),
    ];

    $form['styling']['sibling_separation'] = [
      '#type' => 'number',
      '#title' => $this->t('Sibling Separation'),
      '#default_value' => $chart['styling']['sibling_separation'] ?? 20,
      '#min' => 5,
      '#max' => 100,
      '#description' => $this->t('Space between sibling nodes.'),
    ];

    $form['styling']['level_separation'] = [
      '#type' => 'number',
      '#title' => $this->t('Level Separation'),
      '#default_value' => $chart['styling']['level_separation'] ?? 60,
      '#min' => 20,
      '#max' => 200,
      '#description' => $this->t('Space between hierarchy levels.'),
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $chart_id ? $this->t('Update Chart') : $this->t('Save Chart'),
      '#button_type' => 'primary',
    ];

    $form['actions']['cancel'] = [
      '#type' => 'link',
      '#title' => $this->t('Cancel'),
      '#url' => Url::fromRoute('f_organisation_chart.settings'),
      '#attributes' => ['class' => ['button']],
    ];

    return $form;
  }

  /**
   * Machine name exists callback.
   */
  public function chartExists($value) {
    $config = $this->config('f_organisation_chart.settings');
    $charts = $config->get('charts') ?: [];
    return isset($charts[$value]);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $config = $this->config('f_organisation_chart.settings');
    $charts = $config->get('charts') ?: [];
    
    $chart_id = $form_state->getValue('chart_id') ?: $form_state->getValue('machine_name');
    
    $charts[$chart_id] = [
      'name' => $form_state->getValue('name'),
      'vocabulary' => $form_state->getValue('vocabulary'),
      'status' => $form_state->getValue('status'),
      'description' => $form_state->getValue('description'),
      'styling' => [
        'node_width' => $form_state->getValue('node_width'),
        'node_height' => $form_state->getValue('node_height'),
        'sibling_separation' => $form_state->getValue('sibling_separation'),
        'level_separation' => $form_state->getValue('level_separation'),
      ],
    ];

    $config->set('charts', $charts)->save();

    $this->messenger()->addMessage($this->t('Organisation chart saved successfully.'));
    $form_state->setRedirect('f_organisation_chart.settings');
  }

}