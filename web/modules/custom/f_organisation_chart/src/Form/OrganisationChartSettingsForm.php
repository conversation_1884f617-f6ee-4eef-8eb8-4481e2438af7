<?php

namespace Drupal\f_organisation_chart\Form;

use <PERSON>upal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON>upal\Core\Url;
use <PERSON>upal\Core\Link;

/**
 * Configuration form for organisation charts.
 */
class OrganisationChartSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['f_organisation_chart.settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'f_organisation_chart_settings_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('f_organisation_chart.settings');
    $charts = $config->get('charts') ?: [];

    $form['description'] = [
      '#markup' => '<p>' . $this->t('Manage multiple organisation charts. Each chart can have its own taxonomy vocabulary and configuration.') . '</p>',
    ];

    // Add new chart button
    $form['add_chart'] = [
      '#type' => 'link',
      '#title' => $this->t('Add New Chart'),
      '#url' => Url::fromRoute('f_organisation_chart.chart_form'),
      '#attributes' => [
        'class' => ['button', 'button--primary'],
      ],
    ];

    // Charts table
    $form['charts_table'] = [
      '#type' => 'table',
      '#header' => [
        $this->t('Name'),
        $this->t('Machine Name'),
        $this->t('Vocabulary'),
        $this->t('Status'),
        $this->t('Operations'),
      ],
      '#empty' => $this->t('No charts found. <a href="@url">Add a chart</a>.', [
        '@url' => Url::fromRoute('f_organisation_chart.chart_form')->toString(),
      ]),
    ];

    foreach ($charts as $chart_id => $chart) {
      $form['charts_table'][$chart_id]['name'] = [
        '#markup' => $chart['name'],
      ];
      $form['charts_table'][$chart_id]['machine_name'] = [
        '#markup' => $chart_id,
      ];
      $form['charts_table'][$chart_id]['vocabulary'] = [
        '#markup' => $chart['vocabulary'],
      ];
      $form['charts_table'][$chart_id]['status'] = [
        '#markup' => $chart['status'] ? $this->t('Enabled') : $this->t('Disabled'),
      ];

      $operations = [];
      $operations['edit'] = [
        'title' => $this->t('Edit'),
        'url' => Url::fromRoute('f_organisation_chart.chart_edit', ['chart_id' => $chart_id]),
      ];
      $operations['delete'] = [
        'title' => $this->t('Delete'),
        'url' => Url::fromRoute('f_organisation_chart.chart_delete', ['chart_id' => $chart_id]),
      ];

      $form['charts_table'][$chart_id]['operations'] = [
        '#type' => 'operations',
        '#links' => $operations,
      ];
    }

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);
  }

}