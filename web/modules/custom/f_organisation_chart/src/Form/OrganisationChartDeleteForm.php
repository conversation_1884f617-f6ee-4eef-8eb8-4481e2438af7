<?php

namespace Drupal\f_organisation_chart\Form;

use <PERSON><PERSON>al\Core\Form\ConfirmFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Url;

/**
 * Form for deleting organisation charts.
 */
class OrganisationChartDeleteForm extends ConfirmFormBase {

  /**
   * The chart ID to delete.
   *
   * @var string
   */
  protected $chartId;

  /**
   * The chart data.
   *
   * @var array
   */
  protected $chart;

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'f_organisation_chart_delete_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, $chart_id = NULL) {
    $this->chartId = $chart_id;
    
    $config = $this->config('f_organisation_chart.settings');
    $charts = $config->get('charts') ?: [];
    
    if (!isset($charts[$chart_id])) {
      $this->messenger()->addError($this->t('Chart not found.'));
      $form_state->setRedirect('f_organisation_chart.settings');
      return [];
    }
    
    $this->chart = $charts[$chart_id];
    
    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function getQuestion() {
    return $this->t('Are you sure you want to delete the organisation chart %name?', [
      '%name' => $this->chart['name']
    ]);
  }

  /**
   * {@inheritdoc}
   */
  public function getDescription() {
    return $this->t('This action cannot be undone. The chart configuration will be deleted, but the taxonomy terms will remain.');
  }

  /**
   * {@inheritdoc}
   */
  public function getCancelUrl() {
    return new Url('f_organisation_chart.settings');
  }

  /**
   * {@inheritdoc}
   */
  public function getConfirmText() {
    return $this->t('Delete');
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $config = $this->config('f_organisation_chart.settings');
    $charts = $config->get('charts') ?: [];
    
    unset($charts[$this->chartId]);
    
    $config->set('charts', $charts)->save();
    
    $this->messenger()->addMessage($this->t('Organisation chart %name has been deleted.', [
      '%name' => $this->chart['name']
    ]));
    
    $form_state->setRedirect('f_organisation_chart.settings');
  }

}