<?php

/**
 * @file
 * Contains \Drupal\f_organisation_chart\Plugin\Block.
 */

namespace Drupal\f_organisation_chart\Plugin\Block;

use Drupal\Core\Block\BlockBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\taxonomy\Entity\Term;
use Drupal\Core\Url;

/**
 * Provides an Organisation Chart block.
 *
 * @Block(
 *   id = "organisation_chart",
 *   admin_label = @Translation("Organisation Chart"),
 *   category = @Translation("Organisation Chart")
 * )
 */
class OrganisationChart extends BlockBase {

  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return [
      'chart_id' => '',
      'show_title' => TRUE,
    ] + parent::defaultConfiguration();
  }

  /**
   * {@inheritdoc}
   */
  public function blockForm($form, FormStateInterface $form_state) {
    $form = parent::blockForm($form, $form_state);
    $config = $this->getConfiguration();

    // Get available charts
    $chart_config = \Drupal::config('f_organisation_chart.settings');
    $charts = $chart_config->get('charts') ?: [];
    
    $chart_options = [];
    foreach ($charts as $chart_id => $chart) {
      if ($chart['status']) {
        $chart_options[$chart_id] = $chart['name'];
      }
    }

    $form['chart_id'] = [
      '#type' => 'select',
      '#title' => $this->t('Select Chart'),
      '#options' => $chart_options,
      '#default_value' => $config['chart_id'],
      '#required' => TRUE,
      '#empty_option' => $this->t('- Select a chart -'),
      '#description' => $this->t('Choose which organisation chart to display.'),
    ];

    $form['show_title'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Show chart title'),
      '#default_value' => $config['show_title'],
      '#description' => $this->t('Display the chart name as a title above the chart.'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
    parent::blockSubmit($form, $form_state);
    $this->configuration['chart_id'] = $form_state->getValue('chart_id');
    $this->configuration['show_title'] = $form_state->getValue('show_title');
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $config = $this->getConfiguration();
    $chart_id = $config['chart_id'];

    if (empty($chart_id)) {
      return [
        '#markup' => $this->t('Please configure this block to select a chart.'),
        '#cache' => ['max-age' => 0],
      ];
    }

    // Get chart configuration
    $chart_config = \Drupal::config('f_organisation_chart.settings');
    $charts = $chart_config->get('charts') ?: [];
    
    if (!isset($charts[$chart_id]) || !$charts[$chart_id]['status']) {
      return [
        '#markup' => $this->t('Selected chart is not available.'),
        '#cache' => ['max-age' => 0],
      ];
    }

    $chart = $charts[$chart_id];
    $lang = \Drupal::languageManager()->getCurrentLanguage()->getId();

    // Load terms from the configured vocabulary
    $vid = $chart['vocabulary'];
    $terms = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties(['vid' => $vid]);

    $data = [];
    foreach ($terms as $id => $term) {
      if ($term->hasTranslation($lang)) {
        $term = $term->getTranslation($lang);
      }

      $url = Url::fromRoute('entity.taxonomy_term.canonical', ['taxonomy_term' => $term->id()]);
      $absolute_url = $url->setAbsolute()->toString();

      $item = [
        'id' => $term->id(),
        'name' => $term->label(),
        'desc' => '',
        'link' => $absolute_url,
      ];

      // Get description field if it exists
      if ($term->hasField('field_description') && !$term->get('field_description')->isEmpty()) {
        $item['desc'] = $term->get('field_description')->value;
      }

      // Get image field if it exists
      if ($term->hasField('field_media_image') && !$term->get('field_media_image')->isEmpty()) {
        try {
          $media_entity = $term->get('field_media_image')->entity;
          if ($media_entity && $media_entity->hasField('field_media_image')) {
            $image_field = $media_entity->get('field_media_image')->first();
            if ($image_field) {
              $file_uri = $image_field->entity->getFileUri();
              $image_url = \Drupal::service('file_url_generator')->generateAbsoluteString($file_uri);
              $item['img'] = $image_url;
            }
          }
        } catch (\Exception $e) {
          // Skip image if there's an error
        }
      }

      // Get color field if it exists, otherwise use default
      if ($term->hasField('field_color') && !$term->get('field_color')->isEmpty()) {
        $color_field = $term->get('field_color')->first();
        $color_value = $color_field ? $color_field->getValue() : null;
        
        // Handle different color field formats
        if ($color_value) {
          if (isset($color_value['value'])) {
            $item['color'] = $color_value['value'];
          } elseif (isset($color_value['color'])) {
            $item['color'] = $color_value['color'];
          } else {
            $item['color'] = $color_value;
          }
        } else {
          $item['color'] = '#ffffff';
        }
        
        // Debug: log the color value
        \Drupal::logger('f_organisation_chart')->info('Term @id (@name) has color: @color (raw: @raw)', [
          '@id' => $term->id(),
          '@name' => $term->label(),
          '@color' => $item['color'],
          '@raw' => json_encode($color_value),
        ]);
      } else {
        $item['color'] = '#ffffff'; // Default white background
        \Drupal::logger('f_organisation_chart')->info('Term @id (@name) using default color (no field or empty)', [
          '@id' => $term->id(),
          '@name' => $term->label(),
        ]);
      }

      // Get link field if it exists
      if ($term->hasField('field_lien') && !$term->get('field_lien')->isEmpty()) {
        $link_field = $term->get('field_lien')->first();
        if ($link_field) {
          $item['link_url'] = $link_field->getUrl()->toString();
          $item['link_title'] = $link_field->title ?: 'Voir plus';
        }
      }

      // Get parent relationship
      $parents = \Drupal::entityTypeManager()
        ->getStorage('taxonomy_term')
        ->loadParents($term->id());

      if (!empty($parents)) {
        $pid = reset($parents)->id();
        $item['pid'] = $pid;
      }
      
      $data[] = $item;
    }

    $unique_id = 'org-chart-' . $chart_id . '-' . $this->getPluginId();

    return [
      '#theme' => 'organisation_chart',
      '#data' => [
        'terms' => $data,
        'chart' => $chart,
        'chart_id' => $chart_id,
        'unique_id' => $unique_id,
        'show_title' => $config['show_title'],
      ],
      '#attached' => [
        'library' => [
          'f_organisation_chart/organisation-chart',
        ],
        'drupalSettings' => [
          'f_organisation_chart' => [
            $unique_id => [
              'data' => $data,
              'chart' => $chart,
            ],
          ],
        ],
      ],
      '#cache' => [
        'tags' => ['config:f_organisation_chart.settings', 'taxonomy_term_list:' . $vid],
        'contexts' => ['languages:language_content'],
      ],
    ];
  }

}
