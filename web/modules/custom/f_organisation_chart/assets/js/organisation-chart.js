(function (Drupal, drupalSettings) {
  'use strict';

  // ===== Templates =====
  if (typeof OrgChart !== 'undefined' && !OrgChart.templates.custom) {
    OrgChart.templates.custom = Object.assign({}, OrgChart.templates.ana);
    OrgChart.templates.custom.size = [280, 150];
    OrgChart.templates.custom.node =
      '<rect x="0" y="0" width="280" height="150" rx="16" fill="#ffffff" stroke="#75abff" stroke-width="1"/>';
    OrgChart.templates.custom.img_0 =
      '<foreignObject class="img" x="94" y="-40" width="70" height="70"><img style="display:block;margin:0 auto;border-radius:50%;width:70px;height:70px;" src="{val}" /></foreignObject>';
    OrgChart.templates.custom.field_0 =
      '<foreignObject x="0" y="50" width="280" height="50"><div class="organigram-title" style="font-size:14px">{val}</div></foreignObject>';
    OrgChart.templates.custom.field_1 =
      '<foreignObject x="0" y="80" width="280" height="100"><div class="organigram-name">{val}</div></foreignObject>';
  }

  if (typeof OrgChart !== 'undefined' && !OrgChart.templates.customNoImg) {
    OrgChart.templates.customNoImg = Object.assign({}, OrgChart.templates.ana);
    OrgChart.templates.customNoImg.size = [280, 150];
    OrgChart.templates.customNoImg.node =
      '<rect x="0" y="0" width="280" height="150" rx="16" fill="#ffffff" stroke="#75abff" stroke-width="1"/>';
    OrgChart.templates.customNoImg.field_0 =
      '<foreignObject x="0" y="50" width="280" height="50"><div class="organigram-title">{val}</div></foreignObject>';
    OrgChart.templates.customNoImg.field_1 =
      '<foreignObject x="0" y="80" width="280" height="100"><div class="organigram-name">{val}</div></foreignObject>';
  }


  function prepareData(data) {
    // console.log('Organisation Chart: preparing data', data);
    return data.map(function (n) {
      var d = Object.assign({}, n);
      var hasImg =
      d.img && typeof d.img === 'string' && d.img.trim() !== '' &&
      !/^(#|about:blank)$/i.test(d.img.trim());
      
      if (!hasImg) {
        d.tags = Array.isArray(d.tags) ? d.tags.concat('noimg') : ['noimg'];
      }

      // Jointure entre les parents premier niveau 
      if (d.id == 140 || d.id == 139) {
        d.pid = 132;              // parent id (important for tags jointure to work)
        d.tags = ['partner'];     // tags partner for jointure 
      }
      if (d.id == 196 || d.id == 197) {
        d.pid = 177;              // parent id (important for tags jointure to work)
        d.tags = ['partner'];     // tags partner for jointure 
      }
      if (d.id == 242 || d.id == 243) {
        d.pid = 226;              // parent id (important for tags jointure to work)
        d.tags = ['partner'];     // tags partner for jointure 
      }
      if (d.id == 199) {
        d.pid = 198;              // parent id (important for tags jointure to work)
        d.tags = ['partner'];     // tags partner for jointure 
      }
      if (d.id == 171) {
        d.pid = 168;              // parent id (important for tags jointure to work)
        d.tags = ['partner'];     // tags partner for jointure 
      }
      
      return d;
    });
  }

  // ===== Init single chart =====
  function initOrgChart(containerId, data) {
    const container = document.getElementById(containerId);
    if (!container || !data) { console.error('Organisation Chart: container/data manquant(s) pour', containerId); return null; }

    // wrapper arrondi qui clippe les bords
    if (!container.parentElement || !container.parentElement.classList.contains('org-card-shell')) {
      const shell = document.createElement('div');
      shell.className = 'org-card-shell';
      container.parentNode.insertBefore(shell, container);
      shell.appendChild(container);
    }
    const shell = container.parentElement;

    const chart = new OrgChart(container, {
      template: "custom",
      siblingSeparation: 20,
      levelSeparation: 56,      // plus compact
      padding: 8,               // marge externe du diagramme
      align: OrgChart.align.center,
      nodeBinding: { field_0: "desc", field_1: "name", img_0: "img", field_2: "link" },
      tags: { 
        noimg: { template: "customNoImg" }
      },
      showXScroll: OrgChart.scroll.visible,
      showYScroll: OrgChart.scroll.visible,
      nodeMouseClick: OrgChart.action.none,

      // ✅ Zoom molette + bornes 40% ↔ 100%
      zoom: { speed: 50, smooth: 10 },
      scaleMin: 0.4,
      scaleMax: 1.0,
      scaleInitial: 1,
      mouseScrool: OrgChart.action.none,
      toolbar: false,

      enableDragDrop: false,
      searchFields: [],
      searchUI: false,
      nodes: prepareData(data)
    });
    // === Centrer horizontalement la 1ʳᵉ rangée (root + partners) ===
    (function centerTopRow() {
      const svg = container.querySelector('svg');
      if (!svg) return;

      const nodes = Array.from(svg.querySelectorAll('g.node'));
      if (!nodes.length) return;

      // 1) détecter la rangée la plus haute via le plus petit Y
      const boxes = nodes.map(n => ({ n, b: n.getBBox() }));
      let minY = Infinity;
      boxes.forEach(o => { if (o.b.y < minY) minY = o.b.y; });

      const EPS = 2; // tolérance pour attraper la même rangée
      const firstRow = boxes.filter(o => Math.abs(o.b.y - minY) <= EPS);
      if (!firstRow.length) return;

      // 2) bbox horizontale de cette rangée
      let minX = Infinity, maxX = -Infinity;
      firstRow.forEach(o => {
        minX = Math.min(minX, o.b.x);
        maxX = Math.max(maxX, o.b.x + o.b.width);
      });
      const rowCenter = (minX + maxX) / 2;

      // 3) largeur du viewport en unités SVG
      const vb = svg.viewBox && svg.viewBox.baseVal;
      const svgWidth = (vb && vb.width) ? vb.width : svg.getBoundingClientRect().width;
      if (!svgWidth) return;

      // 4) groupe pan/zoom (selon versions d’OrgChart)
      const panGroup =
        svg.querySelector('g[boc-id]') ||
        svg.querySelector('g.boc')     ||
        svg.querySelector('svg > g')   ||
        svg.querySelector('g');

      if (!panGroup) return;

      // 5) tenir compte d’un éventuel zoom (scale) déjà appliqué
      const cons   = panGroup.transform.baseVal.consolidate();
      const m0     = cons ? cons.matrix : svg.createSVGMatrix();
      const scaleX = m0.a || 1;

      // centre actuel du viewport dans le repère contenu
      const viewportCenter = (-m0.e) / scaleX + (svgWidth / (2 * scaleX));

      // décalage nécessaire pour aligner le centre de la 1ʳᵉ rangée
      const dx = viewportCenter - rowCenter;

      if (Math.abs(dx) > 0.5) {
        const m1 = m0.translate(dx, 0);
        const t  = svg.createSVGTransformFromMatrix(m1);
        panGroup.transform.baseVal.initialize(t);
      }
    })();

    (function addZoomControls() {
      const old = shell.querySelector('.org-zoom-box');
      if (old) old.remove();

      const box = document.createElement('div');
      box.className = 'org-zoom-box';
      
      box.innerHTML = `
        <button type="button" class="org-zoom-btn org-zoom-in" aria-label="Zoomer">+</button>
        <div class="org-zoom-badge" aria-live="polite">100%</div>
        <button type="button" class="org-zoom-btn org-zoom-out" aria-label="Dézoomer">−</button>
      `;
      // shell.appendChild(box);
      shell.insertAdjacentElement('afterbegin', box);

      const btnIn  = box.querySelector('.org-zoom-in');
      const btnOut = box.querySelector('.org-zoom-out');
      const badge  = box.querySelector('.org-zoom-badge');

      const STEP = 0.10;
      let busy = false;

      function getScale(){
        return (chart.getScale && chart.getScale())
            || (typeof chart.scale === 'number' ? chart.scale
            :  (typeof chart.config?.scale === 'number' ? chart.config.scale : 1));
      }
      function clamp(x){
        const min = typeof chart.config?.scaleMin === 'number' ? chart.config.scaleMin : 0.4; // 40%
        const max = typeof chart.config?.scaleMax === 'number' ? chart.config.scaleMax : 1.0; // 100%
        return Math.max(min, Math.min(max, x));
      }
      function updateUI(){
        const s   = getScale();
        const min = typeof chart.config?.scaleMin === 'number' ? chart.config.scaleMin : 0.4;
        const max = typeof chart.config?.scaleMax === 'number' ? chart.config.scaleMax : 1.0;
        badge.textContent = Math.round(s * 100) + '%';
        btnOut.disabled = s <= min + 0.001;
        btnIn.disabled  = s >= max - 0.001;
      }
      function zoomTo(target){
        if (busy) return;
        busy = true;
        target = clamp(Math.round(target * 100) / 100);

        if (typeof chart.zoomIn === 'function' && typeof chart.zoomOut === 'function') {
          let s = getScale();
          const up = target > s;
          let steps = Math.min(20, Math.round(Math.abs(target - s) / STEP));
          if (steps === 0) { busy = false; updateUI(); return; }
          (function tick(){
            if (steps-- <= 0) { busy = false; updateUI(); return; }
            up ? chart.zoomIn() : chart.zoomOut();
            requestAnimationFrame(tick);
          })();
          return;
        }

        if (typeof chart.zoom === 'function' && chart.zoom.length <= 1) {
          chart.zoom(target);
        } else if (typeof chart.setScale === 'function') {
          chart.setScale(target);
        } else {
          (chart.config || (chart.config = {})).scale = target;
          if (typeof chart.draw === 'function') chart.draw();
        }
        busy = false;
        updateUI();
      }

      btnIn.onclick  = () => zoomTo(getScale() * 1.1);
      btnOut.onclick = () => zoomTo(getScale() / 1.1);
      chart.on('redraw', updateUI);
      updateUI();
    })();

    // ===== Centrage vertical robuste + safe gap sous la photo =====
   const NODE_HEIGHT = 150, GAP = 8, V_PADDING = 12, IMG_SAFE_GAP = 10;

    chart.on('redraw', function () {
      requestAnimationFrame(function () {
        const svg = container.querySelector('svg');

        container.querySelectorAll('g.node').forEach(function (node) {
          // Apply custom background colors
          let nodeId = node.getAttribute('node-id') || 
                      node.getAttribute('data-n-id') || 
                      node.id;
          
          if (!nodeId) {
            const classes = node.getAttribute('class') || '';
            const match = classes.match(/node-(\d+)/);
            if (match) {
              nodeId = match[1];
            }
          }
          
          if (nodeId) {
            const nodeData = data.find(function(d) { return d.id == nodeId; });
            const rect = node.querySelector('rect');
            if (rect && nodeData && nodeData.color && nodeData.color !== '#ffffff') {
              // Apply custom color only if different from default
              rect.setAttribute('fill', nodeData.color);
              rect.style.setProperty('fill', nodeData.color, 'important');
            }
            
            // Add link if exists
            if (nodeData && nodeData.link_url) {
              const nameDiv = node.querySelector('.organigram-name');
              if (nameDiv && !nameDiv.querySelector('.voir-plus-link')) {
                const linkElement = document.createElement('a');
                linkElement.href = nodeData.link_url;
                linkElement.textContent = nodeData.link_title;
                linkElement.className = 'voir-plus-link';
                linkElement.style.display = 'block';
                linkElement.style.color = '#007cba';
                linkElement.style.textDecoration = 'underline';
                linkElement.style.fontSize = '12px';
                linkElement.style.marginTop = '5px';
                linkElement.style.textAlign = 'center';
                nameDiv.appendChild(linkElement);
              }
            }
          }
          
          const titleDiv = node.querySelector('.organigram-title');
          const nameDiv  = node.querySelector('.organigram-name');
          if (!titleDiv && !nameDiv) return;

          const titleFO = titleDiv ? titleDiv.closest('foreignObject') : null;
          const nameFO  = nameDiv  ? nameDiv.closest('foreignObject')  : null;

          const hasTitle = !!(titleDiv && titleDiv.textContent.trim());
          const hasName  = !!(nameDiv  && nameDiv.textContent.trim());

          const titleH = hasTitle ? Math.ceil(titleDiv.scrollHeight || 0) : 0;
          const nameH  = hasName  ? Math.ceil(nameDiv.scrollHeight  || 0) : 0;

          let totalH;
          if (hasTitle && hasName) totalH = titleH + GAP + nameH;
          else if (hasTitle)       totalH = titleH;
          else                     totalH = nameH;

          let top = Math.round((NODE_HEIGHT - totalH) / 2);
          top = Math.max(V_PADDING, Math.min(top, NODE_HEIGHT - V_PADDING - totalH));

          const imgFO = node.querySelector('foreignObject.img');
          if (imgFO) {
            const imgY   = parseInt(imgFO.getAttribute('y'), 10) || -40;
            const imgH   = parseInt(imgFO.getAttribute('height'), 10) || 80;
            const imgBot = imgY + imgH;
            top = Math.max(top, imgBot + IMG_SAFE_GAP);
            top = Math.min(top, NODE_HEIGHT - V_PADDING - totalH);
          }

          let curY = top;

          if (titleFO) {
            if (hasTitle) {
              titleFO.setAttribute('y', curY);
              titleFO.setAttribute('height', titleH + 2);
              curY += titleH;
            } else {
              titleFO.setAttribute('y', top);
              titleFO.setAttribute('height', 0);
            }
          }

          if (hasTitle && hasName) curY += GAP;

          if (nameFO) {
            if (hasName) {
              nameFO.setAttribute('y', curY);
              nameFO.setAttribute('height', nameH + 2);
            } else {
              nameFO.setAttribute('y', top);
              nameFO.setAttribute('height', 0);
            }
          }
        });

        // garder nœuds/boutons au-dessus des liens
        if (svg) {
          svg.querySelectorAll('g.node').forEach(n => svg.appendChild(n));
          svg.querySelectorAll('g[data-ctrl-ec-id]').forEach(b => svg.appendChild(b));
        }
        requestAnimationFrame(function () {
          const STROKE = '#3C77CE';   // bleu
          const WIDTH  = 1;         // épaisseur souhaitée

          // Cible robuste : groupes de liens et tout path sans remplissage (fill="none")
          const linkPaths = container.querySelectorAll(
            'g.link path, g.links path, path[fill="none"]'
          );

        linkPaths.forEach(p => {
          p.style.setProperty('stroke', STROKE, 'important');
          p.style.setProperty('stroke-width', String(WIDTH), 'important');
          p.style.setProperty('fill', 'none', 'important');
          p.style.setProperty('opacity', '1', 'important');
        });

          // (optionnel) harmoniser le contour des boutons d’expansion
          container.querySelectorAll('g[data-ctrl-ec-id] circle').forEach(c => {
            c.style.setProperty('stroke', STROKE, 'important');
            c.style.setProperty('stroke-width', '1', 'important');
            // c.style.setProperty('fill', 'transparent', 'important'); // si besoin que le trait passe dessous
          });
         const rules = [
          { ids: ['139','140'], style: { fill: 'transparent', stroke: '#3C77CE' } },
          { ids: ['135'], style: { fill: '#E2EDFA', stroke: 'transparent', } },
          { ids: ['136','137','138','196','197','242','243', '199','171'], style: { fill: '#fff', stroke: '#3C77CE', } }
        ];

        rules.forEach(rule => {
          const sel = rule.ids
            .map(id => `g[data-n-id="${id}"] rect, g[node-id="${id}"] rect`)
            .join(', ');
          container.querySelectorAll(sel).forEach(rect => {
            for (const [prop, val] of Object.entries(rule.style)) {
              if (val == null) rect.style.removeProperty(prop);
              else rect.style.setProperty(prop, val, 'important');
            }
          });
        });
        });
      });
    });

    return chart;
  }

  // ===== Init all charts on page =====
  function initAllCharts() {
    if (!drupalSettings.f_organisation_chart) return;
    Object.keys(drupalSettings.f_organisation_chart).forEach(function (chartId) {
      const chartConfig = drupalSettings.f_organisation_chart[chartId];
      if (chartConfig && chartConfig.data) initOrgChart(chartId, chartConfig.data);
    });
  }

  // ===== Drupal attach =====
  Drupal.behaviors.organisationChart = {
    attach: function () {
      if (typeof OrgChart !== 'undefined') initAllCharts();
      else console.error('OrgChart library not loaded');
    }
  };

})(Drupal, drupalSettings);