
#org-chart-organigrame-organisation_chart .img {
    width: 100px;
    height: 100px;
}
  #org-chart-organigrame-organisation_chart img {
    position: absolute;
    width: 80px;
    height: 80px;
}
#org-chart-organigrame-organisation_chart, 
#org-chart-marine-organisation_chart {
    --org-color-title: #3c77ce;
    --org-color-name: #2e2e2e;
    --org-border-color: #75abff;
    --org-bg-color: #fafafa;
    background-color: transparent;
    width: 100%;
    /* height: 100%; */
    /* height: auto; */
    /* margin: 0 auto 60px; */
}
#org-chart-organigrame-organisation_chart .link path,
#org-chart-marine-organisation_chart .link path{
    stroke: var(--org-border-color);
}
#org-chart-organigrame-organisation_chart .boc-fill,
#org-chart-marine-organisation_chart .boc-fill{
    stroke: var(--org-border-color);
    fill: var(--org-border-color);
}

#org-chart-organigrame-organisation_chart .boc-fill ~ line {
    stroke: #ffffff;
    stroke-width: 2;
}

#org-chart-organigrame-organisation_chart .node,
#org-chart-marine-organisation_chart .node{
    background-color: #fafafa !important;
    border: 1px solid #ccc;
}

         
#org-chart-organigrame-organisation_chart .organigram-name,
#org-chart-marine-organisation_chart .organigram-name{
    font-size: 16px;
    color: var(--org-color-name);
    text-align: center;
}
#org-chart-organigrame-organisation_chart .boc-search,
#org-chart-marine-organisation_chart .boc-search, 
#org-chart-organigramme_dtr-organisation_chart .boc-search,
#org-chart-organigramme_dgac-organisation_chart .boc-search,
#org-chart-organigramme_dmm-organisation_chart .boc-search,
#org-chart-organigramme_dspct-organisation_chart .boc-search,
#org-chart-organigramme_de_la_daajg-organisation_chart .boc-search,
#org-chart-organigramme_dsi-organisation_chart .boc-search{
    display: none !important;
}

/* Custom style card */
#org-chart-organigrame-organisation_chart [data-n-id="99"] rect {
    fill: #042246;
}
#org-chart-organigrame-organisation_chart [data-n-id="99"] {
    transform: matrix(1,0,0,1,300,20);
}
#org-chart-organigrame-organisation_chart [data-ctrl-ec-id="99"] {
    transform:matrix(1,0,0,1,425,152);
}
        

/* partner firt level */
#org-chart-organigrame-organisation_chart [data-n-id="98"] rect {
    fill: transparent;
}

/* Ahmed */
#org-chart-organigrame-organisation_chart .organigram-name,
#org-chart-organigrame-organisation_chart .organigram-title{
    font-size: 16px;
    text-align: center;
    line-height: 20px;
    font-weight: 700;
}
#org-chart-organigrame-organisation_chart [data-n-id="99"] .organigram-name,
#org-chart-organigrame-organisation_chart [data-n-id="99"] .organigram-title{
    color: white;
}


/* 2eme org */
.boc-light#org-chart-marine-organisation_char .organigram-name{
    font-size: 16px;
    color: var(--org-color-name);
    text-align: center;
}
.boc-light circle ~ line{
    stroke: #ffffff !important;
    stroke-width: 2 !important;
}

/* Deuxième niveau et suivants : couleurs plus douces */
[id^="org-chart-"] g.node[data-l="2"] rect {
  fill: #fff;
  stroke: #fff;
}
[id^="org-chart-"] g.node[data-l="2"] .organigram-title,
[id^="org-chart-"] g.node[data-l="2"] .organigram-name {
  color: #3c77ce;
}

[id^="org-chart-"] g.node[data-tags~="pivot"] rect {
  fill: var(--c-primary-dark);
}
[id^="org-chart-"] g.node[data-tags~="pivot"] .organigram-name,
[id^="org-chart-"] g.node[data-tags~="pivot"] .organigram-title {
  color: #fff;
}

/* Centrer le champ de recherche et son contenu */
[id^="org-chart-"] .boc-search {
  display: none !important;
}

/* Centrer le texte dans les titres et les noms des cartes */
[id^="org-chart-"] .organigram-title,
[id^="org-chart-"] .organigram-name {
  text-align: center !important;
}
/* Couleurs globales (si variables CSS disponibles) */
:root {
  --accordion-bg: #75abff;  /* couleur du cercle (bleu moyen) */
  --accordion-fg: #ffffff;  /* couleur du trait (blanc) */
}

/* Appliquer le même style à tous les boutons d’extension/réduction */
[id^="org-chart-"] g[data-ctrl-ec-id] circle {
  fill: var(--accordion-bg) !important;
  stroke: var(--accordion-bg) !important;
}
[id^="org-chart-"] g[data-ctrl-ec-id] line {
  stroke: var(--accordion-fg) !important;
  stroke-width: 2 !important;
}

/* Compatibilité : si l’implémentation utilise .boc-fill plutôt qu’un <circle> */
[id^="org-chart-"] .boc-fill {
  fill: var(--accordion-bg) !important;
  stroke: var(--accordion-bg) !important;
}
[id^="org-chart-"] .boc-fill ~ line,
[id^="org-chart-"] .boc-light circle ~ line {
  stroke: var(--accordion-fg) !important;
  stroke-width: 2 !important;
}

/* Colorer les nœuds de niveau 2 et plus en bleu clair quand le fond général est blanc */
[id^="org-chart-"] g.node[data-l="2"] rect,
[id^="org-chart-"] g.node[data-l="3"] rect,
[id^="org-chart-"] g.node[data-l="4"] rect,
[id^="org-chart-"] g.node[data-l="5"] rect {
    fill: #e2edfa !important;
    stroke: #e2edfa !important;
}

/* Conserver une couleur de texte contrastée pour ces nœuds */
[id^="org-chart-"] g.node[data-l="2"] .organigram-title,
[id^="org-chart-"] g.node[data-l="2"] .organigram-name,
[id^="org-chart-"] g.node[data-l="3"] .organigram-title,
[id^="org-chart-"] g.node[data-l="3"] .organigram-name,
[id^="org-chart-"] g.node[data-l="4"] .organigram-title,
[id^="org-chart-"] g.node[data-l="4"] .organigram-name,
[id^="org-chart-"] g.node[data-l="5"] .organigram-title,
[id^="org-chart-"] g.node[data-l="5"] .organigram-name {
    color: var(--c-primary); /* ou une teinte bleue comme #3c77ce */
}
[id^="org-chart-"] .organigram-title,
[id^="org-chart-"] .organigram-name {
  padding-left: 10px;  /* ou 1rem selon tes besoins */
  padding-right: 10px;
  /* on peut aussi utiliser une seule ligne */
  /* padding: 0 10px; */
}
[id^="org-chart-"] .organigram-title,
[id^="org-chart-"] .organigram-name {
  font-size: 18px;
  font-weight: bold;
  
}
.org-chart-wrapper {
  width: 100%;
  overflow-x: auto;
}
#org-chart-dtr {
  width: max-content;
  min-width: 100%;
}

.organisation-chart-container{
  border-radius:inherit;
  background-clip:padding-box;
  padding:12px;
  width:100%;
  height: 650px !important;
  overflow-y:auto !important;
  overflow-x:auto !important;
  scrollbar-width:thin;
  scrollbar-color:#75abff #f1f1f1;
  &#org-chart-organigrame-organisation_chart {
    height: 900px !important;
    padding: 0;
    overflow-y:hidden !important;
  }
  &#org-chart-organigramme_dspct-organisation_chart {
    overflow: hidden;
    position: relative;
    height: 700px;
  }
  &#org-chart-organigramme_dgac-organisation_chart {
    height: 900px !important;
  }
}


/* Chrome/Safari/Edge */
/* WebKit */
.organisation-chart-container::-webkit-scrollbar{ width:8px; height:8px; }
.organisation-chart-container::-webkit-scrollbar-track{ background:#f1f1f1; border-radius:8px; }
.organisation-chart-container::-webkit-scrollbar-thumb{ background:#75abff; border-radius:8px; }
.organisation-chart-container svg {
    padding-bottom: 30px;
} 


[id^="org-chart-"] g.node {
  cursor: pointer;
}
[id^="org-chart-"] g.node[data-l="0"] rect {
  fill: #042246 !important;
  stroke: #75abff !important;
}

[id^="org-chart-"] g.node[data-l="0"] rect {
  fill: #042246 !important;
  stroke: transparent !important;
}
[id^="org-chart-"] g.node[data-l="1"] rect {
  fill: #004ca5 !important;
  stroke: #75abff !important;
}
[id^="org-chart-"] g.node[data-l="2"] rect {
  fill: #e2edfa !important;
  stroke: transparent !important;
}
[id^="org-chart-"] g.node[data-l="3"] rect {
  fill: #ffffff !important;
  stroke: #75abff !important;
}
/* Color de texte */
[id^="org-chart-"] g.node[data-l="0"] .organigram-title,
[id^="org-chart-"] g.node[data-l="0"] .organigram-name {
  color: #ffffff !important;
}
[id^="org-chart-"] g.node[data-l="1"] .organigram-title,
[id^="org-chart-"] g.node[data-l="1"] .organigram-name {
  color: #ffffff !important;
}
[id^="org-chart-"] g.node[data-l="2"] .organigram-title,
[id^="org-chart-"] g.node[data-l="2"] .organigram-name {
  color: #3c77d6 !important;
}
[id^="org-chart-"] g.node[data-l="3"] .organigram-title,
[id^="org-chart-"] g.node[data-l="3"] .organigram-name {
  color: #3c77d6 !important;
}

[id^="org-chart-"] g.node[data-n-id="100"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="100"] .organigram-name {
  color: #fff !important;
}

[id^="org-chart-"] g.node[data-n-id="139"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="139"] .organigram-name, 
[id^="org-chart-"] g.node[data-n-id="140"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="140"] .organigram-name  {
  color: #3C77CE !important;
}

[id^="org-chart-"] g.node[data-n-id="135"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="135"] .organigram-name,
[id^="org-chart-"] g.node[data-n-id="136"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="136"] .organigram-name,
[id^="org-chart-"] g.node[data-n-id="137"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="137"] .organigram-name,
[id^="org-chart-"] g.node[data-n-id="138"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="138"] .organigram-name,
[id^="org-chart-"] g.node[data-n-id="196"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="196"] .organigram-name ,
[id^="org-chart-"] g.node[data-n-id="197"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="197"] .organigram-name,
[id^="org-chart-"] g.node[data-n-id="242"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="242"] .organigram-name,
[id^="org-chart-"] g.node[data-n-id="243"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="243"] .organigram-name, 
[id^="org-chart-"] g.node[data-n-id="199"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="199"] .organigram-name, 
[id^="org-chart-"] g.node[data-n-id="171"] .organigram-title,
[id^="org-chart-"] g.node[data-n-id="171"] .organigram-name    {
  color: #3C77CE !important;
}





.org-card-shell {
    display: block !important;
    direction: ltr;
    div:not(.org-zoom-box) {
        display: block !important;
    }
}

.h2-title + .container {
    display: block !important;
}
.accord-wysiw  .container {
    display: block !important;
}

#org-chart-organigrame-organisation_chart .voir-plus-link {
  position: relative;
  color: #3C77CE !important;
  font-size: 16px !important;
  transition: all 450ms ease;
}

#org-chart-organigrame-organisation_chart .voir-plus-link:hover {
  color: #042246 !important;
  text-decoration: none !important;
}
/* Boîte des boutons zoom */
.org-card-shell {
  position: relative;
  background: #fff;
  border-radius: 15px;
  padding: 12px;
  align-items: center;
}
.org-zoom-box {
  display: flex !important;
  text-align: center;
  gap: 5px;
  justify-content: center;
  padding-top: 12px;
 font-size: 0;
}
html[dir="rtl"] .org-zoom-box {
  direction: rtl;
}
html[dir="rtl"] .org-zoom-btn::after {
  top: 45%;
  font-size: 24px;
}
html[dir="rtl"] .org-zoom-badge {
    font-size: 18px;
}
 /* html[dir="rtl"] .org-zoom-box {
  left: 16px;
  right: auto;
 } */
/* Boutons + / − */
.org-zoom-btn {
  position: relative;
  display: flex;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 100%;
  border: none;
  background: #3C77CE;
  font-size: 0;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,.15);
  transition: background 0.2s, transform 0.1s;
}
.org-zoom-out::after {
  content: "-";
}
.org-zoom-in::after {
  content: "+";
}
.org-zoom-btn::after {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}
.org-zoom-btn:hover {
  background: #042246;
}
.org-zoom-btn:active {
  transform: scale(1);
}
.org-zoom-badge {
    padding-top: 0;
    width: 60px;
    align-self: center;
    font-size: 16px;
    font-weight: 500;
}
