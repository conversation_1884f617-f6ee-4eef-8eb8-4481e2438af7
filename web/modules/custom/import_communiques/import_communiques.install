<?php

/**
 * @file
 * Installation et mise à jour du module import_communiques.
 */

use Drupal\field\Entity\FieldConfig;
use Drupal\field\Entity\FieldStorageConfig;
use Drupal\node\Entity\NodeType;

/**
 * Implements hook_install().
 */
function import_communiques_install() {
  // Vérifier si le type de contenu existe
  $node_type = NodeType::load('communiques_de_presse');
  if (!$node_type) {
    \Drupal::messenger()->addWarning(t('Le type de contenu "communiques_de_presse" n\'existe pas. Veuillez le créer manuellement.'));
    return;
  }

  // Créer le champ field_external_id s'il n'existe pas
  _import_communiques_create_external_id_field();
  
  \Drupal::messenger()->addStatus(t('Module Import Communiqués installé avec succès.'));
}

/**
 * C<PERSON>e le champ field_external_id pour éviter les doublons.
 */
function _import_communiques_create_external_id_field() {
  // Vérifier si le stockage du champ existe déjà
  $field_storage = FieldStorageConfig::loadByName('node', 'field_external_id');
  if (!$field_storage) {
    // Créer le stockage du champ
    $field_storage = FieldStorageConfig::create([
      'field_name' => 'field_external_id',
      'entity_type' => 'node',
      'type' => 'string',
      'settings' => [
        'max_length' => 255,
        'case_sensitive' => FALSE,
        'is_ascii' => FALSE,
      ],
      'cardinality' => 1,
      'translatable' => FALSE,
    ]);
    $field_storage->save();
  }

  // Vérifier si le champ existe pour le type de contenu
  $field = FieldConfig::loadByName('node', 'communiques_de_presse', 'field_external_id');
  if (!$field) {
    // Créer le champ pour le type de contenu
    $field = FieldConfig::create([
      'field_storage' => $field_storage,
      'bundle' => 'communiques_de_presse',
      'label' => 'ID Externe',
      'description' => 'Identifiant externe utilisé pour éviter les doublons lors de l\'importation.',
      'required' => FALSE,
      'translatable' => FALSE,
      'settings' => [],
    ]);
    $field->save();

    // Configurer l'affichage du formulaire
    try {
      $form_display = \Drupal::entityTypeManager()
        ->getStorage('entity_form_display')
        ->load('node.communiques_de_presse.default');

      if ($form_display) {
        $form_display->setComponent('field_external_id', [
          'type' => 'string_textfield',
          'weight' => 10,
          'settings' => [
            'size' => 60,
            'placeholder' => '',
          ],
        ])->save();
      }

      // Masquer le champ dans l'affichage par défaut
      $view_display = \Drupal::entityTypeManager()
        ->getStorage('entity_view_display')
        ->load('node.communiques_de_presse.default');

      if ($view_display) {
        $view_display->removeComponent('field_external_id')->save();
      }
    }
    catch (\Exception $e) {
      \Drupal::logger('import_communiques')->warning('Erreur lors de la configuration des affichages: @error', ['@error' => $e->getMessage()]);
    }
  }
}

/**
 * Implements hook_uninstall().
 */
function import_communiques_uninstall() {
  // Optionnel : supprimer le champ field_external_id
  // Décommentez si vous voulez supprimer le champ lors de la désinstallation
  /*
  $field = FieldConfig::loadByName('node', 'communiques_de_presse', 'field_external_id');
  if ($field) {
    $field->delete();
  }
  
  $field_storage = FieldStorageConfig::loadByName('node', 'field_external_id');
  if ($field_storage) {
    $field_storage->delete();
  }
  */
  
  \Drupal::messenger()->addStatus(t('Module Import Communiqués désinstallé.'));
}
