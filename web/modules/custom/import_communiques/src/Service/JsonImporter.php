<?php

namespace Drupal\import_communiques\Service;

use Dr<PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON>upal\Core\Logger\LoggerChannelFactoryInterface;
use <PERSON><PERSON>al\Core\Language\LanguageManagerInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;

/**
 * Service pour importer des communiqués de presse depuis un fichier JSON.
 */
class JsonImporter {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * The language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  protected $languageManager;

  /**
   * Constructs a JsonImporter object.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system,
    LoggerChannelFactoryInterface $logger_factory,
    LanguageManagerInterface $language_manager
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
    $this->loggerFactory = $logger_factory;
    $this->languageManager = $language_manager;
  }

  /**
   * Importe les communiqués depuis un fichier JSON.
   *
   * @param string $file_path
   *   Le chemin vers le fichier JSON.
   *
   * @return array
   *   Résultats de l'importation.
   */
  public function import($file_path) {
    $results = [
      'success' => FALSE,
      'created' => 0,
      'updated' => 0,
      'errors' => [],
      'processed' => 0,
    ];

    try {
      // Lire le fichier JSON
      $json_content = file_get_contents($file_path);
      if ($json_content === FALSE) {
        $results['errors'][] = 'Impossible de lire le fichier JSON.';
        return $results;
      }

      // Décoder le JSON
      $data = json_decode($json_content, TRUE);
      if ($data === NULL) {
        $results['errors'][] = 'Fichier JSON invalide.';
        return $results;
      }

      // Détecter le format du JSON
      if (isset($data['content']['tables'][0]['rows'])) {
        // Nouveau format avec structure "rows"
        $items = $this->parseRowsContent($data['content']['tables'][0]['rows']);
      }
      elseif (isset($data['content']) && isset($data['content']['html'])) {
        // Format HTML (fallback)
        $items = $this->parseHtmlContent($data['content']['html']);
      }
      elseif (is_array($data) && isset($data[0]['id'])) {
        // Ancien format avec tableau d'objets
        $items = $data;
      }
      else {
        $results['errors'][] = 'Format JSON non reconnu.';
        return $results;
      }

      // Traiter chaque communiqué
      foreach ($items as $item) {
        $results['processed']++;

        try {
          $this->processItem($item, $results);
        }
        catch (\Exception $e) {
          $results['errors'][] = 'Erreur lors du traitement de l\'item ID ' . ($item['id'] ?? 'inconnu') . ': ' . $e->getMessage();
        }
      }

      $results['success'] = TRUE;
    }
    catch (\Exception $e) {
      $results['errors'][] = 'Erreur générale: ' . $e->getMessage();
    }

    return $results;
  }

  /**
   * Parse le contenu "rows" pour extraire les communiqués.
   *
   * @param array $rows
   *   Le tableau des lignes à parser.
   *
   * @return array
   *   Tableau des communiqués extraits.
   */
  protected function parseRowsContent($rows) {
    $items = [];
    $seen_ids = []; // Pour éviter les doublons

    // Ignorer les 2 premières lignes (en-tête)
    for ($i = 2; $i < count($rows); $i++) {
      $row = $rows[$i];

      // Vérifier qu'on a au moins 6 colonnes
      if (count($row) < 6) {
        continue;
      }

      $date = trim($row[0] ?? '');
      $titre_ar = trim($row[1] ?? '');
      $titre_fr = trim($row[2] ?? '');
      $identifiant = trim($row[3] ?? '');

      // Les secteurs peuvent être des chaînes ou des tableaux
      $secteurs_ar = $this->processSecteurField($row[4] ?? '');
      $secteurs_fr = $this->processSecteurField($row[5] ?? '');

      // Ignorer les lignes vides ou sans titre français
      if (empty($titre_fr) || $titre_fr === 'Titre FR') {
        continue;
      }

      // Générer un ID unique si manquant
      if (empty($identifiant)) {
        $identifiant = 'auto_' . ($i - 1);
      }

      // Éviter les doublons d'ID
      if (isset($seen_ids[$identifiant])) {
        $this->loggerFactory->get('import_communiques')->warning('ID dupliqué ignoré: @id', ['@id' => $identifiant]);
        continue;
      }
      $seen_ids[$identifiant] = true;

      // Créer l'item
      $item = [
        'id' => $identifiant,
        'date' => $date,
        'titre' => $titre_fr,
        'titre_ar' => $titre_ar,
        'secteurs' => $secteurs_fr,
        'secteurs_ar' => $secteurs_ar,
      ];

      $items[] = $item;
    }

    return $items;
  }

  /**
   * Traite un champ secteur qui peut être une chaîne ou un tableau.
   */
  protected function processSecteurField($field) {
    if (is_array($field)) {
      // Si c'est un tableau, joindre les éléments avec des points-virgules
      return implode('; ', array_filter($field));
    }
    else {
      // Si c'est une chaîne, essayer de séparer les secteurs concaténés
      $field = trim($field);

      // Liste des secteurs connus pour la séparation
      $secteurs_connus = [
        'Transport routier', 'Transport aérien', 'Transport Aérien', 'Transport ferroviaire',
        'Marine marchande', 'Logistique', 'Sécurité routière', 'Transport durable',
        'Gouvernance', 'Coopération',
        'النقل الطرقي', 'النقل الجوي', 'النقل السككي', 'النقل البحري',
        'اللوجيستيك', 'السلامة الطرقية', 'التنقل المستدام', 'الحكامة',
        'التعاون', 'الحوار القطاعي'
      ];

      // Trier par longueur décroissante pour éviter les correspondances partielles
      usort($secteurs_connus, function($a, $b) {
        return strlen($b) - strlen($a);
      });

      $secteurs_trouves = [];
      $remaining = $field;

      foreach ($secteurs_connus as $secteur) {
        if (strpos($remaining, $secteur) !== false) {
          $secteurs_trouves[] = $secteur;
          $remaining = str_replace($secteur, '', $remaining);
        }
      }

      if (!empty($secteurs_trouves)) {
        return implode('; ', $secteurs_trouves);
      }

      return $field;
    }
  }

  /**
   * Parse le contenu HTML pour extraire les communiqués.
   *
   * @param string $html_content
   *   Le contenu HTML à parser.
   *
   * @return array
   *   Tableau des communiqués extraits.
   */
  protected function parseHtmlContent($html_content) {
    $items = [];

    // Utiliser DOMDocument pour parser le HTML
    $dom = new \DOMDocument();
    libxml_use_internal_errors(TRUE);
    $dom->loadHTML('<?xml encoding="UTF-8">' . $html_content);
    libxml_clear_errors();

    $xpath = new \DOMXPath($dom);
    $rows = $xpath->query('//tr');

    $id_counter = 1;

    foreach ($rows as $row) {
      $cells = $xpath->query('.//td', $row);

      // Ignorer les lignes d'en-tête ou avec moins de 6 colonnes
      if ($cells->length < 6) {
        continue;
      }

      // Extraire les données des cellules
      $date = trim($cells->item(0)->textContent ?? '');
      $titre_ar = trim($cells->item(1)->textContent ?? '');
      $titre_fr = trim($cells->item(2)->textContent ?? '');
      $identifiant = trim($cells->item(3)->textContent ?? '');
      $secteurs_ar = $this->extractListItems($cells->item(4));
      $secteurs_fr = $this->extractListItems($cells->item(5));

      // Ignorer les lignes vides ou d'en-tête
      if (empty($date) || $date === 'Date' || empty($titre_fr)) {
        continue;
      }

      // Créer l'item
      $item = [
        'id' => !empty($identifiant) ? $identifiant : $id_counter++,
        'date' => $date,
        'titre' => $titre_fr,
        'titre_ar' => $titre_ar,
        'secteurs' => implode('; ', $secteurs_fr),
        'secteurs_ar' => implode('; ', $secteurs_ar),
      ];

      $items[] = $item;
    }

    return $items;
  }

  /**
   * Extrait les éléments de liste d'une cellule.
   */
  protected function extractListItems($cell) {
    if (!$cell) {
      return [];
    }

    $xpath = new \DOMXPath($cell->ownerDocument);
    $list_items = $xpath->query('.//li', $cell);

    $items = [];
    foreach ($list_items as $li) {
      $text = trim($li->textContent);
      if (!empty($text)) {
        $items[] = $text;
      }
    }

    // Si pas de liste, prendre le texte direct
    if (empty($items)) {
      $text = trim($cell->textContent);
      if (!empty($text)) {
        $items[] = $text;
      }
    }

    return $items;
  }

  /**
   * Traite un élément du JSON.
   */
  protected function processItem($item, &$results) {
    // Vérifier si le communiqué existe déjà
    $existing_node = $this->findExistingNode($item['id']);
    
    if ($existing_node) {
      // Mise à jour
      $node = $existing_node;
      $results['updated']++;
    }
    else {
      // Création
      $node = Node::create(['type' => 'communiques_de_presse']);
      $results['created']++;
    }

    // Mapper les champs
    $this->mapFields($node, $item);
    
    // Sauvegarder le nœud
    $node->save();
  }

  /**
   * Cherche un nœud existant par ID externe.
   */
  protected function findExistingNode($external_id) {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'communiques_de_presse')
      ->condition('field_external_id', $external_id)
      ->range(0, 1);

    $nids = $query->execute();
    
    if (!empty($nids)) {
      return Node::load(reset($nids));
    }
    
    return NULL;
  }

  /**
   * Mappe les champs du JSON vers les champs Drupal.
   */
  protected function mapFields($node, $item) {
    // Titre français
    $titre = !empty($item['titre']) && $item['titre'] !== 'Sans titre' && $item['titre'] !== '???'
      ? $item['titre']
      : 'Communiqué de presse #' . $item['id'];
    $node->setTitle($titre);

    // ID externe pour éviter les doublons
    if (isset($item['id'])) {
      $node->set('field_external_id', $item['id']);
    }

    // Date
    if (!empty($item['date']) && $item['date'] !== 'Non spécifiée') {
      $date = $this->parseDate($item['date']);
      if ($date) {
        $node->set('field_date', $date);
      }
    }

    // Secteurs français
    if (!empty($item['secteurs']) && $item['secteurs'] !== 'Non spécifié') {
      $secteur_terms = $this->processSecteurs($item['secteurs']);
      if (!empty($secteur_terms)) {
        $node->set('field_secteur', $secteur_terms);
      }
    }

    // Langue par défaut (français)
    $node->set('langcode', 'fr');
    $node->set('status', 1); // Publié

    // Sauvegarder le nœud français d'abord
    $node->save();

    // Créer la traduction arabe si disponible
    if (!empty($item['titre_ar']) && $item['titre_ar'] !== $item['titre']) {
      $this->createArabicTranslation($node, $item);
    }
  }

  /**
   * Crée la traduction arabe du nœud.
   */
  protected function createArabicTranslation($node, $item) {
    try {
      // Vérifier si la traduction arabe existe déjà
      if ($node->hasTranslation('ar')) {
        $translation = $node->getTranslation('ar');
      }
      else {
        $translation = $node->addTranslation('ar');
      }

      // Titre arabe
      $translation->setTitle($item['titre_ar']);

      // Secteurs arabes (si différents)
      if (!empty($item['secteurs_ar']) && $item['secteurs_ar'] !== $item['secteurs']) {
        $secteur_terms_ar = $this->processSecteurs($item['secteurs_ar']);
        if (!empty($secteur_terms_ar)) {
          $translation->set('field_secteur', $secteur_terms_ar);
        }
      }

      // Même date et ID externe
      if ($node->hasField('field_date') && !$node->get('field_date')->isEmpty()) {
        $translation->set('field_date', $node->get('field_date')->value);
      }
      if ($node->hasField('field_external_id') && !$node->get('field_external_id')->isEmpty()) {
        $translation->set('field_external_id', $node->get('field_external_id')->value);
      }

      $translation->set('status', 1);
      $translation->save();
    }
    catch (\Exception $e) {
      $this->loggerFactory->get('import_communiques')->warning('Erreur lors de la création de la traduction arabe: @error', ['@error' => $e->getMessage()]);
    }
  }

  /**
   * Parse une date depuis le format du JSON.
   */
  protected function parseDate($date_string) {
    try {
      // Format attendu: dd/mm/yyyy
      if (preg_match('/^(\d{2})\/(\d{2})\/(\d{4})$/', $date_string, $matches)) {
        $day = $matches[1];
        $month = $matches[2];
        $year = $matches[3];
        return $year . '-' . $month . '-' . $day;
      }
    }
    catch (\Exception) {
      // Ignorer les erreurs de date
    }
    
    return NULL;
  }

  /**
   * Traite les secteurs et retourne les IDs des termes de taxonomie.
   */
  protected function processSecteurs($secteurs_string) {
    $secteur_ids = [];
    
    // Séparer les secteurs par point-virgule
    $secteurs = array_map('trim', explode(';', $secteurs_string));
    
    foreach ($secteurs as $secteur_name) {
      if (!empty($secteur_name)) {
        $term_id = $this->findOrCreateSecteurTerm($secteur_name);
        if ($term_id) {
          $secteur_ids[] = ['target_id' => $term_id];
        }
      }
    }
    
    return $secteur_ids;
  }

  /**
   * Trouve un terme de taxonomie existant pour un secteur.
   */
  protected function findOrCreateSecteurTerm($secteur_name) {
    // Chercher le terme existant
    $query = $this->entityTypeManager->getStorage('taxonomy_term')->getQuery()
      ->accessCheck(FALSE)
      ->condition('vid', 'modes_de_transport')
      ->condition('name', $secteur_name)
      ->range(0, 1);

    $tids = $query->execute();

    if (!empty($tids)) {
      return reset($tids);
    }

    // Ne pas créer de nouveau terme, retourner NULL
    $this->loggerFactory->get('import_communiques')->info('Terme de secteur non trouvé, ignoré: @secteur', ['@secteur' => $secteur_name]);
    return NULL;
  }

}
