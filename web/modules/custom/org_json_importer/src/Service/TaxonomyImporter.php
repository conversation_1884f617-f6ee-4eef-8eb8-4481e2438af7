<?php

namespace Drupal\org_json_importer\Service;

use <PERSON><PERSON>al\taxonomy\Entity\Vocabulary;
use Dr<PERSON>al\taxonomy\Entity\Term;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\StringTranslation\StringTranslationTrait;

/**
 * Service for importing organizational data into taxonomy terms.
 */
class TaxonomyImporter {

  use StringTranslationTrait;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Constructs a TaxonomyImporter object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager) {
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * Create a new vocabulary.
   *
   * @param string $name
   *   The vocabulary name.
   * @param string $description
   *   Optional vocabulary description.
   *
   * @return string
   *   The vocabulary ID.
   */
  public function createVocabulary($name, $description = '') {
    // Create machine name from human name
    $vid = strtolower(preg_replace('/[^a-zA-Z0-9_]/', '_', $name));
    $vid = preg_replace('/_+/', '_', $vid);
    $vid = trim($vid, '_');

    // Ensure unique vocabulary ID
    $original_vid = $vid;
    $counter = 1;
    while (Vocabulary::load($vid)) {
      $vid = $original_vid . '_' . $counter;
      $counter++;
    }

    $vocabulary = Vocabulary::create([
      'vid' => $vid,
      'name' => $name,
      'description' => $description ?: $this->t('Imported organizational hierarchy'),
    ]);

    $vocabulary->save();

    return $vid;
  }

  /**
   * Clear all terms from a vocabulary.
   *
   * @param string $vocabulary_id
   *   The vocabulary ID.
   */
  public function clearVocabularyTerms($vocabulary_id) {
    $term_storage = $this->entityTypeManager->getStorage('taxonomy_term');
    
    $terms = $term_storage->loadByProperties(['vid' => $vocabulary_id]);
    
    if (!empty($terms)) {
      $term_storage->delete($terms);
    }
  }

  /**
   * Import organizational data as taxonomy terms with hierarchy.
   *
   * @param array $organizational_data
   *   Parsed organizational data.
   * @param string $vocabulary_id
   *   Target vocabulary ID.
   *
   * @return int
   *   Number of terms imported.
   */
  public function importOrganizationalData(array $organizational_data, $vocabulary_id) {
    $imported_count = 0;
    $term_map = []; // Map to track created terms for hierarchy

    // Import root level items
    if (isset($organizational_data['root_items'])) {
      foreach ($organizational_data['root_items'] as $item) {
        $term_id = $this->createTerm($item, $vocabulary_id);
        if ($term_id) {
          $term_map[$this->getItemKey($item)] = $term_id;
          $imported_count++;

          // Import children (services within divisions)
          if (!empty($item['children'])) {
            foreach ($item['children'] as $child) {
              $child_term_id = $this->createTerm($child, $vocabulary_id, $term_id);
              if ($child_term_id) {
                $imported_count++;
                $term_map[$this->getItemKey($child)] = $child_term_id;
              }
            }
          }
        }
      }
    }

    return $imported_count;
  }

  /**
   * Create a taxonomy term from organizational item data.
   *
   * @param array $item
   *   Organizational item data.
   * @param string $vocabulary_id
   *   Target vocabulary ID.
   * @param int|null $parent_id
   *   Parent term ID for hierarchy.
   *
   * @return int|null
   *   Created term ID or NULL on failure.
   */
  protected function createTerm(array $item, $vocabulary_id, $parent_id = NULL) {
    if (empty($item['name'])) {
      return NULL;
    }

    $term_data = [
      'vid' => $vocabulary_id,
      'name' => $item['name'],
    ];

    // Add parent if specified
    if ($parent_id) {
      $term_data['parent'] = [$parent_id];
    }

    // Create description from available data
    $description_parts = [];
    
    if (!empty($item['type'])) {
      $type_labels = [
        'director' => $this->t('Director'),
        'quality_service' => $this->t('Quality Service'),
        'division' => $this->t('Division'),
        'service' => $this->t('Service'),
      ];
      $description_parts[] = $type_labels[$item['type']] ?? ucfirst($item['type']);
    }

    if (!empty($item['position'])) {
      $description_parts[] = $this->t('Position: @position', ['@position' => $item['position']]);
    }

    if (!empty($item['responsible'])) {
      $description_parts[] = $this->t('Responsible: @responsible', ['@responsible' => $item['responsible']]);
    }

    if (!empty($description_parts)) {
      $term_data['description'] = [
        'value' => implode('<br>', $description_parts),
        'format' => 'basic_html',
      ];
    }

    try {
      $term = Term::create($term_data);
      $term->save();
      
      return $term->id();
    } catch (\Exception $e) {
      \Drupal::logger('org_json_importer')->error('Failed to create term: @error', ['@error' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Generate a unique key for an organizational item.
   *
   * @param array $item
   *   Organizational item data.
   *
   * @return string
   *   Unique key for the item.
   */
  protected function getItemKey(array $item) {
    return md5($item['name'] . '_' . ($item['type'] ?? ''));
  }

  /**
   * Check if vocabulary exists.
   *
   * @param string $vocabulary_id
   *   Vocabulary ID to check.
   *
   * @return bool
   *   TRUE if vocabulary exists.
   */
  public function vocabularyExists($vocabulary_id) {
    return (bool) Vocabulary::load($vocabulary_id);
  }

  /**
   * Get term count for vocabulary.
   *
   * @param string $vocabulary_id
   *   Vocabulary ID.
   *
   * @return int
   *   Number of terms in vocabulary.
   */
  public function getVocabularyTermCount($vocabulary_id) {
    $term_storage = $this->entityTypeManager->getStorage('taxonomy_term');
    
    $query = $term_storage->getQuery()
      ->condition('vid', $vocabulary_id)
      ->accessCheck(FALSE);
    
    return $query->count()->execute();
  }

}