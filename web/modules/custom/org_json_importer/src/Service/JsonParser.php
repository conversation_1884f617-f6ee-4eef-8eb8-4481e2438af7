<?php

namespace Drupal\org_json_importer\Service;

/**
 * Service for parsing organizational JSON data.
 */
class JsonParser {

  /**
   * Parse JSON organizational data into structured array.
   *
   * @param string $json_content
   *   The JSON content to parse.
   *
   * @return array
   *   Parsed organizational data structure.
   *
   * @throws \Exception
   *   If JSON is invalid or structure is unexpected.
   */
  public function parseJsonData($json_content) {
    $data = json_decode($json_content, TRUE);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \Exception('Invalid JSON: ' . json_last_error_msg());
    }

    $parsed_data = [
      'root_items' => [],
      'hierarchy' => [],
    ];

    // Parse director (directeur)
    if (isset($data['directeur'])) {
      $director = $this->parseDirector($data['directeur']);
      $parsed_data['root_items'][] = $director;
    }

    // Parse quality service (service_qualite)  
    if (isset($data['service_qualite'])) {
      $quality_service = $this->parseQualityService($data['service_qualite']);
      $parsed_data['root_items'][] = $quality_service;
    }

    // Parse divisions
    if (isset($data['divisions']) && is_array($data['divisions'])) {
      foreach ($data['divisions'] as $division_data) {
        $division = $this->parseDivision($division_data);
        $parsed_data['root_items'][] = $division;
      }
    }

    return $parsed_data;
  }

  /**
   * Parse director data.
   *
   * @param array $director_data
   *   Director data from JSON.
   *
   * @return array
   *   Parsed director structure.
   */
  protected function parseDirector($director_data) {
    return [
      'type' => 'director',
      'name' => $director_data['nom'] ?? '',
      'position' => $director_data['poste'] ?? '',
      'responsible' => null,
      'parent_id' => null,
      'children' => [],
    ];
  }

  /**
   * Parse quality service data.
   *
   * @param array $quality_data
   *   Quality service data from JSON.
   *
   * @return array
   *   Parsed quality service structure.
   */
  protected function parseQualityService($quality_data) {
    return [
      'type' => 'quality_service',
      'name' => $quality_data['nom'] ?? '',
      'position' => null,
      'responsible' => null,
      'parent_id' => null,
      'children' => [],
    ];
  }

  /**
   * Parse division data with services.
   *
   * @param array $division_data
   *   Division data from JSON.
   *
   * @return array
   *   Parsed division structure with services.
   */
  protected function parseDivision($division_data) {
    $division = [
      'type' => 'division',
      'name' => $division_data['nom'] ?? '',
      'position' => null,
      'responsible' => $division_data['responsable'] ?? null,
      'parent_id' => null,
      'children' => [],
    ];

    // Parse services within division
    if (isset($division_data['services']) && is_array($division_data['services'])) {
      foreach ($division_data['services'] as $service_data) {
        $service = [
          'type' => 'service',
          'name' => $service_data['nom'] ?? '',
          'position' => null,
          'responsible' => $service_data['responsable'] ?? null,
          'parent_id' => null, // Will be set during import
          'children' => [],
        ];
        $division['children'][] = $service;
      }
    }

    return $division;
  }

  /**
   * Validate that the JSON structure contains expected organizational data.
   *
   * @param array $data
   *   Parsed JSON data.
   *
   * @return bool
   *   TRUE if structure is valid.
   *
   * @throws \Exception
   *   If structure is invalid.
   */
  public function validateStructure(array $data) {
    $required_fields = ['directeur', 'divisions'];
    
    foreach ($required_fields as $field) {
      if (!isset($data[$field])) {
        throw new \Exception("Missing required field: {$field}");
      }
    }

    // Validate director structure
    if (!isset($data['directeur']['nom'])) {
      throw new \Exception("Director must have 'nom' field");
    }

    // Validate divisions structure
    if (!is_array($data['divisions'])) {
      throw new \Exception("Divisions must be an array");
    }

    foreach ($data['divisions'] as $index => $division) {
      if (!isset($division['nom'])) {
        throw new \Exception("Division at index {$index} must have 'nom' field");
      }

      if (isset($division['services']) && !is_array($division['services'])) {
        throw new \Exception("Services in division '{$division['nom']}' must be an array");
      }
    }

    return TRUE;
  }

}