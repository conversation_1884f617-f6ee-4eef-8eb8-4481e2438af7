<?php

/**
 * @file
 * Install, update and uninstall functions for the org_json_importer module.
 */

use Dr<PERSON>al\taxonomy\Entity\Vocabulary;
use Drupal\field\Entity\FieldConfig;
use Drupal\field\Entity\FieldStorageConfig;

/**
 * Implements hook_install().
 */
function org_json_importer_install() {
  // Create a default organizational vocabulary if none exists
  $vocabulary_id = 'organizational_hierarchy';
  
  if (!Vocabulary::load($vocabulary_id)) {
    $vocabulary = Vocabulary::create([
      'vid' => $vocabulary_id,
      'name' => 'Organizational Hierarchy',
      'description' => 'Default vocabulary for organizational structure imported from JSON files',
    ]);
    $vocabulary->save();

    // Add description field if it doesn't exist
    _org_json_importer_add_description_field($vocabulary_id);

    \Drupal::messenger()->addStatus(t('Created default "Organizational Hierarchy" vocabulary.'));
  }
}

/**
 * Implements hook_uninstall().
 */
function org_json_importer_uninstall() {
  // Optionally clean up created vocabularies
  // Note: We don't automatically delete vocabularies as they may contain important data
  \Drupal::messenger()->addWarning(t('The org_json_importer module has been uninstalled. Any created vocabularies and taxonomy terms have been preserved.'));
}

/**
 * Helper function to add description field to taxonomy terms.
 *
 * @param string $vocabulary_id
 *   The vocabulary ID to add the field to.
 */
function _org_json_importer_add_description_field($vocabulary_id) {
  $field_name = 'field_org_description';
  
  // Check if field storage exists
  $field_storage = FieldStorageConfig::loadByName('taxonomy_term', $field_name);
  if (!$field_storage) {
    $field_storage = FieldStorageConfig::create([
      'field_name' => $field_name,
      'entity_type' => 'taxonomy_term',
      'type' => 'text_long',
      'cardinality' => 1,
    ]);
    $field_storage->save();
  }

  // Check if field instance exists for this vocabulary
  $field = FieldConfig::loadByName('taxonomy_term', $vocabulary_id, $field_name);
  if (!$field) {
    $field = FieldConfig::create([
      'field_storage' => $field_storage,
      'bundle' => $vocabulary_id,
      'label' => 'Description',
      'description' => 'Organizational unit description and responsibilities',
      'required' => FALSE,
    ]);
    $field->save();

    // Set form display
    $form_display = \Drupal::entityTypeManager()
      ->getStorage('entity_form_display')
      ->load('taxonomy_term.' . $vocabulary_id . '.default');
    
    if ($form_display) {
      $form_display->setComponent($field_name, [
        'type' => 'text_textarea',
        'weight' => 10,
        'settings' => [
          'rows' => 5,
        ],
      ]);
      $form_display->save();
    }

    // Set view display
    $view_display = \Drupal::entityTypeManager()
      ->getStorage('entity_view_display')
      ->load('taxonomy_term.' . $vocabulary_id . '.default');
    
    if ($view_display) {
      $view_display->setComponent($field_name, [
        'type' => 'text_default',
        'weight' => 10,
        'label' => 'above',
      ]);
      $view_display->save();
    }
  }
}