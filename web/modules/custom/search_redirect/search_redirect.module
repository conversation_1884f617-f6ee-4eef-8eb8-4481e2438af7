<?php

/**
 * @file
 * Module simple pour rediriger la recherche vers /recherche avec support multilingue
 */

use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Url;

/**
 * Implements hook_form_alter().
 */
function search_redirect_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Rediriger tous les formulaires de recherche
  if (strpos($form_id, 'search') !== FALSE) {
    // Obtenir la langue courante
    $current_language = \Drupal::languageManager()->getCurrentLanguage();
    $lang_code = $current_language->getId();

    // Créer l'URL avec la langue courante
    $url = Url::fromUserInput('/recherche', [
      'language' => $current_language
    ])->toString();

    $form['#action'] = $url;

    // Renommer le champ keys pour correspondre à la vue
    if (isset($form['keys'])) {
      $form['keys']['#name'] = 'search_api_fulltext';

      // Ajouter un placeholder selon la langue
      $placeholder = '';
      switch ($lang_code) {
        case 'ar':
          $placeholder = 'البحث في النص الكامل...';
          break;
        case 'en':
          $placeholder = 'Full text search...';
          break;
        case 'fr':
        default:
          $placeholder = 'Recherche en texte intégral...';
          break;
      }

      $form['keys']['#attributes']['placeholder'] = $placeholder;
    }
  }
}
