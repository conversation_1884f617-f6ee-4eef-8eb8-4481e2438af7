<?php

/**
 * Script de test pour vérifier la fonctionnalité de secteurs multiples
 * 
 * Usage: php test_secteurs_multiples.php
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

// Bootstrap Drupal
$autoloader = require_once 'autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Test de la fonctionnalité
function testSecteurMultiple() {
  echo "=== Test de la fonctionnalité secteurs multiples ===\n\n";
  
  // 1. Créer une actualité de test avec un secteur
  echo "1. Création d'une actualité de test...\n";
  
  $test_data = [
    'title' => 'Test Actualité Secteurs Multiples - ' . date('Y-m-d H:i:s'),
    'body' => 'Contenu de test pour vérifier les secteurs multiples.',
    'date' => date('d/m/Y'),
  ];
  
  // Simuler la création avec le secteur "transport_routier"
  $form = new \Drupal\scraper_news\Form\ScraperNewsForm(
    \Drupal::entityTypeManager(),
    \Drupal::service('file_system'),
    \Drupal::service('file.repository')
  );
  
  // Utiliser la réflexion pour accéder aux méthodes privées
  $reflection = new ReflectionClass($form);
  
  $createNewsNode = $reflection->getMethod('createNewsNode');
  $createNewsNode->setAccessible(true);
  
  $findExistingNewsNode = $reflection->getMethod('findExistingNewsNode');
  $findExistingNewsNode->setAccessible(true);
  
  $addSecteurToNode = $reflection->getMethod('addSecteurToNode');
  $addSecteurToNode->setAccessible(true);
  
  $getSecteurTermBySecteur = $reflection->getMethod('getSecteurTermBySecteur');
  $getSecteurTermBySecteur->setAccessible(true);
  
  // Créer le premier nœud avec secteur "transport_routier"
  echo "   - Création avec secteur 'transport_routier'\n";
  $node1 = $createNewsNode->invoke($form, $test_data, 'fr', 'transport_routier');
  
  if ($node1) {
    echo "   ✓ Nœud créé avec ID: " . $node1->id() . "\n";
    
    // Vérifier les secteurs actuels
    $secteurs = $node1->get('field_secteur')->getValue();
    echo "   - Secteurs actuels: " . count($secteurs) . "\n";
    
    // 2. Essayer de créer la même actualité avec un secteur différent
    echo "\n2. Ajout d'un secteur supplémentaire...\n";
    echo "   - Tentative d'ajout du secteur 'maritime'\n";
    
    $node2 = $createNewsNode->invoke($form, $test_data, 'fr', 'maritime');
    
    if ($node2 && $node2->id() == $node1->id()) {
      echo "   ✓ Même nœud retourné (ID: " . $node2->id() . ")\n";
      
      // Recharger le nœud pour voir les secteurs mis à jour
      $node_reloaded = \Drupal::entityTypeManager()->getStorage('node')->load($node1->id());
      $secteurs_updated = $node_reloaded->get('field_secteur')->getValue();
      echo "   - Secteurs après ajout: " . count($secteurs_updated) . "\n";
      
      if (count($secteurs_updated) > count($secteurs)) {
        echo "   ✓ Secteur ajouté avec succès!\n";
      } else {
        echo "   ⚠ Aucun secteur supplémentaire ajouté\n";
      }
      
      // Afficher les secteurs
      foreach ($secteurs_updated as $secteur) {
        $term = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->load($secteur['target_id']);
        if ($term) {
          echo "     - " . $term->label() . " (ID: " . $term->id() . ")\n";
        }
      }
    } else {
      echo "   ✗ Erreur: Un nouveau nœud a été créé au lieu d'ajouter le secteur\n";
    }
    
    // 3. Nettoyer - supprimer le nœud de test
    echo "\n3. Nettoyage...\n";
    $node1->delete();
    echo "   ✓ Nœud de test supprimé\n";
    
  } else {
    echo "   ✗ Erreur: Impossible de créer le nœud de test\n";
  }
  
  echo "\n=== Fin du test ===\n";
}

// Exécuter le test
try {
  testSecteurMultiple();
} catch (Exception $e) {
  echo "Erreur lors du test: " . $e->getMessage() . "\n";
  echo "Trace: " . $e->getTraceAsString() . "\n";
}
