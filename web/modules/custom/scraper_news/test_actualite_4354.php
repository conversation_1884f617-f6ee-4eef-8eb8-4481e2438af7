<?php

/**
 * Test d'import de l'actualité ID 4354 depuis deux secteurs différents
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

// Bootstrap Drupal
$autoloader = require_once __DIR__ . '/../../../../vendor/autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

echo "=== Test d'import de l'actualité ID 4354 ===\n\n";

// Créer une instance du formulaire
$form = new \Drupal\scraper_news\Form\ScraperNewsForm(
  \Drupal::entityTypeManager(),
  \Drupal::service('file_system'),
  \Drupal::service('file.repository')
);

// Utiliser la réflexion pour accéder aux méthodes privées
$reflection = new ReflectionClass($form);

$fetchHtml = $reflection->getMethod('fetchHtml');
$fetchHtml->setAccessible(true);

$parseHtml = $reflection->getMethod('parseHtml');
$parseHtml->setAccessible(true);

$createNewsNode = $reflection->getMethod('createNewsNode');
$createNewsNode->setAccessible(true);

$findExistingNewsNode = $reflection->getMethod('findExistingNewsNode');
$findExistingNewsNode->setAccessible(true);

// 1. Tester l'URL Transport routier
echo "1. Test depuis Transport routier...\n";
$url_transport = 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=4354';
$html_transport = $fetchHtml->invoke($form, $url_transport);

$node1 = null;
if ($html_transport) {
  echo "   ✓ HTML récupéré depuis Transport routier\n";
  $data_transport = $parseHtml->invoke($form, $html_transport, 'fr', 'transport_routier');
  
  if (!empty($data_transport) && !empty($data_transport[0]['title'])) {
    echo "   ✓ Données parsées: " . $data_transport[0]['title'] . "\n";
    echo "   - Date: " . ($data_transport[0]['date'] ?? 'N/A') . "\n";
    echo "   - Contenu: " . substr($data_transport[0]['body'], 0, 100) . "...\n";
    
    // Vérifier si l'actualité existe déjà
    $existing = $findExistingNewsNode->invoke($form, $data_transport[0]['title'], $data_transport[0]['date'], 'fr');
    if ($existing) {
      echo "   ⚠ Actualité existe déjà avec ID: " . $existing->id() . "\n";
      // Supprimer pour test propre
      $existing->delete();
      echo "   - Actualité existante supprimée pour test propre\n";
    }
    
    // Créer le nœud depuis transport routier
    $node1 = $createNewsNode->invoke($form, $data_transport[0], 'fr', 'transport_routier');
    if ($node1) {
      echo "   ✓ Nœud créé avec ID: " . $node1->id() . "\n";
      $secteurs1 = $node1->get('field_secteur')->getValue();
      echo "   - Nombre de secteurs: " . count($secteurs1) . "\n";
      
      // Afficher les secteurs
      foreach ($secteurs1 as $secteur) {
        $term = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->load($secteur['target_id']);
        if ($term) {
          echo "     * " . $term->label() . "\n";
        }
      }
    }
  } else {
    echo "   ✗ Impossible de parser les données\n";
  }
} else {
  echo "   ✗ Impossible de récupérer le HTML\n";
}

echo "\n2. Test depuis Marine marchande...\n";
$url_maritime = 'https://www.transport.gov.ma/maritime/Actualites/Pages/Actualites.aspx?IdNews=4354';
$html_maritime = $fetchHtml->invoke($form, $url_maritime);

if ($html_maritime) {
  echo "   ✓ HTML récupéré depuis Marine marchande\n";
  $data_maritime = $parseHtml->invoke($form, $html_maritime, 'fr', 'maritime');
  
  if (!empty($data_maritime) && !empty($data_maritime[0]['title'])) {
    echo "   ✓ Données parsées: " . $data_maritime[0]['title'] . "\n";
    echo "   - Date: " . ($data_maritime[0]['date'] ?? 'N/A') . "\n";
    
    // Créer/mettre à jour le nœud depuis marine marchande
    $node2 = $createNewsNode->invoke($form, $data_maritime[0], 'fr', 'maritime');
    if ($node2) {
      echo "   ✓ Nœud traité avec ID: " . $node2->id() . "\n";
      
      if ($node1 && $node2->id() == $node1->id()) {
        echo "   ✓ Même nœud utilisé - secteur ajouté!\n";
      } else {
        echo "   ⚠ Nouveau nœud créé (ID différent)\n";
      }
      
      // Recharger le nœud pour voir les secteurs finaux
      $node_final = \Drupal::entityTypeManager()->getStorage('node')->load($node2->id());
      $secteurs_final = $node_final->get('field_secteur')->getValue();
      echo "   - Nombre de secteurs final: " . count($secteurs_final) . "\n";
      
      // Afficher les secteurs
      foreach ($secteurs_final as $secteur) {
        $term = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->load($secteur['target_id']);
        if ($term) {
          echo "     * " . $term->label() . "\n";
        }
      }
      
      // Nettoyer - supprimer le nœud de test
      echo "\n3. Nettoyage...\n";
      $node_final->delete();
      echo "   ✓ Nœud de test supprimé\n";
    }
  } else {
    echo "   ✗ Impossible de parser les données\n";
  }
} else {
  echo "   ✗ Impossible de récupérer le HTML\n";
}

echo "\n=== Fin du test ===\n";
