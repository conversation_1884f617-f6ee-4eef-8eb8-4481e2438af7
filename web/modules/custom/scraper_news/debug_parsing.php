<?php

/**
 * Debug du parsing de l'actualité ID 4354
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

// Bootstrap Drupal
$autoloader = require_once __DIR__ . '/../../../../vendor/autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

echo "=== Debug du parsing de l'actualité ID 4354 ===\n\n";

// Créer une instance du formulaire
$form = new \Drupal\scraper_news\Form\ScraperNewsForm(
  \Drupal::entityTypeManager(),
  \Drupal::service('file_system'),
  \Drupal::service('file.repository')
);

// Utiliser la réflexion pour accéder aux méthodes privées
$reflection = new ReflectionClass($form);

$fetchHtml = $reflection->getMethod('fetchHtml');
$fetchHtml->setAccessible(true);

$parseHtml = $reflection->getMethod('parseHtml');
$parseHtml->setAccessible(true);

// Test depuis Transport routier
echo "1. Test du parsing depuis Transport routier...\n";
$url_transport = 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=4354';
$html_transport = $fetchHtml->invoke($form, $url_transport);

if ($html_transport) {
  echo "   ✓ HTML récupéré (" . strlen($html_transport) . " caractères)\n";
  
  $data_transport = $parseHtml->invoke($form, $html_transport, 'fr', 'transport_routier');
  
  if (!empty($data_transport)) {
    $item = $data_transport[0];
    echo "   ✓ Données parsées:\n";
    echo "     - Titre: '" . ($item['title'] ?? 'VIDE') . "'\n";
    echo "     - Date: '" . ($item['date'] ?? 'VIDE') . "'\n";
    echo "     - Body: '" . substr($item['body'] ?? 'VIDE', 0, 200) . "...'\n";
    echo "     - Image: '" . ($item['image'] ?? 'VIDE') . "'\n";
    
    // Vérifier si le body est vraiment vide
    if (empty($item['body'])) {
      echo "\n   ⚠ BODY VIDE - Analysons le HTML...\n";
      
      // Extraire le div spécifique pour transport routier
      $divId = 'ctl00_SPWebPartManager1_g_239181c7_58a5_4b48_a074_b30219232440_ctl00_detailPnl';
      $pattern = '/<div[^>]*id="' . preg_quote($divId, '/') . '"[^>]*>(.*?)<\/div>/s';
      
      if (preg_match($pattern, $html_transport, $matches)) {
        $content = $matches[1];
        echo "     - Contenu du div trouvé (" . strlen($content) . " caractères)\n";
        
        // Chercher les éléments de contenu
        if (preg_match('/<div[^>]*class="chapitre_pageinterne"[^>]*>(.*?)<\/div>/s', $content, $chapitreMatch)) {
          echo "     - Chapitre trouvé: " . substr(strip_tags($chapitreMatch[1]), 0, 100) . "...\n";
        } else {
          echo "     - Aucun chapitre trouvé\n";
        }
        
        if (preg_match('/<div[^>]*class="reste_contenu"[^>]*>(.*?)<\/div>/s', $content, $resteMatch)) {
          echo "     - Reste contenu trouvé: " . substr(strip_tags($resteMatch[1]), 0, 100) . "...\n";
        } else {
          echo "     - Aucun reste contenu trouvé\n";
        }
        
        // Afficher un échantillon du HTML pour debug
        echo "\n     Échantillon du HTML du div:\n";
        echo "     " . substr($content, 0, 500) . "...\n";
        
      } else {
        echo "     - Div avec ID '$divId' non trouvé\n";
        
        // Chercher tous les divs avec des IDs
        if (preg_match_all('/<div[^>]*id="([^"]*)"[^>]*>/i', $html_transport, $allDivs)) {
          echo "     - IDs de divs trouvés:\n";
          foreach (array_unique($allDivs[1]) as $id) {
            if (strpos($id, 'ctl00') === 0) {
              echo "       * $id\n";
            }
          }
        }
      }
    }
    
    // Vérifier l'image
    if (empty($item['image'])) {
      echo "\n   ⚠ IMAGE VIDE - Cherchons les images...\n";
      if (preg_match_all('/<img[^>]*src="([^"]*)"[^>]*>/i', $html_transport, $images)) {
        echo "     - Images trouvées:\n";
        foreach ($images[1] as $img) {
          echo "       * $img\n";
        }
      }
    }
    
  } else {
    echo "   ✗ Aucune donnée parsée\n";
  }
} else {
  echo "   ✗ Impossible de récupérer le HTML\n";
}

echo "\n=== Fin du debug ===\n";
