(function ($, <PERSON><PERSON><PERSON>, once) {
  'use strict';

  Drupal.behaviors.hierarchicalFiltersBehavior = {
    attach: function (context, settings) {
      
      // Use Drupal's once() function on the form itself
      once('hierarchicalFiltersBehavior', 'form[id*="views-exposed-form-reglementation"], form[id*="views-exposed-form-procedure-formulaire"]', context).forEach(function (form) {
        var $form = $(form);
        
        // Try to find secteur field (can be select or radio buttons)
        var secteurSelect = $form.find('select[name="field_secteur_target_id"]');
        var secteurRadios = $form.find('input[name="field_secteur_target_id"]');
        // Determine which view we are on
        var isReglementation = ($form.attr('id') || '').indexOf('views-exposed-form-reglementation') !== -1;
        var isProcedure = ($form.attr('id') || '').indexOf('views-exposed-form-procedure-formulaire') !== -1;
        // Domain fields per view
        var domaineCheckboxes = $form.find('input[name^="field_domaine_d_activite_target_id"]'); // reglementation
        var domaineSelectPF = $form.find('select[name="field_domaines_activite_pf_target_id"]'); // procedure_formulaire
        var domaineRadiosPF = $form.find('input[name="field_domaines_activite_pf_target_id"]'); // procedure_formulaire

        // Determine which secteur field type we're working with
        var usingSecteurSelect = secteurSelect.length > 0;
        var usingSecteurRadios = secteurRadios.length > 0;

        if (!usingSecteurSelect && !usingSecteurRadios) {
          return;
        }
        
        // Ensure the appropriate domaine field exists for the current view
        if (isReglementation && domaineCheckboxes.length === 0) {
          return;
        }
        if (isProcedure && (domaineSelectPF.length === 0 && domaineRadiosPF.length === 0)) {
          return;
        }

        // Initial setup - hide domaine container if no secteur is selected
        var initialSecteurValue = getCurrentSecteurValue();
        if (!initialSecteurValue || initialSecteurValue === 'All') {
          hideDomaineContainer();
        } else {
          loadDomainesBySecteur(initialSecteurValue);
        }
        
        // Event listeners for secteur changes
        if (usingSecteurSelect) {
          secteurSelect.on('change', handleSecteurChange);
        }
        
        if (usingSecteurRadios) {
          secteurRadios.on('change', handleSecteurChange);
        }
        
        // Function to get current secteur value
        function getCurrentSecteurValue() {
          if (usingSecteurSelect) {
            return secteurSelect.val();
          } else if (usingSecteurRadios) {
            return secteurRadios.filter(':checked').val();
          }
          return null;
        }
        
        // Function to handle secteur change
        function handleSecteurChange() {
          var secteurId = getCurrentSecteurValue();
          
          if (!secteurId || secteurId === 'All') {
            hideDomaineContainer();
          } else {
            loadDomainesBySecteur(secteurId);
          }
        }
        
        // Function to hide domaine container completely
        function hideDomaineContainer() {
          if (isReglementation) {
            domaineCheckboxes.prop('disabled', true);
            domaineCheckboxes.prop('checked', false);
            var domaineContainer = domaineCheckboxes.closest('.form-item, .form-checkboxes');
            if (domaineContainer.length) {
              domaineContainer.hide();
              var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
              if (domaineFieldset.length) {
                domaineFieldset.hide();
              }
              var messageId = 'hierarchical-filters-message';
              var existingMessage = domaineContainer.find('#' + messageId);
              if (existingMessage.length) {
                existingMessage.remove();
              }
            }
          } else if (isProcedure) {
            // For procedure: disable and clear select/radios and hide their container
            if (domaineSelectPF.length) {
              domaineSelectPF.prop('disabled', true);
              domaineSelectPF.val('');
              var selectContainer = domaineSelectPF.closest('.form-item, .form-select');
              selectContainer.hide();
              var fieldset = selectContainer.closest('fieldset, .fieldgroup');
              if (fieldset.length) {
                fieldset.hide();
              }
            }
            if (domaineRadiosPF.length) {
              domaineRadiosPF.prop('disabled', true);
              domaineRadiosPF.prop('checked', false);
              var radiosContainer = domaineRadiosPF.closest('.form-item, .form-radios');
              radiosContainer.hide();
              var fieldset2 = radiosContainer.closest('fieldset, .fieldgroup');
              if (fieldset2.length) {
                fieldset2.hide();
              }
            }
          }
        }

        // Function to load domaines by secteur
        function loadDomainesBySecteur(secteurId) {
          var viewType = isProcedure ? 'procedure_formulaire' : 'reglementation';

          // Save current domaine selections
          var selectedDomaines = [];
          if (isReglementation) {
            domaineCheckboxes.filter(':checked').each(function() {
              var match = this.name.match(/\[(\d+)\]/);
              if (match) {
                selectedDomaines.push(match[1]);
              }
            });
          } else if (isProcedure) {
            if (domaineSelectPF.length) {
              var val = domaineSelectPF.val();
              if (val) { selectedDomaines.push(val); }
            }
            if (domaineRadiosPF.length) {
              var v = domaineRadiosPF.filter(':checked').val();
              if (v) { selectedDomaines.push(v); }
            }
          }

          // Prepare UI: hide/disable all domain inputs
          var domaineContainer = $();
          if (isReglementation) {
            domaineCheckboxes.each(function() {
              $(this).closest('label, .form-item').hide();
            });
            domaineCheckboxes.prop('disabled', true);
            domaineContainer = domaineCheckboxes.closest('.form-item, .form-checkboxes');
          } else if (isProcedure) {
            if (domaineSelectPF.length) {
              // Remove all options except the empty one for now
              var emptyOption = domaineSelectPF.find('option[value=""]').first();
              domaineSelectPF.find('option').not(emptyOption).remove();
              domaineSelectPF.prop('disabled', true);
              var selectContainer = domaineSelectPF.closest('.form-item, .form-select');
              selectContainer.show();
              domaineContainer = selectContainer;
            }
            if (domaineRadiosPF.length) {
              domaineRadiosPF.each(function() {
                $(this).closest('label, .form-item').hide();
              });
              domaineRadiosPF.prop('disabled', true);
              var radiosContainer = domaineRadiosPF.closest('.form-item, .form-radios');
              radiosContainer.show();
              if (!domaineContainer.length) { domaineContainer = radiosContainer; }
            }
          }

          if (domaineContainer.length) {
            domaineContainer.addClass('hierarchical-filters-loading');
            var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
            if (domaineFieldset.length) {
              domaineFieldset.show();
            }
            var messageId = 'hierarchical-filters-message';
            var existingMessage = domaineContainer.find('#' + messageId);
            if (existingMessage.length) {
              existingMessage.remove();
            }
          }

          $.ajax({
            url: Drupal.url('domaines-by-secteur/' + secteurId) + '?view_type=' + encodeURIComponent(viewType),
            type: 'GET',
            dataType: 'json',
            timeout: 10000,
            success: function (response) {
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading hierarchical-filters-disabled');
              }
              var availableDomaines = [];
              if (response && response.length > 0) {
                if (isReglementation) {
                  response.forEach(function(domaine) {
                    availableDomaines.push(String(domaine.id));
                    var checkbox = domaineCheckboxes.filter('[name*="[' + domaine.id + ']"]');
                    if (checkbox.length) {
                      checkbox.prop('disabled', false);
                      checkbox.closest('label, .form-item').show();
                    }
                  });
                } else if (isProcedure) {
                  response.forEach(function(domaine) {
                    availableDomaines.push(String(domaine.id));
                    if (domaineSelectPF.length) {
                      // Append option
                      domaineSelectPF.append($('<option></option>').attr('value', domaine.id).text(domaine.name));
                      domaineSelectPF.prop('disabled', false);
                      domaineSelectPF.closest('.form-item, .form-select').show();
                    }
                    if (domaineRadiosPF.length) {
                      var radio = domaineRadiosPF.filter('[value="' + domaine.id + '"]');
                      if (radio.length) {
                        radio.prop('disabled', false);
                        radio.closest('label, .form-item').show();
                      }
                      radio.closest('.form-item, .form-radios').show();
                    }
                  });
                }
              }
              // Restore previous selection if still valid
              if (selectedDomaines.length) {
                if (isReglementation) {
                  selectedDomaines.forEach(function(domaineId) {
                    if (availableDomaines.includes(String(domaineId))) {
                      var checkbox = domaineCheckboxes.filter('[name*="[' + domaineId + ']"]');
                      if (checkbox.length) {
                        checkbox.prop('checked', true);
                      }
                    }
                  });
                } else if (isProcedure) {
                  var prev = selectedDomaines[0];
                  if (availableDomaines.includes(String(prev))) {
                    if (domaineSelectPF.length) {
                      domaineSelectPF.val(String(prev));
                    }
                    if (domaineRadiosPF.length) {
                      var radioPrev = domaineRadiosPF.filter('[value="' + prev + '"]');
                      if (radioPrev.length) {
                        radioPrev.prop('checked', true);
                      }
                    }
                  }
                }
              }
            },
            error: function () {
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading');
              }
              // Keep inputs hidden/disabled on error
            }
          });
        }
      });
    }
  };
})(jQuery, Drupal, once);