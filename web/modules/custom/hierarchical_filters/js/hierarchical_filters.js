(function ($, <PERSON><PERSON><PERSON>, once) {
  'use strict';

  Drupal.behaviors.hierarchicalFiltersBehavior = {
    attach: function (context, settings) {
      
      // Use Drupal's once() function on the form itself
      once('hierarchicalFiltersBehavior', 'form[id*="views-exposed-form-reglementation"], form[id*="views-exposed-form-procedure-formulaire"]', context).forEach(function (form) {
        var $form = $(form);
        
        // Try to find secteur field (can be select or radio buttons)
        var secteurSelect = $form.find('select[name="field_secteur_target_id"]');
        var secteurRadios = $form.find('input[name="field_secteur_target_id"]');
        // Determine which view we are on
        var isReglementation = ($form.attr('id') || '').indexOf('views-exposed-form-reglementation') !== -1;
        var isProcedure = ($form.attr('id') || '').indexOf('views-exposed-form-procedure-formulaire') !== -1;
        // Domain fields per view
        var domaineCheckboxes = $form.find('input[name^="field_domaine_d_activite_target_id"]'); // reglementation
        var domaineSelectPF = $form.find('select[name="field_domaines_activite_pf_target_id"]'); // procedure_formulaire
        var domaineRadiosPF = $form.find('input[name="field_domaines_activite_pf_target_id"]'); // procedure_formulaire
        var domaineLinksPF  = $form.find('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"] .bef-links a'); // BEF links

        // Determine which secteur field type we're working with
        var usingSecteurSelect = secteurSelect.length > 0;
        var usingSecteurRadios = secteurRadios.length > 0;

        if (!usingSecteurSelect && !usingSecteurRadios) {
          return;
        }
        
        // Ensure the appropriate domaine field exists for the current view
        if (isReglementation && domaineCheckboxes.length === 0) {
          return;
        }
        if (isProcedure && (domaineSelectPF.length === 0 && domaineRadiosPF.length === 0 && domaineLinksPF.length === 0)) {
          // For procedure view, support BEF "links"; only return if none of the widgets exist
          return;
        }
        // Locate stable wrappers using data-drupal-selector and fix ordering if needed
        var secteurWrapper = $form.find('[data-drupal-selector="edit-field-secteur-target-id-wrapper"]');
        var domaineWrapperReg = $form.find('[data-drupal-selector="edit-field-domaine-d-activite-target-id-wrapper"]');
        var domaineWrapperPF  = $form.find('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"]');

        // Ensure Domaine wrapper is placed after Secteur for better UX
        if (secteurWrapper.length) {
          if (isReglementation && domaineWrapperReg.length) {
            domaineWrapperReg.insertAfter(secteurWrapper);
          }
          if (isProcedure && domaineWrapperPF.length) {
            domaineWrapperPF.insertAfter(secteurWrapper);
          }
        }

        // Initial setup - hide domaine container if no secteur is selected
        var initialSecteurValue = getCurrentSecteurValue();
        if (!initialSecteurValue || initialSecteurValue === 'All') {
          hideDomaineContainer();
        } else {
          loadDomainesBySecteur(initialSecteurValue);
        }
        
        // Event listeners for secteur changes
        if (usingSecteurSelect) {
          secteurSelect.on('change', handleSecteurChange);
        }
        
        if (usingSecteurRadios) {
          secteurRadios.on('change', handleSecteurChange);
        }
        
        // Function to get current secteur value
        function getCurrentSecteurValue() {
          if (usingSecteurSelect) {
            return secteurSelect.val();
          } else if (usingSecteurRadios) {
            return secteurRadios.filter(':checked').val();
          }
          return null;
        }
        
        // Function to handle secteur change
        function handleSecteurChange() {
          var secteurId = getCurrentSecteurValue();
          
          if (!secteurId || secteurId === 'All') {
            hideDomaineContainer();
          } else {
            loadDomainesBySecteur(secteurId);
          }
        }
        
        // Function to hide domaine container completely
        function hideDomaineContainer() {
          if (isReglementation) {
            domaineCheckboxes.prop('disabled', true);
            domaineCheckboxes.prop('checked', false);
            var domaineContainer = domaineCheckboxes.closest('.form-item, .form-checkboxes');
            if (domaineContainer.length) {
              domaineContainer.hide();
              var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
              if (domaineFieldset.length) {
                domaineFieldset.hide();
              }
              var messageId = 'hierarchical-filters-message';
              var existingMessage = domaineContainer.find('#' + messageId);
              if (existingMessage.length) {
                existingMessage.remove();
              }
            }
          } else if (isProcedure) {
            // For procedure: disable and clear select/radios and hide their wrapper
            var domaineWrapperPF  = $form.find('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"]');
            if (domaineSelectPF.length) {
              domaineSelectPF.prop('disabled', true).val('');
            }
            if (domaineRadiosPF.length) {
              domaineRadiosPF.prop('disabled', true).prop('checked', false);
            }
            if (domaineWrapperPF.length) {
              domaineWrapperPF.hide();
            }
          }
        }

        // Function to load domaines by secteur
        function loadDomainesBySecteur(secteurId) {
          var viewType = isProcedure ? 'procedure_formulaire' : 'reglementation';

          // Save current domaine selections
          var selectedDomaines = [];
          if (isReglementation) {
            domaineCheckboxes.filter(':checked').each(function() {
              var match = this.name.match(/\[(\d+)\]/);
              if (match) {
                selectedDomaines.push(match[1]);
              }
            });
          } else if (isProcedure) {
            if (domaineSelectPF.length) {
              var val = domaineSelectPF.val();
              if (val) { selectedDomaines.push(val); }
            }
            if (domaineRadiosPF.length) {
              var v = domaineRadiosPF.filter(':checked').val();
              if (v) { selectedDomaines.push(v); }
            }
            // BEF links: capture active selections
            var wrapperPFsel  = $form.find('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"]');
            if (wrapperPFsel.length) {
              wrapperPFsel.find('.bef-links a.is-active, .bef-links a.active, .bef-links a[aria-pressed="true"]').each(function() {
                var valAttr = $(this).attr('data-bef-value') || $(this).data('bef-value') || $(this).attr('data-value') || $(this).data('value');
                if (valAttr) { selectedDomaines.push(String(valAttr)); }
              });
            }
          }

          // Prepare UI: hide/disable all domain inputs
          var domaineContainer = $();
          if (isReglementation) {
            domaineCheckboxes.each(function() {
              $(this).closest('label, .form-item').hide();
            });
            domaineCheckboxes.prop('disabled', true);
            // Prefer stable wrapper when available
            var wrapperReg = $form.find('[data-drupal-selector="edit-field-domaine-d-activite-target-id-wrapper"]');
            domaineContainer = wrapperReg.length ? wrapperReg : domaineCheckboxes.closest('.form-item, .form-checkboxes');
            if (domaineContainer.length) { domaineContainer.show(); }
          } else if (isProcedure) {
            var wrapperPF  = $form.find('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"]');
            // Hide all UI elements inside wrapper (works with BEF links as well)
            if (wrapperPF.length) {
              wrapperPF.find('.bef-links a, a[data-bef-value], label, .form-item, .form-radios, .form-select option').hide();
            }
            if (domaineSelectPF.length) {
              // Remove all options except the empty one for now
              var emptyOption = domaineSelectPF.find('option[value=""]').first();
              domaineSelectPF.find('option').not(emptyOption).remove();
              domaineSelectPF.prop('disabled', true);
            }
            if (domaineRadiosPF.length) {
              domaineRadiosPF.each(function() {
                $(this).closest('label, .form-item').hide();
              });
              domaineRadiosPF.prop('disabled', true);
            }
            domaineContainer = wrapperPF.length ? wrapperPF : (domaineSelectPF.length ? domaineSelectPF.closest('.form-item, .form-select') : domaineRadiosPF.closest('.form-item, .form-radios'));
            if (domaineContainer && domaineContainer.length) { domaineContainer.show(); }
          }

          if (domaineContainer.length) {
            domaineContainer.addClass('hierarchical-filters-loading');
            var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
            if (domaineFieldset.length) {
              domaineFieldset.show();
            }
            var messageId = 'hierarchical-filters-message';
            var existingMessage = domaineContainer.find('#' + messageId);
            if (existingMessage.length) {
              existingMessage.remove();
            }
          }

          $.ajax({
            url: Drupal.url('domaines-by-secteur/' + secteurId) + '?view_type=' + encodeURIComponent(viewType),
            type: 'GET',
            dataType: 'json',
            timeout: 10000,
            success: function (response) {
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading hierarchical-filters-disabled');
              }
              var availableDomaines = [];
              if (response && response.length > 0) {
                if (isReglementation) {
                  response.forEach(function(domaine) {
                    availableDomaines.push(String(domaine.id));
                    var checkbox = domaineCheckboxes.filter('[name*="[' + domaine.id + ']"]');
                    if (checkbox.length) {
                      checkbox.prop('disabled', false);
                      checkbox.closest('label, .form-item').show();
                    }
                  });
                } else if (isProcedure) {
                  var wrapperPF  = $form.find('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"]');
                  // First, hide everything inside the wrapper
                  if (wrapperPF.length) {
                    wrapperPF.find('.bef-links a, a[data-bef-value], label, .form-item').hide();
                  }
                  response.forEach(function(domaine) {
                    var idStr = String(domaine.id);
                    availableDomaines.push(idStr);
                    if (domaineSelectPF.length) {
                      // Append option
                      domaineSelectPF.append($('<option></option>').attr('value', idStr).text(domaine.name));
                      domaineSelectPF.prop('disabled', false);
                    }
                    if (domaineRadiosPF.length) {
                      var radio = domaineRadiosPF.filter('[value="' + idStr + '"]');
                      if (radio.length) {
                        radio.prop('disabled', false);
                        radio.closest('label, .form-item').show();
                      }
                    }
                    // Show matching BEF link if present
                    if (wrapperPF.length) {
                      wrapperPF.find('.bef-links a[data-bef-value="' + idStr + '"], a[data-bef-value="' + idStr + '"]').show();
                    }
                  });
                  // For BEF links, ensure that only allowed links are visible and active states are sane
                  if (wrapperPF.length) {
                    wrapperPF.find('.bef-links a').each(function() {
                      var $a = $(this);
                      var v = $a.attr('data-bef-value') || $a.data('bef-value') || $a.attr('data-value') || $a.data('value');
                      v = v != null ? String(v) : '';
                      var shouldShow = false;
                      if (v && availableDomaines.indexOf(v) !== -1) {
                        shouldShow = true;
                      } else {
                        // Fallback: detect by href patterns (BEF encodes query params)
                        var href = $a.attr('href') || '';
                        if (href.indexOf('field_domaines_activite_pf_target_id') !== -1) {
                          shouldShow = availableDomaines.some(function(id) {
                            id = String(id);
                            return href.indexOf('field_domaines_activite_pf_target_id%5B' + id + '%5D=') !== -1 ||
                                   href.indexOf('field_domaines_activite_pf_target_id%5B%5D=' + id) !== -1 ||
                                   href.indexOf('field_domaines_activite_pf_target_id=' + id) !== -1;
                          });
                        }
                      }
                      if (!shouldShow) {
                        $a.removeClass('is-active active').attr('aria-pressed', 'false').hide();
                      } else {
                        $a.show();
                      }
                    });
                  }
                  if (domaineSelectPF.length) {
                    domaineSelectPF.closest('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"]').show();
                  }
                  if (domaineRadiosPF.length) {
                    domaineRadiosPF.closest('[data-drupal-selector="edit-field-domaines-activite-pf-target-id-wrapper"]').show();
                  }
                }
              }
              // Restore previous selection if still valid
              if (selectedDomaines.length) {
                if (isReglementation) {
                  selectedDomaines.forEach(function(domaineId) {
                    if (availableDomaines.includes(String(domaineId))) {
                      var checkbox = domaineCheckboxes.filter('[name*="[' + domaineId + ']"]');
                      if (checkbox.length) {
                        checkbox.prop('checked', true);
                      }
                    }
                  });
                } else if (isProcedure) {
                  var prev = selectedDomaines[0];
                  if (availableDomaines.includes(String(prev))) {
                    if (domaineSelectPF.length) {
                      domaineSelectPF.val(String(prev));
                    }
                    if (domaineRadiosPF.length) {
                      var radioPrev = domaineRadiosPF.filter('[value="' + prev + '"]');
                      if (radioPrev.length) {
                        radioPrev.prop('checked', true);
                      }
                    }
                  }
                }
              }
            },
            error: function () {
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading');
              }
              // Keep inputs hidden/disabled on error
            }
          });
        }
      });
    }
  };
})(jQuery, Drupal, once);