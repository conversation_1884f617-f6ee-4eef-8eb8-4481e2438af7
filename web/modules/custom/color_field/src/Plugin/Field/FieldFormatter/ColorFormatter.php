<?php

namespace Drupal\color_field\Plugin\Field\FieldFormatter;

use <PERSON><PERSON>al\Core\Field\Attribute\FieldFormatter;
use <PERSON>upal\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\FormatterBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON>upal\Core\StringTranslation\TranslatableMarkup;

/**
 * Defines the 'color_formatter' field formatter.
 */
#[FieldFormatter(
  id: "color_formatter",
  label: new TranslatableMarkup("Color swatch"),
  field_types: ["color"]
)]
class ColorFormatter extends FormatterBase {

  /**
   * {@inheritdoc}
   */
  public static function defaultSettings() {
    return [
      'show_text' => TRUE,
      'swatch_size' => '20px',
    ] + parent::defaultSettings();
  }

  /**
   * {@inheritdoc}
   */
  public function settingsForm(array $form, FormStateInterface $form_state) {
    $elements = parent::settingsForm($form, $form_state);

    $elements['show_text'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Show color value text'),
      '#default_value' => $this->getSetting('show_text'),
    ];

    $elements['swatch_size'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Swatch size'),
      '#default_value' => $this->getSetting('swatch_size'),
      '#description' => $this->t('Size of the color swatch (e.g., 20px, 1em, etc.)'),
    ];

    return $elements;
  }

  /**
   * {@inheritdoc}
   */
  public function settingsSummary() {
    $summary = [];
    $summary[] = $this->t('Show text: @show', ['@show' => $this->getSetting('show_text') ? 'Yes' : 'No']);
    $summary[] = $this->t('Swatch size: @size', ['@size' => $this->getSetting('swatch_size')]);
    return $summary;
  }

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    $elements = [];
    $show_text = $this->getSetting('show_text');
    $swatch_size = $this->getSetting('swatch_size');

    foreach ($items as $delta => $item) {
      $color = $item->value;
      
      if ($color) {
        $elements[$delta] = [
          '#markup' => sprintf(
            '<span class="color-field-swatch" style="display: inline-block; width: %s; height: %s; background-color: %s; border: 1px solid #ccc; margin-right: 5px; vertical-align: middle;"></span>%s',
            $swatch_size,
            $swatch_size,
            $color,
            $show_text ? $color : ''
          ),
        ];
      }
    }

    return $elements;
  }

}