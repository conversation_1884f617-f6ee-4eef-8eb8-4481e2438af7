<?php

namespace Drupal\color_field\Plugin\Field\FieldWidget;

use Drupal\Core\Field\Attribute\FieldWidget;
use Drupal\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\WidgetBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\StringTranslation\TranslatableMarkup;

/**
 * Defines the 'color_widget' field widget.
 */
#[FieldWidget(
  id: "color_widget",
  label: new TranslatableMarkup("Color picker"),
  field_types: ["color"]
)]
class ColorWidget extends WidgetBase {

  /**
   * {@inheritdoc}
   */
  public function formElement(FieldItemListInterface $items, $delta, array $element, array &$form, FormStateInterface $form_state) {
    $value = isset($items[$delta]->value) ? $items[$delta]->value : '#000000';

    $element['value'] = $element + [
      '#type' => 'color',
      '#default_value' => $value,
      '#size' => 7,
      '#maxlength' => 7,
    ];

    return $element;
  }

}